✨ Nuxt DevTools  Press Shift + Option + D to open DevTools 
[Vue warn] Set operation on key "value" failed: target is readonly. RefImpl
[Vue warn] Set operation on key "value" failed: target is readonly. RefImpl
<Suspense> is an experimental feature and its API will likely change.
[Vue warn]: Hydration class mismatch on <div class="system">…</div>
  - rendered on server: class="system"
  - expected on client: class="light"
  Note: this mismatch is check-only. The DOM will not be rectified in production due to performance overhead.
  You should fix the source of the mismatch.
  at <App key=4 >
  at <NuxtRoot>
Hydration completed but contains mismatches.
[Vue warn]: Failed to resolve component: Icon
If this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.
  at <Main ref=Ref< undefined > >
  at <AsyncComponentWrapper ref=Ref< undefined > >
  at <LayoutLoader key="main" layoutProps= Object name="main" >
  at <NuxtLayoutProvider layoutProps= Object key="main" name="main"  ... >
  at <NuxtLayout >
  at <App key=4 >
  at <NuxtRoot>
[Vue warn]: Hydration node mismatch:
- rendered on server: <!---->
- expected on client: Icon
  at <Main ref=Ref< undefined > >
  at <AsyncComponentWrapper ref=Ref< undefined > >
  at <LayoutLoader key="main" layoutProps= Object name="main" >
  at <NuxtLayoutProvider layoutProps= Object key="main" name="main"  ... >
  at <NuxtLayout >
  at <App key=4 >
  at <NuxtRoot>
[Vue warn]: Hydration text content mismatch on <span>​D​</span>​
  - rendered on server: U
  - expected on client: D
  at <Main ref=Ref< undefined > >
  at <AsyncComponentWrapper ref=Ref< undefined > >
  at <LayoutLoader key="main" layoutProps= Object name="main" >
  at <NuxtLayoutProvider layoutProps= Object key="main" name="main"  ... >
  at <NuxtLayout >
  at <App key=4 >
  at <NuxtRoot>
[Vue warn]: Hydration node mismatch:
- rendered on server: <!---->
- expected on client: span
  at <Main ref=Ref< undefined > >
  at <AsyncComponentWrapper ref=Ref< undefined > >
  at <LayoutLoader key="main" layoutProps= Object name="main" >
  at <NuxtLayoutProvider layoutProps= Object key="main" name="main"  ... >
  at <NuxtLayout >
  at <App key=4 >
  at <NuxtRoot>
[Vue Router warn]: No match found for location with path "/daily-recommend"
[Vue Router warn]: No match found for location with path "/charts"
[Vue Router warn]: No match found for location with path "/social/friends"
[Vue Router warn]: No match found for location with path "/social/feed"
[Vue warn] Set operation on key "value" failed: target is readonly. RefImpl
[Vue warn]: Failed to resolve component: Icon
If this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.
  at <Index onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > >
  at <RouteProvider key="/" vnode= Object route= Object  ... >
  at <RouterView name=undefined route=undefined >
  at <NuxtPage >
  at <Main ref=Ref< undefined > >
  at <AsyncComponentWrapper ref=Ref< undefined > >
  at <LayoutLoader key="main" layoutProps= Object name="main" >
  at <NuxtLayoutProvider layoutProps= Object key="main" name="main"  ... >
  at <NuxtLayout >
  at <App key=4 >
  at <NuxtRoot>
[Vue warn]: Failed to resolve component: Button
If this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.
  at <Index onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > >
  at <RouteProvider key="/" vnode= Object route= Object  ... >
  at <RouterView name=undefined route=undefined >
  at <NuxtPage >
  at <Main ref=Ref< undefined > >
  at <AsyncComponentWrapper ref=Ref< undefined > >
  at <LayoutLoader key="main" layoutProps= Object name="main" >
  at <NuxtLayoutProvider layoutProps= Object key="main" name="main"  ... >
  at <NuxtLayout >
  at <App key=4 >
  at <NuxtRoot>
[Vue warn]: Failed to resolve component: Card
If this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.
  at <Index onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > >
  at <RouteProvider key="/" vnode= Object route= Object  ... >
  at <RouterView name=undefined route=undefined >
  at <NuxtPage >
  at <Main ref=Ref< undefined > >
  at <AsyncComponentWrapper ref=Ref< undefined > >
  at <LayoutLoader key="main" layoutProps= Object name="main" >
  at <NuxtLayoutProvider layoutProps= Object key="main" name="main"  ... >
  at <NuxtLayout >
  at <App key=4 >
  at <NuxtRoot>
[Vue warn]: Hydration node mismatch:
- rendered on server: <!---->
- expected on client: Button
  at <Index onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > >
  at <RouteProvider key="/" vnode= Object route= Object  ... >
  at <RouterView name=undefined route=undefined >
  at <NuxtPage >
  at <Main ref=Ref< undefined > >
  at <AsyncComponentWrapper ref=Ref< undefined > >
  at <LayoutLoader key="main" layoutProps= Object name="main" >
  at <NuxtLayoutProvider layoutProps= Object key="main" name="main"  ... >
  at <NuxtLayout >
  at <App key=4 >
  at <NuxtRoot>
[Vue warn]: Hydration node mismatch:
- rendered on server: <!---->
- expected on client: Button
  at <Index onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > >
  at <RouteProvider key="/" vnode= Object route= Object  ... >
  at <RouterView name=undefined route=undefined >
  at <NuxtPage >
  at <Main ref=Ref< undefined > >
  at <AsyncComponentWrapper ref=Ref< undefined > >
  at <LayoutLoader key="main" layoutProps= Object name="main" >
  at <NuxtLayoutProvider layoutProps= Object key="main" name="main"  ... >
  at <NuxtLayout >
  at <App key=4 >
  at <NuxtRoot>
[Vue warn]: Hydration node mismatch:
- rendered on server: <!---->
- expected on client: Card
  at <Index onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > >
  at <RouteProvider key="/" vnode= Object route= Object  ... >
  at <RouterView name=undefined route=undefined >
  at <NuxtPage >
  at <Main ref=Ref< undefined > >
  at <AsyncComponentWrapper ref=Ref< undefined > >
  at <LayoutLoader key="main" layoutProps= Object name="main" >
  at <NuxtLayoutProvider layoutProps= Object key="main" name="main"  ... >
  at <NuxtLayout >
  at <App key=4 >
  at <NuxtRoot>
[Vue warn]: Hydration node mismatch:
- rendered on server: <!---->
- expected on client: Card
  at <Index onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > >
  at <RouteProvider key="/" vnode= Object route= Object  ... >
  at <RouterView name=undefined route=undefined >
  at <NuxtPage >
  at <Main ref=Ref< undefined > >
  at <AsyncComponentWrapper ref=Ref< undefined > >
  at <LayoutLoader key="main" layoutProps= Object name="main" >
  at <NuxtLayoutProvider layoutProps= Object key="main" name="main"  ... >
  at <NuxtLayout >
  at <App key=4 >
  at <NuxtRoot>
[Vue warn]: Hydration node mismatch:
- rendered on server: <!---->
- expected on client: Card
  at <Index onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > >
  at <RouteProvider key="/" vnode= Object route= Object  ... >
  at <RouterView name=undefined route=undefined >
  at <NuxtPage >
  at <Main ref=Ref< undefined > >
  at <AsyncComponentWrapper ref=Ref< undefined > >
  at <LayoutLoader key="main" layoutProps= Object name="main" >
  at <NuxtLayoutProvider layoutProps= Object key="main" name="main"  ... >
  at <NuxtLayout >
  at <App key=4 >
  at <NuxtRoot>
[Vue warn]: Hydration node mismatch:
- rendered on server: <!---->
- expected on client: Card
  at <Index onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > >
  at <RouteProvider key="/" vnode= Object route= Object  ... >
  at <RouterView name=undefined route=undefined >
  at <NuxtPage >
  at <Main ref=Ref< undefined > >
  at <AsyncComponentWrapper ref=Ref< undefined > >
  at <LayoutLoader key="main" layoutProps= Object name="main" >
  at <NuxtLayoutProvider layoutProps= Object key="main" name="main"  ... >
  at <NuxtLayout >
  at <App key=4 >
  at <NuxtRoot>
[Vue warn]: Hydration node mismatch:
- rendered on server: <!---->
- expected on client: Icon
  at <Main ref=Ref< undefined > >
  at <AsyncComponentWrapper ref=Ref< undefined > >
  at <LayoutLoader key="main" layoutProps= Object name="main" >
  at <NuxtLayoutProvider layoutProps= Object key="main" name="main"  ... >
  at <NuxtLayout >
  at <App key=4 >
  at <NuxtRoot>
[Vue warn]: Hydration node mismatch:
- rendered on server: <!---->
- expected on client: Icon
  at <Main ref=Ref< undefined > >
  at <AsyncComponentWrapper ref=Ref< undefined > >
  at <LayoutLoader key="main" layoutProps= Object name="main" >
  at <NuxtLayoutProvider layoutProps= Object key="main" name="main"  ... >
  at <NuxtLayout >
  at <App key=4 >
  at <NuxtRoot>
main布局 onMounted 执行了！
当前时间: 10:46:42
用户认证状态初始化完成: Proxy(Object)
Failed to load resource: the server responded with a status of 500 (Internal Server Error) :3000/api/v1/music/recent-plays?limit=6
Failed to load resource: the server responded with a status of 400 (Bad Request) :3000/api/v1/playlists/recommended?limit=8
API请求错误: FetchError: [GET] "http://localhost:3000/api/v1/playlists/recommended?limit=8": 400 Bad Request
[Vue warn]: Unhandled error during execution of mounted hook
  at <Index onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< Proxy(Object) > >
  at <RouteProvider key="/" vnode= Object route= Object  ... >
  at <RouterView name=undefined route=undefined >
  at <NuxtPage >
  at <Main ref=Ref< Proxy(Object) > >
  at <AsyncComponentWrapper ref=Ref< Proxy(Object) > >
  at <LayoutLoader key="main" layoutProps= Object name="main" >
  at <NuxtLayoutProvider layoutProps= Object key="main" name="main"  ... >
  at <NuxtLayout >
  at <App key=4 >
  at <NuxtRoot>
Uncaught (in promise) TypeError: handleError is not a function
Failed to load resource: the server responded with a status of 500 (Internal Server Error) :3000/api/v1/music/recent-plays?limit=6
API请求错误: FetchError: [GET] "http://localhost:3000/api/v1/music/recent-plays?limit=6": 500 Internal Server Error
[Vue Router warn]: No match found for location with path "/daily-recommend"
[Vue Router warn]: No match found for location with path "/charts"
[Vue Router warn]: No match found for location with path "/social/friends"
[Vue Router warn]: No match found for location with path "/social/feed"
[Vue Router warn]: No match found for location with path "/daily-recommend"
[Vue Router warn]: No match found for location with path "/charts"
[Vue Router warn]: No match found for location with path "/social/friends"
[Vue Router warn]: No match found for location with path "/social/feed"