{"openapi": "3.0.3", "info": {"title": "MusicDou 播放功能模块 API", "description": "MusicDou音乐平台播放功能相关API，包括播放控制、播放历史、播放队列等功能", "version": "1.0.0", "contact": {"name": "MusicDou API Support", "url": "http://localhost:3000"}}, "servers": [{"url": "http://localhost:3000/api/v1", "description": "开发环境服务器"}], "tags": [{"name": "播放控制", "description": "音乐播放控制功能"}, {"name": "播放队列", "description": "播放队列管理"}, {"name": "播放历史", "description": "播放历史记录"}], "paths": {"/play/start": {"post": {"tags": ["播放控制"], "summary": "开始播放", "description": "开始播放指定音乐", "operationId": "startPlay", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["musicId"], "properties": {"musicId": {"type": "string", "description": "音乐ID"}, "quality": {"type": "string", "enum": ["standard", "high", "super", "lossless"], "description": "播放质量"}, "position": {"type": "number", "minimum": 0, "description": "播放位置（秒）"}}}}}}, "responses": {"200": {"description": "播放开始成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Playback started successfully"}, "data": {"$ref": "#/components/schemas/PlaySession"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/play/pause": {"post": {"tags": ["播放控制"], "summary": "暂停播放", "description": "暂停当前播放", "operationId": "pausePlay", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "暂停成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Playback paused successfully"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/play/status": {"get": {"tags": ["播放控制"], "summary": "获取播放状态", "description": "获取当前播放状态信息", "operationId": "getPlayStatus", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Play status retrieved successfully"}, "data": {"$ref": "#/components/schemas/PlayStatus"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/queue": {"get": {"tags": ["播放队列"], "summary": "获取播放队列", "description": "获取当前用户的播放队列", "operationId": "getPlayQueue", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Play queue retrieved successfully"}, "data": {"$ref": "#/components/schemas/PlayQueue"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["播放队列"], "summary": "添加到播放队列", "description": "添加音乐到播放队列", "operationId": "addToQueue", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["musicId"], "properties": {"musicId": {"type": "string", "description": "音乐ID"}, "position": {"type": "integer", "description": "插入位置（可选）"}}}}}}, "responses": {"201": {"description": "添加成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Music added to queue successfully"}, "data": {"$ref": "#/components/schemas/PlayQueue"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/history": {"get": {"tags": ["播放历史"], "summary": "获取播放历史", "description": "获取用户的播放历史记录", "operationId": "getPlayHistory", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "每页数量", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Play history retrieved successfully"}, "data": {"type": "object", "properties": {"history": {"type": "array", "items": {"$ref": "#/components/schemas/PlayHistory"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT认证令牌"}}, "schemas": {"PlaySession": {"type": "object", "properties": {"_id": {"type": "string", "description": "播放会话ID"}, "userId": {"type": "string", "description": "用户ID"}, "musicId": {"type": "string", "description": "音乐ID"}, "status": {"type": "string", "enum": ["playing", "paused", "stopped"], "description": "播放状态"}, "quality": {"type": "string", "enum": ["standard", "high", "super", "lossless"], "description": "播放质量"}, "position": {"type": "number", "description": "播放位置（秒）"}, "duration": {"type": "number", "description": "总时长（秒）"}, "startedAt": {"type": "string", "format": "date-time", "description": "开始播放时间"}, "lastUpdatedAt": {"type": "string", "format": "date-time", "description": "最后更新时间"}}}, "PlayStatus": {"type": "object", "properties": {"isPlaying": {"type": "boolean", "description": "是否正在播放"}, "currentMusic": {"type": "object", "nullable": true, "properties": {"_id": {"type": "string"}, "title": {"type": "string"}, "artist": {"type": "string"}, "album": {"type": "string"}, "duration": {"type": "number"}, "coverImage": {"type": "string", "nullable": true}}, "description": "当前播放音乐信息"}, "position": {"type": "number", "description": "当前播放位置（秒）"}, "quality": {"type": "string", "enum": ["standard", "high", "super", "lossless"], "description": "播放质量"}, "volume": {"type": "number", "minimum": 0, "maximum": 100, "description": "音量"}, "playMode": {"type": "string", "enum": ["sequence", "loop", "shuffle", "single"], "description": "播放模式"}}}, "PlayQueue": {"type": "object", "properties": {"_id": {"type": "string", "description": "播放队列ID"}, "userId": {"type": "string", "description": "用户ID"}, "currentIndex": {"type": "integer", "description": "当前播放索引"}, "songs": {"type": "array", "items": {"type": "object", "properties": {"musicId": {"type": "object", "properties": {"_id": {"type": "string"}, "title": {"type": "string"}, "artist": {"type": "string"}, "album": {"type": "string"}, "duration": {"type": "number"}, "coverImage": {"type": "string", "nullable": true}}}, "addedAt": {"type": "string", "format": "date-time"}, "order": {"type": "integer"}}}, "description": "队列中的歌曲"}, "playMode": {"type": "string", "enum": ["sequence", "loop", "shuffle", "single"], "description": "播放模式"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "PlayHistory": {"type": "object", "properties": {"_id": {"type": "string", "description": "播放历史ID"}, "userId": {"type": "string", "description": "用户ID"}, "musicId": {"type": "object", "properties": {"_id": {"type": "string"}, "title": {"type": "string"}, "artist": {"type": "string"}, "album": {"type": "string"}, "duration": {"type": "number"}, "coverImage": {"type": "string", "nullable": true}}, "description": "播放的音乐信息"}, "playedAt": {"type": "string", "format": "date-time", "description": "播放时间"}, "duration": {"type": "number", "description": "播放时长（秒）"}, "completionRate": {"type": "number", "minimum": 0, "maximum": 1, "description": "完成率"}, "quality": {"type": "string", "enum": ["standard", "high", "super", "lossless"], "description": "播放质量"}}}, "Pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码"}, "limit": {"type": "integer", "description": "每页数量"}, "total": {"type": "integer", "description": "总记录数"}, "pages": {"type": "integer", "description": "总页数"}, "hasNext": {"type": "boolean", "description": "是否有下一页"}, "hasPrev": {"type": "boolean", "description": "是否有上一页"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "description": "错误类型"}, "message": {"type": "string", "description": "错误描述"}}}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "Unauthorized": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "NotFound": {"description": "资源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}