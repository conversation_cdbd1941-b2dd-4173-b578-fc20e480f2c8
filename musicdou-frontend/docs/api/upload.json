{"openapi": "3.0.3", "info": {"title": "MusicDou 文件上传模块 API", "description": "MusicDou音乐平台文件上传相关API，包括音乐上传、图片上传、文件管理等功能", "version": "1.0.0", "contact": {"name": "MusicDou API Support", "url": "http://localhost:3000"}}, "servers": [{"url": "http://localhost:3000/api/v1", "description": "开发环境服务器"}], "tags": [{"name": "音乐上传", "description": "音乐文件上传和处理"}, {"name": "图片上传", "description": "图片文件上传和处理"}, {"name": "文件管理", "description": "上传文件的管理和操作"}], "paths": {"/upload/music": {"post": {"tags": ["音乐上传"], "summary": "上传音乐文件", "description": "上传音乐文件并提取元数据", "operationId": "uploadMusic", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "format": "binary", "description": "音乐文件 (支持 MP3, FLAC, WAV, AAC, OGG)"}, "title": {"type": "string", "description": "歌曲标题（可选，会从文件元数据提取）"}, "artist": {"type": "string", "description": "艺术家（可选，会从文件元数据提取）"}, "album": {"type": "string", "description": "专辑名称（可选，会从文件元数据提取）"}, "genre": {"type": "string", "description": "音乐流派（可选）"}, "year": {"type": "integer", "description": "发行年份（可选）"}, "tags": {"type": "string", "description": "标签，逗号分隔（可选）"}, "isPublic": {"type": "boolean", "default": true, "description": "是否公开"}}}}}}, "responses": {"201": {"description": "上传成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Music uploaded successfully"}, "data": {"$ref": "#/components/schemas/UploadedMusic"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "413": {"description": "文件过大", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "File too large", "message": "File size exceeds the maximum limit of 100MB"}}}}, "415": {"description": "不支持的文件类型", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Unsupported media type", "message": "Only audio files are supported"}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/upload/image": {"post": {"tags": ["图片上传"], "summary": "上传图片文件", "description": "上传图片文件（用于头像、封面等）", "operationId": "uploadImage", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "format": "binary", "description": "图片文件 (支持 JPEG, PNG, GIF, WebP)"}, "type": {"type": "string", "enum": ["avatar", "cover", "playlist", "general"], "default": "general", "description": "图片用途类型"}, "resize": {"type": "boolean", "default": true, "description": "是否自动调整大小"}, "quality": {"type": "integer", "minimum": 1, "maximum": 100, "default": 85, "description": "图片质量（1-100）"}}}}}}, "responses": {"201": {"description": "上传成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Image uploaded successfully"}, "data": {"$ref": "#/components/schemas/UploadedImage"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "413": {"description": "文件过大", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "File too large", "message": "Image size exceeds the maximum limit of 10MB"}}}}, "415": {"description": "不支持的文件类型", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Unsupported media type", "message": "Only image files are supported"}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/upload/progress/{uploadId}": {"get": {"tags": ["文件管理"], "summary": "获取上传进度", "description": "获取文件上传进度信息", "operationId": "getUploadProgress", "security": [{"bearerAuth": []}], "parameters": [{"name": "uploadId", "in": "path", "required": true, "description": "上传任务ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Upload progress retrieved successfully"}, "data": {"$ref": "#/components/schemas/UploadProgress"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT认证令牌"}}, "schemas": {"UploadedMusic": {"type": "object", "properties": {"_id": {"type": "string", "description": "音乐ID"}, "title": {"type": "string", "description": "歌曲标题"}, "artist": {"type": "string", "description": "艺术家"}, "album": {"type": "string", "description": "专辑名称"}, "duration": {"type": "number", "description": "时长（秒）"}, "bitrate": {"type": "number", "description": "比特率"}, "sampleRate": {"type": "number", "description": "采样率"}, "fileSize": {"type": "number", "description": "文件大小（字节）"}, "format": {"type": "string", "description": "文件格式"}, "quality": {"type": "string", "enum": ["standard", "high", "super", "lossless"], "description": "音质等级"}, "filePath": {"type": "string", "description": "文件路径"}, "originalName": {"type": "string", "description": "原始文件名"}, "status": {"type": "string", "enum": ["processing", "pending", "approved", "rejected"], "description": "处理状态"}, "uploadedBy": {"type": "string", "description": "上传者ID"}, "uploadedAt": {"type": "string", "format": "date-time", "description": "上传时间"}, "metadata": {"type": "object", "properties": {"genre": {"type": "string"}, "year": {"type": "integer"}, "tags": {"type": "array", "items": {"type": "string"}}}, "description": "音乐元数据"}}}, "UploadedImage": {"type": "object", "properties": {"_id": {"type": "string", "description": "图片ID"}, "url": {"type": "string", "description": "图片访问URL"}, "thumbnailUrl": {"type": "string", "description": "缩略图URL"}, "filename": {"type": "string", "description": "文件名"}, "originalName": {"type": "string", "description": "原始文件名"}, "mimeType": {"type": "string", "description": "MIME类型"}, "size": {"type": "number", "description": "文件大小（字节）"}, "dimensions": {"type": "object", "properties": {"width": {"type": "integer"}, "height": {"type": "integer"}}, "description": "图片尺寸"}, "type": {"type": "string", "enum": ["avatar", "cover", "playlist", "general"], "description": "图片用途类型"}, "uploadedBy": {"type": "string", "description": "上传者ID"}, "uploadedAt": {"type": "string", "format": "date-time", "description": "上传时间"}}}, "UploadProgress": {"type": "object", "properties": {"uploadId": {"type": "string", "description": "上传任务ID"}, "status": {"type": "string", "enum": ["uploading", "processing", "completed", "failed"], "description": "上传状态"}, "progress": {"type": "number", "minimum": 0, "maximum": 100, "description": "上传进度百分比"}, "bytesUploaded": {"type": "number", "description": "已上传字节数"}, "totalBytes": {"type": "number", "description": "总字节数"}, "speed": {"type": "number", "description": "上传速度（字节/秒）"}, "estimatedTimeRemaining": {"type": "number", "description": "预计剩余时间（秒）"}, "currentStage": {"type": "string", "description": "当前处理阶段"}, "error": {"type": "string", "nullable": true, "description": "错误信息（如果有）"}, "startedAt": {"type": "string", "format": "date-time", "description": "开始时间"}, "updatedAt": {"type": "string", "format": "date-time", "description": "最后更新时间"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "description": "错误类型"}, "message": {"type": "string", "description": "错误描述"}}}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "Unauthorized": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "NotFound": {"description": "资源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}