{"openapi": "3.0.3", "info": {"title": "MusicDou 音乐管理模块 API", "description": "MusicDou音乐平台音乐管理相关API，包括音乐获取、搜索、上传、管理、批量操作、统计分析等功能", "version": "1.0.0", "contact": {"name": "MusicDou API Support", "url": "http://localhost:3000"}}, "servers": [{"url": "http://localhost:3000/api/v1", "description": "开发环境服务器"}], "tags": [{"name": "音乐浏览", "description": "音乐列表和详情获取"}, {"name": "音乐搜索", "description": "音乐搜索和过滤功能"}, {"name": "音乐上传", "description": "音乐文件上传和管理"}, {"name": "音乐管理", "description": "音乐信息管理和操作"}, {"name": "推荐系统", "description": "音乐推荐和发现功能"}, {"name": "管理员功能", "description": "管理员专用功能"}, {"name": "统计分析", "description": "音乐统计和数据分析"}], "paths": {"/music": {"get": {"tags": ["音乐浏览"], "summary": "获取音乐列表", "description": "获取音乐列表，支持分页、过滤和排序", "operationId": "getMusicList", "parameters": [{"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "每页数量", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "status", "in": "query", "description": "音乐状态", "schema": {"type": "string", "enum": ["pending", "approved", "rejected"]}}, {"name": "quality", "in": "query", "description": "音质等级", "schema": {"type": "string", "enum": ["standard", "high", "super", "lossless"]}}, {"name": "genre", "in": "query", "description": "音乐流派", "schema": {"type": "string"}}, {"name": "artist", "in": "query", "description": "艺术家", "schema": {"type": "string"}}, {"name": "sortBy", "in": "query", "description": "排序方式", "schema": {"type": "string", "enum": ["newest", "oldest", "popular", "title", "artist"], "default": "newest"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Music list retrieved successfully"}, "data": {"type": "object", "properties": {"music": {"type": "array", "items": {"$ref": "#/components/schemas/Music"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/music/{id}": {"get": {"tags": ["音乐浏览"], "summary": "获取音乐详情", "description": "根据ID获取音乐的详细信息", "operationId": "getMusicById", "parameters": [{"name": "id", "in": "path", "required": true, "description": "音乐ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Music retrieved successfully"}, "data": {"$ref": "#/components/schemas/MusicDetail"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["音乐管理"], "summary": "更新音乐信息", "description": "更新音乐的元数据信息", "operationId": "updateMusic", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "音乐ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string", "description": "歌曲标题"}, "artist": {"type": "string", "description": "艺术家"}, "album": {"type": "string", "description": "专辑名称"}, "genre": {"type": "string", "description": "音乐流派"}, "year": {"type": "integer", "description": "发行年份"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "标签"}}}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Music updated successfully"}, "data": {"$ref": "#/components/schemas/Music"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["音乐管理"], "summary": "删除音乐", "description": "删除指定的音乐文件和记录", "operationId": "deleteMusic", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "音乐ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Music deleted successfully"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/music/search": {"get": {"tags": ["音乐搜索"], "summary": "搜索音乐", "description": "根据关键词搜索音乐", "operationId": "searchMusic", "parameters": [{"name": "q", "in": "query", "required": true, "description": "搜索关键词", "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "每页数量", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "sortBy", "in": "query", "description": "排序方式", "schema": {"type": "string", "enum": ["relevance", "newest", "popular", "title", "artist"], "default": "relevance"}}, {"name": "quality", "in": "query", "description": "音质过滤", "schema": {"type": "string", "enum": ["standard", "high", "super", "lossless"]}}, {"name": "genre", "in": "query", "description": "流派过滤", "schema": {"type": "string"}}, {"name": "artist", "in": "query", "description": "艺术家过滤", "schema": {"type": "string"}}], "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Music search completed successfully"}, "data": {"type": "object", "properties": {"music": {"type": "array", "items": {"$ref": "#/components/schemas/Music"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}, "searchInfo": {"type": "object", "properties": {"query": {"type": "string"}, "totalResults": {"type": "integer"}, "searchTime": {"type": "number"}}}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/music/search/advanced": {"get": {"tags": ["音乐搜索"], "summary": "高级搜索音乐", "description": "使用高级过滤条件搜索音乐", "operationId": "advancedSearchMusic", "parameters": [{"name": "q", "in": "query", "description": "搜索关键词", "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "每页数量", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "minDuration", "in": "query", "description": "最小时长（秒）", "schema": {"type": "integer", "minimum": 0}}, {"name": "maxDuration", "in": "query", "description": "最大时长（秒）", "schema": {"type": "integer", "minimum": 0}}, {"name": "minBitrate", "in": "query", "description": "最小比特率", "schema": {"type": "integer", "minimum": 0}}, {"name": "maxBitrate", "in": "query", "description": "最大比特率", "schema": {"type": "integer", "minimum": 0}}, {"name": "year", "in": "query", "description": "发行年份", "schema": {"type": "integer"}}, {"name": "exactMatch", "in": "query", "description": "精确匹配", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Advanced search completed successfully"}, "data": {"type": "object", "properties": {"music": {"type": "array", "items": {"$ref": "#/components/schemas/Music"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT认证令牌"}}, "schemas": {"Music": {"type": "object", "properties": {"_id": {"type": "string", "description": "音乐唯一标识符"}, "title": {"type": "string", "description": "歌曲标题"}, "artist": {"type": "string", "description": "艺术家"}, "album": {"type": "string", "description": "专辑名称"}, "duration": {"type": "number", "description": "时长（秒）"}, "bitrate": {"type": "number", "description": "比特率"}, "sampleRate": {"type": "number", "description": "采样率"}, "fileSize": {"type": "number", "description": "文件大小（字节）"}, "quality": {"type": "string", "enum": ["standard", "high", "super", "lossless"], "description": "音质等级"}, "genre": {"type": "string", "description": "音乐流派"}, "year": {"type": "integer", "description": "发行年份"}, "coverImage": {"type": "string", "nullable": true, "description": "封面图片URL"}, "status": {"type": "string", "enum": ["pending", "approved", "rejected"], "description": "审核状态"}, "uploadedBy": {"type": "string", "description": "上传者ID"}, "playCount": {"type": "number", "description": "播放次数"}, "likeCount": {"type": "number", "description": "点赞数"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "标签"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "MusicDetail": {"allOf": [{"$ref": "#/components/schemas/Music"}, {"type": "object", "properties": {"filePath": {"type": "string", "description": "文件路径"}, "lyrics": {"type": "string", "nullable": true, "description": "歌词"}, "uploadedByUser": {"type": "object", "properties": {"_id": {"type": "string"}, "username": {"type": "string"}, "avatar": {"type": "string", "nullable": true}}, "description": "上传者信息"}, "reviewInfo": {"type": "object", "nullable": true, "properties": {"reviewedBy": {"type": "string"}, "reviewedAt": {"type": "string", "format": "date-time"}, "reviewNote": {"type": "string"}}, "description": "审核信息"}}}]}, "Pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码"}, "limit": {"type": "integer", "description": "每页数量"}, "total": {"type": "integer", "description": "总记录数"}, "pages": {"type": "integer", "description": "总页数"}, "hasNext": {"type": "boolean", "description": "是否有下一页"}, "hasPrev": {"type": "boolean", "description": "是否有上一页"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "description": "错误类型"}, "message": {"type": "string", "description": "错误描述"}}}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Validation error", "message": "Search query is required"}}}}, "Unauthorized": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Access denied", "message": "No token provided"}}}}, "Forbidden": {"description": "访问被禁止", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Permission denied", "message": "You don't have permission to access this resource"}}}}, "NotFound": {"description": "资源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Music not found", "message": "Music not found"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Internal Server Error", "message": "An error occurred during processing"}}}}}}}