{"openapi": "3.0.3", "info": {"title": "MusicDou 社交功能模块 API", "description": "MusicDou音乐平台社交功能相关API，包括关注系统、评论系统、点赞系统、分享系统、动态系统等功能", "version": "1.0.0", "contact": {"name": "MusicDou API Support", "url": "http://localhost:3000"}}, "servers": [{"url": "http://localhost:3000/api/v1", "description": "开发环境服务器"}], "tags": [{"name": "关注系统", "description": "用户关注和粉丝管理"}, {"name": "评论系统", "description": "音乐评论和回复功能"}, {"name": "点赞系统", "description": "内容点赞和取消点赞"}, {"name": "分享系统", "description": "内容分享和分享链接生成"}, {"name": "动态系统", "description": "用户动态发布和时间线"}], "paths": {"/follows/{userId}": {"post": {"tags": ["关注系统"], "summary": "关注用户", "description": "关注指定用户", "operationId": "followUser", "security": [{"bearerAuth": []}], "parameters": [{"name": "userId", "in": "path", "required": true, "description": "要关注的用户ID", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"source": {"type": "string", "description": "关注来源", "enum": ["profile", "recommendation", "search", "mutual"]}}}}}}, "responses": {"201": {"description": "关注成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "User followed successfully"}, "data": {"$ref": "#/components/schemas/Follow"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "409": {"description": "已经关注", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Already following", "message": "You are already following this user"}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["关注系统"], "summary": "取消关注用户", "description": "取消关注指定用户", "operationId": "unfollowUser", "security": [{"bearerAuth": []}], "parameters": [{"name": "userId", "in": "path", "required": true, "description": "要取消关注的用户ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "取消关注成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "User unfollowed successfully"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/follows/{userId}/following": {"get": {"tags": ["关注系统"], "summary": "获取用户关注列表", "description": "获取指定用户的关注列表", "operationId": "getFollowing", "security": [{"bearerAuth": []}], "parameters": [{"name": "userId", "in": "path", "required": true, "description": "用户ID", "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "每页数量", "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 20}}, {"name": "sort", "in": "query", "description": "排序方式", "schema": {"type": "string", "enum": ["recent", "oldest", "mutual"], "default": "recent"}}, {"name": "status", "in": "query", "description": "关注状态过滤", "schema": {"type": "string", "enum": ["active", "mutual"]}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Following list retrieved successfully"}, "data": {"type": "object", "properties": {"following": {"type": "array", "items": {"$ref": "#/components/schemas/FollowWithUser"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/comments": {"post": {"tags": ["评论系统"], "summary": "发布评论", "description": "发布音乐评论或回复评论", "operationId": "createComment", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["content", "musicId"], "properties": {"content": {"type": "string", "minLength": 1, "maxLength": 1000, "description": "评论内容"}, "musicId": {"type": "string", "description": "音乐ID"}, "parentId": {"type": "string", "description": "父评论ID（回复时使用）"}, "replyToUserId": {"type": "string", "description": "回复的用户ID"}}}}}}, "responses": {"201": {"description": "评论发布成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Comment created successfully"}, "data": {"$ref": "#/components/schemas/Comment"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/comments/music/{musicId}": {"get": {"tags": ["评论系统"], "summary": "获取音乐评论列表", "description": "获取指定音乐的评论列表", "operationId": "getMusicComments", "security": [{"bearerAuth": []}], "parameters": [{"name": "musicId", "in": "path", "required": true, "description": "音乐ID", "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "每页数量", "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 20}}, {"name": "sortBy", "in": "query", "description": "排序方式", "schema": {"type": "string", "enum": ["newest", "oldest", "hot", "likes"], "default": "newest"}}, {"name": "includeReplies", "in": "query", "description": "是否包含回复", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Comments retrieved successfully"}, "data": {"type": "object", "properties": {"comments": {"type": "array", "items": {"$ref": "#/components/schemas/Comment"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/likes": {"post": {"tags": ["点赞系统"], "summary": "点赞目标", "description": "对音乐、评论或歌单进行点赞", "operationId": "<PERSON><PERSON><PERSON><PERSON>", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["targetType", "targetId"], "properties": {"targetType": {"type": "string", "enum": ["music", "comment", "playlist"], "description": "点赞目标类型"}, "targetId": {"type": "string", "description": "目标ID"}}}}}}, "responses": {"201": {"description": "点赞成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Target liked successfully"}, "data": {"$ref": "#/components/schemas/Like"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "409": {"description": "已经点赞", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Already liked", "message": "You have already liked this target"}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["点赞系统"], "summary": "取消点赞", "description": "取消对目标的点赞", "operationId": "unlike<PERSON><PERSON><PERSON>", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["targetType", "targetId"], "properties": {"targetType": {"type": "string", "enum": ["music", "comment", "playlist"], "description": "点赞目标类型"}, "targetId": {"type": "string", "description": "目标ID"}}}}}}, "responses": {"200": {"description": "取消点赞成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Target unliked successfully"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT认证令牌"}}, "schemas": {"Follow": {"type": "object", "properties": {"_id": {"type": "string", "description": "关注记录ID"}, "follower": {"type": "string", "description": "关注者ID"}, "following": {"type": "string", "description": "被关注者ID"}, "status": {"type": "string", "enum": ["active", "blocked"], "description": "关注状态"}, "isMutual": {"type": "boolean", "description": "是否相互关注"}, "source": {"type": "string", "enum": ["profile", "recommendation", "search", "mutual"], "description": "关注来源"}, "createdAt": {"type": "string", "format": "date-time", "description": "关注时间"}}}, "FollowWithUser": {"type": "object", "properties": {"_id": {"type": "string", "description": "关注记录ID"}, "following": {"type": "object", "properties": {"_id": {"type": "string"}, "username": {"type": "string"}, "avatar": {"type": "string", "nullable": true}, "userGroup": {"type": "string", "enum": ["normal", "vip", "admin"]}}, "description": "被关注用户信息"}, "status": {"type": "string", "enum": ["active", "blocked"], "description": "关注状态"}, "isMutual": {"type": "boolean", "description": "是否相互关注"}, "createdAt": {"type": "string", "format": "date-time", "description": "关注时间"}}}, "Comment": {"type": "object", "properties": {"_id": {"type": "string", "description": "评论ID"}, "content": {"type": "string", "description": "评论内容"}, "author": {"type": "object", "properties": {"_id": {"type": "string"}, "username": {"type": "string"}, "avatar": {"type": "string", "nullable": true}, "userGroup": {"type": "string", "enum": ["normal", "vip", "admin"]}}, "description": "评论作者信息"}, "musicId": {"type": "string", "description": "音乐ID"}, "parentId": {"type": "string", "nullable": true, "description": "父评论ID"}, "rootId": {"type": "string", "nullable": true, "description": "根评论ID"}, "level": {"type": "number", "description": "评论层级"}, "replyToUser": {"type": "object", "nullable": true, "properties": {"_id": {"type": "string"}, "username": {"type": "string"}}, "description": "回复的用户信息"}, "status": {"type": "string", "enum": ["pending", "approved", "rejected", "hidden"], "description": "审核状态"}, "stats": {"type": "object", "properties": {"likes": {"type": "number", "description": "点赞数"}, "replies": {"type": "number", "description": "回复数"}, "reports": {"type": "number", "description": "举报数"}}}, "isEdited": {"type": "boolean", "description": "是否已编辑"}, "editHistory": {"type": "array", "items": {"type": "object", "properties": {"content": {"type": "string"}, "editedAt": {"type": "string", "format": "date-time"}, "reason": {"type": "string"}}}, "description": "编辑历史"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "Like": {"type": "object", "properties": {"_id": {"type": "string", "description": "点赞记录ID"}, "user": {"type": "string", "description": "点赞用户ID"}, "targetType": {"type": "string", "enum": ["music", "comment", "playlist"], "description": "点赞目标类型"}, "targetId": {"type": "string", "description": "目标ID"}, "createdAt": {"type": "string", "format": "date-time", "description": "点赞时间"}}}, "Pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码"}, "limit": {"type": "integer", "description": "每页数量"}, "total": {"type": "integer", "description": "总记录数"}, "pages": {"type": "integer", "description": "总页数"}, "hasNext": {"type": "boolean", "description": "是否有下一页"}, "hasPrev": {"type": "boolean", "description": "是否有上一页"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "description": "错误类型"}, "message": {"type": "string", "description": "错误描述"}}}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Validation error", "message": "Invalid target type"}}}}, "Unauthorized": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Access denied", "message": "No token provided"}}}}, "NotFound": {"description": "资源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Not found", "message": "User not found"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Internal Server Error", "message": "An error occurred during processing"}}}}}}}