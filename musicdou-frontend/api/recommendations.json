{"openapi": "3.0.3", "info": {"title": "MusicDou 推荐系统模块 API", "description": "MusicDou音乐平台推荐系统相关API，包括个性化推荐、相似推荐、热门推荐等功能", "version": "1.0.0", "contact": {"name": "MusicDou API Support", "url": "http://localhost:3000"}}, "servers": [{"url": "http://localhost:3000/api/v1", "description": "开发环境服务器"}], "tags": [{"name": "个性化推荐", "description": "基于用户行为的个性化音乐推荐"}, {"name": "相似推荐", "description": "基于内容的相似音乐推荐"}, {"name": "热门推荐", "description": "热门音乐和趋势推荐"}], "paths": {"/recommendations/personalized": {"get": {"tags": ["个性化推荐"], "summary": "获取个性化推荐", "description": "基于用户历史行为获取个性化音乐推荐", "operationId": "getPersonalizedRecommendations", "security": [{"bearerAuth": []}], "parameters": [{"name": "limit", "in": "query", "description": "推荐数量", "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 20}}, {"name": "type", "in": "query", "description": "推荐类型", "schema": {"type": "string", "enum": ["discover", "daily", "weekly", "mood"], "default": "discover"}}, {"name": "genre", "in": "query", "description": "流派过滤", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Personalized recommendations retrieved successfully"}, "data": {"type": "object", "properties": {"recommendations": {"type": "array", "items": {"$ref": "#/components/schemas/RecommendationItem"}}, "metadata": {"$ref": "#/components/schemas/RecommendationMetadata"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/recommendations/similar/{musicId}": {"get": {"tags": ["相似推荐"], "summary": "获取相似音乐推荐", "description": "基于指定音乐获取相似音乐推荐", "operationId": "getSimilarRecommendations", "parameters": [{"name": "musicId", "in": "path", "required": true, "description": "音乐ID", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "description": "推荐数量", "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 10}}, {"name": "algorithm", "in": "query", "description": "推荐算法", "schema": {"type": "string", "enum": ["content", "collaborative", "hybrid"], "default": "hybrid"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Similar recommendations retrieved successfully"}, "data": {"type": "object", "properties": {"sourceMusic": {"$ref": "#/components/schemas/MusicBasic"}, "recommendations": {"type": "array", "items": {"$ref": "#/components/schemas/SimilarRecommendationItem"}}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/recommendations/trending": {"get": {"tags": ["热门推荐"], "summary": "获取热门推荐", "description": "获取当前热门和趋势音乐推荐", "operationId": "getTrendingRecommendations", "parameters": [{"name": "timeframe", "in": "query", "description": "时间范围", "schema": {"type": "string", "enum": ["daily", "weekly", "monthly", "yearly"], "default": "weekly"}}, {"name": "category", "in": "query", "description": "分类", "schema": {"type": "string", "enum": ["all", "new", "rising", "viral"], "default": "all"}}, {"name": "limit", "in": "query", "description": "推荐数量", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 50}}, {"name": "genre", "in": "query", "description": "流派过滤", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Trending recommendations retrieved successfully"}, "data": {"type": "object", "properties": {"trending": {"type": "array", "items": {"$ref": "#/components/schemas/TrendingRecommendationItem"}}, "metadata": {"type": "object", "properties": {"timeframe": {"type": "string"}, "category": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}}}}}}}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/recommendations/feedback": {"post": {"tags": ["个性化推荐"], "summary": "提交推荐反馈", "description": "用户对推荐结果的反馈，用于优化推荐算法", "operationId": "submitRecommendationFeedback", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["musicId", "action"], "properties": {"musicId": {"type": "string", "description": "音乐ID"}, "action": {"type": "string", "enum": ["like", "dislike", "play", "skip", "save"], "description": "用户行为"}, "recommendationType": {"type": "string", "enum": ["personalized", "similar", "trending"], "description": "推荐类型"}, "context": {"type": "object", "properties": {"position": {"type": "integer", "description": "推荐位置"}, "algorithm": {"type": "string", "description": "使用的算法"}}, "description": "推荐上下文"}}}}}}, "responses": {"200": {"description": "反馈提交成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> submitted successfully"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT认证令牌"}}, "schemas": {"RecommendationItem": {"type": "object", "properties": {"music": {"$ref": "#/components/schemas/MusicBasic"}, "score": {"type": "number", "minimum": 0, "maximum": 1, "description": "推荐分数"}, "reason": {"type": "string", "description": "推荐理由"}, "algorithm": {"type": "string", "description": "使用的推荐算法"}}}, "SimilarRecommendationItem": {"type": "object", "properties": {"music": {"$ref": "#/components/schemas/MusicBasic"}, "similarity": {"type": "number", "minimum": 0, "maximum": 1, "description": "相似度分数"}, "features": {"type": "array", "items": {"type": "string"}, "description": "相似特征"}}}, "TrendingRecommendationItem": {"type": "object", "properties": {"music": {"$ref": "#/components/schemas/MusicBasic"}, "rank": {"type": "integer", "description": "排名"}, "trendScore": {"type": "number", "description": "趋势分数"}, "stats": {"type": "object", "properties": {"playCount": {"type": "number"}, "likeCount": {"type": "number"}, "shareCount": {"type": "number"}, "growth": {"type": "number", "description": "增长率"}}}}}, "MusicBasic": {"type": "object", "properties": {"_id": {"type": "string", "description": "音乐ID"}, "title": {"type": "string", "description": "歌曲标题"}, "artist": {"type": "string", "description": "艺术家"}, "album": {"type": "string", "description": "专辑名称"}, "duration": {"type": "number", "description": "时长（秒）"}, "genre": {"type": "string", "description": "音乐流派"}, "coverImage": {"type": "string", "nullable": true, "description": "封面图片URL"}, "quality": {"type": "string", "enum": ["standard", "high", "super", "lossless"], "description": "音质等级"}}}, "RecommendationMetadata": {"type": "object", "properties": {"type": {"type": "string", "description": "推荐类型"}, "algorithm": {"type": "string", "description": "使用的算法"}, "generatedAt": {"type": "string", "format": "date-time", "description": "生成时间"}, "userProfile": {"type": "object", "properties": {"preferences": {"type": "array", "items": {"type": "string"}}, "recentActivity": {"type": "string", "format": "date-time"}}}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "description": "错误类型"}, "message": {"type": "string", "description": "错误描述"}}}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "Unauthorized": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "NotFound": {"description": "资源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}