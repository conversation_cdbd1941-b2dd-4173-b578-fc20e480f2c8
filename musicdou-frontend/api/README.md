# MusicDou API 文档

本目录包含了 MusicDou 音乐平台的完整 OpenAPI 3.0 格式的 API 文档，按功能模块分类组织。

## 文档结构

### 📁 API 模块文档

| 文件名 | 模块名称 | 描述 | 主要功能 |
|--------|----------|------|----------|
| `auth.json` | 用户认证模块 | 用户认证相关API | 注册、登录、个人资料管理、每日签到 |
| `music.json` | 音乐管理模块 | 音乐管理相关API | 音乐获取、搜索、上传、管理、批量操作 |
| `playlists.json` | 歌单管理模块 | 歌单管理相关API | 歌单CRUD、歌曲管理、收藏功能 |
| `social.json` | 社交功能模块 | 社交功能相关API | 关注、评论、点赞、分享、动态 |
| `play.json` | 播放功能模块 | 播放功能相关API | 播放控制、播放历史、播放队列 |
| `recommendations.json` | 推荐系统模块 | 推荐系统相关API | 个性化推荐、相似推荐、热门推荐 |
| `notifications.json` | 通知系统模块 | 通知系统相关API | 通知管理、通知设置、系统通知 |
| `upload.json` | 文件上传模块 | 文件上传相关API | 音乐上传、图片上传、文件管理 |
| `stats.json` | 统计分析模块 | 统计分析相关API | 用户统计、音乐统计、系统监控 |
| `points.json` | 积分系统模块 | 积分系统相关API | 积分获取、积分历史、积分兑换 |
| `admin.json` | 系统管理模块 | 系统管理相关API | 性能监控、音频质量管理、批量操作 |

## 🚀 使用方法

### 1. 在线查看
可以使用以下工具在线查看和测试 API 文档：

- **Swagger UI**: 将 JSON 文件导入 [Swagger Editor](https://editor.swagger.io/)
- **Postman**: 导入 OpenAPI 文档到 Postman 进行测试
- **Insomnia**: 支持直接导入 OpenAPI 3.0 格式文档

### 2. 本地部署
```bash
# 使用 swagger-ui-serve 本地部署
npm install -g swagger-ui-serve
swagger-ui-serve docs/api/auth.json

# 或使用 Docker
docker run -p 8080:8080 -v $(pwd)/docs/api:/usr/share/nginx/html/api swaggerapi/swagger-ui
```

### 3. 代码生成
可以使用 OpenAPI Generator 生成各种语言的客户端代码：

```bash
# 安装 OpenAPI Generator
npm install @openapitools/openapi-generator-cli -g

# 生成 JavaScript 客户端
openapi-generator-cli generate -i docs/api/auth.json -g javascript -o ./client/auth

# 生成 Python 客户端
openapi-generator-cli generate -i docs/api/music.json -g python -o ./client/music
```

## 📋 API 概览

### 认证方式
所有需要认证的 API 都使用 JWT Bearer Token 认证：
```
Authorization: Bearer <your-jwt-token>
```

### 基础 URL
```
http://localhost:3000/api/v1
```

### 响应格式
所有 API 响应都遵循统一的格式：
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误类型",
  "message": "错误描述"
}
```

## 🔧 主要功能模块

### 1. 用户认证 (`auth.json`)
- 用户注册和登录
- 个人资料管理
- 每日签到系统
- JWT 令牌管理

### 2. 音乐管理 (`music.json`)
- 音乐列表和详情获取
- 高级搜索功能
- 音乐信息管理
- 批量操作支持

### 3. 歌单管理 (`playlists.json`)
- 歌单 CRUD 操作
- 歌曲添加和排序
- 收藏和分享功能
- 封面管理

### 4. 社交功能 (`social.json`)
- 用户关注系统
- 评论和回复
- 点赞和分享
- 用户动态

### 5. 播放功能 (`play.json`)
- 播放控制（播放、暂停、停止）
- 播放队列管理
- 播放历史记录
- 播放状态同步

### 6. 推荐系统 (`recommendations.json`)
- 个性化音乐推荐
- 基于内容的相似推荐
- 热门和趋势推荐
- 推荐反馈机制

### 7. 通知系统 (`notifications.json`)
- 实时通知推送
- 通知偏好设置
- 通知历史管理
- 免打扰模式

### 8. 文件上传 (`upload.json`)
- 音乐文件上传和处理
- 图片上传和压缩
- 上传进度跟踪
- 文件格式验证

### 9. 统计分析 (`stats.json`)
- 用户行为统计
- 音乐播放统计
- 系统性能监控
- 自定义报告生成

### 10. 积分系统 (`points.json`)
- 积分获取和消费
- 积分历史记录
- 积分兑换商城
- 等级系统

### 11. 系统管理 (`admin.json`)
- 系统性能监控
- 音频质量分析
- 批量数据处理
- 健康状态检查

## 📊 数据模型

每个模块都定义了完整的数据模型，包括：
- 请求参数验证
- 响应数据结构
- 错误处理规范
- 分页和排序支持

## 🔒 安全性

- JWT 认证机制
- 角色权限控制
- 请求频率限制
- 数据验证和过滤

## 📝 版本信息

- OpenAPI 版本: 3.0.3
- API 版本: 1.0.0
- 最后更新: 2025-07-31

## 🤝 贡献指南

如需更新 API 文档，请：
1. 修改对应的 JSON 文件
2. 验证 OpenAPI 格式正确性
3. 更新相关的代码实现
4. 提交 Pull Request

## 📞 支持

如有问题或建议，请联系：
- 项目地址: http://localhost:3000
- 技术支持: MusicDou API Support
