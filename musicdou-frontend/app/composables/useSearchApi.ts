import type { 
  SearchResult, 
  SearchQuery, 
  SearchSuggestion, 
  SearchFilters,
  Music,
  Playlist,
  User,
  PaginatedResponse,
  ApiResponse
} from '~/types'

export const useSearchApi = () => {
  const { get, post } = useApi()

  // 全局搜索
  const globalSearch = async (searchQuery: SearchQuery): Promise<ApiResponse<PaginatedResponse<SearchResult>>> => {
    try {
      const response = await post<PaginatedResponse<SearchResult>>('/search', searchQuery)
      return response
    } catch (error) {
      console.error('全局搜索错误:', error)
      throw error
    }
  }

  // 音乐搜索
  const searchMusic = async (
    query: string, 
    filters?: Partial<SearchFilters>,
    page = 1,
    limit = 20
  ): Promise<ApiResponse<PaginatedResponse<Music>>> => {
    try {
      const searchParams = {
        query,
        filters: { ...filters, type: 'music' },
        page,
        limit
      }
      
      const response = await post<PaginatedResponse<Music>>('/search/music', searchParams)
      return response
    } catch (error) {
      console.error('音乐搜索错误:', error)
      throw error
    }
  }

  // 歌单搜索
  const searchPlaylists = async (
    query: string,
    filters?: Partial<SearchFilters>,
    page = 1,
    limit = 20
  ): Promise<ApiResponse<PaginatedResponse<Playlist>>> => {
    try {
      const searchParams = {
        query,
        filters: { ...filters, type: 'playlists' },
        page,
        limit
      }
      
      const response = await post<PaginatedResponse<Playlist>>('/search/playlists', searchParams)
      return response
    } catch (error) {
      console.error('歌单搜索错误:', error)
      throw error
    }
  }

  // 用户搜索
  const searchUsers = async (
    query: string,
    filters?: Partial<SearchFilters>,
    page = 1,
    limit = 20
  ): Promise<ApiResponse<PaginatedResponse<User>>> => {
    try {
      const searchParams = {
        query,
        filters: { ...filters, type: 'users' },
        page,
        limit
      }
      
      const response = await post<PaginatedResponse<User>>('/search/users', searchParams)
      return response
    } catch (error) {
      console.error('用户搜索错误:', error)
      throw error
    }
  }

  // 获取搜索建议
  const getSearchSuggestions = async (
    query: string,
    limit = 10
  ): Promise<ApiResponse<SearchSuggestion[]>> => {
    try {
      const response = await get<SearchSuggestion[]>('/search/suggestions', {
        query,
        limit
      })
      return response
    } catch (error) {
      console.error('获取搜索建议错误:', error)
      throw error
    }
  }

  // 获取热门搜索
  const getHotSearches = async (limit = 10): Promise<ApiResponse<string[]>> => {
    try {
      const response = await get<string[]>('/search/hot', { limit })
      return response
    } catch (error) {
      console.error('获取热门搜索错误:', error)
      throw error
    }
  }

  // 获取搜索统计
  const getSearchStats = async (): Promise<ApiResponse<{
    totalSearches: number
    popularQueries: Array<{ query: string; count: number }>
    recentTrends: Array<{ query: string; growth: number }>
  }>> => {
    try {
      const response = await get('/search/stats')
      return response
    } catch (error) {
      console.error('获取搜索统计错误:', error)
      throw error
    }
  }

  // 记录搜索行为
  const recordSearch = async (query: string, resultCount: number): Promise<ApiResponse<void>> => {
    try {
      const response = await post<void>('/search/record', {
        query,
        resultCount,
        timestamp: new Date().toISOString()
      })
      return response
    } catch (error) {
      console.error('记录搜索行为错误:', error)
      throw error
    }
  }

  // 获取相关搜索
  const getRelatedSearches = async (query: string): Promise<ApiResponse<string[]>> => {
    try {
      const response = await get<string[]>('/search/related', { query })
      return response
    } catch (error) {
      console.error('获取相关搜索错误:', error)
      throw error
    }
  }

  // 按类型搜索
  const searchByType = async (
    query: string,
    type: 'music' | 'playlists' | 'users',
    filters?: Partial<SearchFilters>,
    page = 1,
    limit = 20
  ) => {
    switch (type) {
      case 'music':
        return searchMusic(query, filters, page, limit)
      case 'playlists':
        return searchPlaylists(query, filters, page, limit)
      case 'users':
        return searchUsers(query, filters, page, limit)
      default:
        throw new Error(`不支持的搜索类型: ${type}`)
    }
  }

  // 高级搜索
  const advancedSearch = async (searchParams: {
    query: string
    filters: SearchFilters
    sortBy?: 'relevance' | 'date' | 'popularity' | 'rating'
    sortOrder?: 'asc' | 'desc'
    page?: number
    limit?: number
  }): Promise<ApiResponse<PaginatedResponse<SearchResult>>> => {
    try {
      const response = await post<PaginatedResponse<SearchResult>>('/search/advanced', searchParams)
      return response
    } catch (error) {
      console.error('高级搜索错误:', error)
      throw error
    }
  }

  // 搜索自动完成
  const getAutoComplete = async (
    query: string,
    types: Array<'query' | 'artist' | 'album' | 'genre'> = ['query', 'artist']
  ): Promise<ApiResponse<SearchSuggestion[]>> => {
    try {
      const response = await get<SearchSuggestion[]>('/search/autocomplete', {
        query,
        types: types.join(',')
      })
      return response
    } catch (error) {
      console.error('获取自动完成错误:', error)
      throw error
    }
  }

  // 搜索历史同步（如果用户登录）
  const syncSearchHistory = async (history: Array<{
    query: string
    timestamp: string
    resultCount: number
  }>): Promise<ApiResponse<void>> => {
    try {
      const response = await post<void>('/search/history/sync', { history })
      return response
    } catch (error) {
      console.error('同步搜索历史错误:', error)
      throw error
    }
  }

  // 获取用户搜索历史
  const getUserSearchHistory = async (
    page = 1,
    limit = 20
  ): Promise<ApiResponse<PaginatedResponse<{
    query: string
    timestamp: string
    resultCount: number
  }>>> => {
    try {
      const response = await get('/search/history', { page, limit })
      return response
    } catch (error) {
      console.error('获取用户搜索历史错误:', error)
      throw error
    }
  }

  // 清除用户搜索历史
  const clearUserSearchHistory = async (): Promise<ApiResponse<void>> => {
    try {
      const response = await post<void>('/search/history/clear')
      return response
    } catch (error) {
      console.error('清除用户搜索历史错误:', error)
      throw error
    }
  }

  // 获取搜索过滤选项
  const getSearchFilters = async (): Promise<ApiResponse<{
    genres: string[]
    languages: string[]
    qualities: string[]
    dateRanges: Array<{ label: string; value: string }>
  }>> => {
    try {
      const response = await get('/search/filters')
      return response
    } catch (error) {
      console.error('获取搜索过滤选项错误:', error)
      throw error
    }
  }

  // 搜索结果反馈
  const submitSearchFeedback = async (
    query: string,
    feedback: {
      helpful: boolean
      relevantResults: number
      suggestions?: string
    }
  ): Promise<ApiResponse<void>> => {
    try {
      const response = await post<void>('/search/feedback', {
        query,
        ...feedback,
        timestamp: new Date().toISOString()
      })
      return response
    } catch (error) {
      console.error('提交搜索反馈错误:', error)
      throw error
    }
  }

  return {
    // 基础搜索
    globalSearch,
    searchMusic,
    searchPlaylists,
    searchUsers,
    searchByType,
    advancedSearch,
    
    // 搜索建议和自动完成
    getSearchSuggestions,
    getAutoComplete,
    getRelatedSearches,
    
    // 热门和统计
    getHotSearches,
    getSearchStats,
    
    // 搜索历史
    recordSearch,
    syncSearchHistory,
    getUserSearchHistory,
    clearUserSearchHistory,
    
    // 搜索配置
    getSearchFilters,
    
    // 反馈
    submitSearchFeedback
  }
}
