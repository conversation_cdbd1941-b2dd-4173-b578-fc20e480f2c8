import type { ApiResponse } from '~/types'

export const useApi = () => {
  const config = useRuntimeConfig()
  const notification = useNotification()

  // 创建API请求函数
  const api = async (url: string, options: any = {}) => {
    // 添加认证token
    const token = useCookie('auth-token')
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-Request-ID': Math.random().toString(36).substr(2, 9),
      ...options.headers
    }

    if (token.value) {
      headers.Authorization = `Bearer ${token.value}`
    }

    // 构建完整URL
    const fullUrl = url.startsWith('http') ? url : `${config.public.apiBase}${url}`

    try {
      const response = await $fetch(fullUrl, {
        ...options,
        headers
      })
      return response
    } catch (error: any) {
      // 处理认证错误 - 只在特定情况下自动跳转
      if (error.status === 401) {
        // 只有在当前页面需要认证时才自动跳转
        const currentRoute = useRoute()
        const shouldRedirect = currentRoute.meta?.middleware?.includes('auth')

        if (shouldRedirect) {
          // 清除token并跳转到登录页
          const token = useCookie('auth-token')
          token.value = null
          notification.error('登录已过期', '请重新登录')
          await navigateTo('/login')
        }
        throw error
      }

      // 处理其他错误
      if (error.status >= 500) {
        notification.error('服务器错误', '请稍后重试')
      } else if (error.status === 403) {
        notification.error('权限不足', '您没有执行此操作的权限')
      } else if (error.status === 404) {
        notification.error('资源不存在', '请求的资源未找到')
      } else if (error.status === 429) {
        notification.error('请求过于频繁', '请稍后再试')
      } else if (error.status === 400) {
        const errorMessage = error.data?.message || '请求参数错误'
        notification.error('请求错误', errorMessage)
      }

      throw error
    }
  }

  // 通用API请求方法
  const request = async <T = any>(
    url: string,
    options: any = {}
  ): Promise<ApiResponse<T>> => {
    try {
      const response = await api<ApiResponse<T>>(url, options)
      return response
    } catch (error: any) {
      console.error('API请求错误:', error)
      throw error
    }
  }

  // GET请求
  const get = <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return request<T>(url, {
      method: 'GET',
      params
    })
  }

  // POST请求
  const post = <T = any>(url: string, body?: any): Promise<ApiResponse<T>> => {
    return request<T>(url, {
      method: 'POST',
      body
    })
  }

  // PUT请求
  const put = <T = any>(url: string, body?: any): Promise<ApiResponse<T>> => {
    return request<T>(url, {
      method: 'PUT',
      body
    })
  }

  // DELETE请求
  const del = <T = any>(url: string): Promise<ApiResponse<T>> => {
    return request<T>(url, {
      method: 'DELETE'
    })
  }

  // 文件上传
  const upload = async <T = any>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<T>> => {
    const formData = new FormData()
    formData.append('file', file)

    return request<T>(url, {
      method: 'POST',
      body: formData,
      onUploadProgress: (progress: any) => {
        if (onProgress && progress.total) {
          const percentage = Math.round((progress.loaded * 100) / progress.total)
          onProgress(percentage)
        }
      }
    })
  }

  return {
    api,
    request,
    get,
    post,
    put,
    delete: del,
    upload
  }
}
