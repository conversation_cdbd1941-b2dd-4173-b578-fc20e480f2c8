import type { ApiResponse, PaginatedResponse, Music } from '~/types'

export const useHistoryApi = () => {
  const { get, post, delete: del } = useApi()

  // 获取播放历史
  const getPlayHistory = async (params?: {
    page?: number
    limit?: number
    startDate?: string
    endDate?: string
    playSource?: string
    isCompleted?: boolean
  }): Promise<ApiResponse<PaginatedResponse<{
    id: string
    musicId: Music
    playlistId?: string
    startTime: string
    endTime?: string
    duration: number
    isCompleted: boolean
    playSource: string
    deviceInfo?: string
    ipAddress?: string
  }>>> => {
    return await get('/history', params)
  }

  // 获取最近播放
  const getRecentlyPlayed = async (params?: {
    limit?: number
  }): Promise<ApiResponse<Music[]>> => {
    return await get('/history/recent', params)
  }

  // 获取播放统计
  const getPlayStats = async (params?: {
    period?: 'day' | 'week' | 'month' | 'year'
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<{
    totalPlays: number
    totalPlayTime: number
    completedPlays: number
    averagePlayTime: number
    topGenres: Array<{ genre: string; count: number }>
    topArtists: Array<{ artist: string; count: number }>
    dailyStats: Array<{ date: string; plays: number; playTime: number }>
  }>> => {
    return await get('/history/stats', params)
  }

  // 获取播放历史详情
  const getPlayHistoryDetail = async (historyId: string): Promise<ApiResponse<{
    id: string
    musicId: Music
    playlistId?: string
    startTime: string
    endTime?: string
    duration: number
    isCompleted: boolean
    playSource: string
    deviceInfo?: string
    ipAddress?: string
  }>> => {
    return await get(`/history/${historyId}`)
  }

  // 删除播放历史记录
  const deletePlayHistory = async (historyId: string): Promise<ApiResponse<{ deletedId: string }>> => {
    return await del(`/history/${historyId}`)
  }

  // 批量删除播放历史记录
  const batchDeletePlayHistory = async (params: {
    historyIds?: string[]
    deleteAll?: boolean
    beforeDate?: string
  }): Promise<ApiResponse<{ deletedCount: number }>> => {
    return await del('/history', params)
  }

  // 获取播放历史图表数据
  const getPlayHistoryChart = async (params?: {
    period?: 'week' | 'month' | 'year'
    chartType?: 'plays' | 'time' | 'completion'
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<{
    period: string
    chartType: string
    chartData: Array<{
      date: string
      playCount: number
      completedCount: number
      totalPlayTime: number
    }>
    summary: {
      totalDays: number
      totalPlays: number
      totalCompletedPlays: number
      totalPlayTime: number
    }
  }>> => {
    return await get('/history/chart', params)
  }

  return {
    getPlayHistory,
    getRecentlyPlayed,
    getPlayStats,
    getPlayHistoryDetail,
    deletePlayHistory,
    batchDeletePlayHistory,
    getPlayHistoryChart
  }
}
