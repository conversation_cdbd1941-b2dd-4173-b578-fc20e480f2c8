import type { ApiResponse, PaginatedResponse, User, Comment } from '~/types'

export const useSocialApi = () => {
  const { api } = useApi()

  // 关注系统 API
  
  // 关注用户
  const followUser = async (userId: string, source?: string): Promise<ApiResponse<{
    id: string
    followerId: string
    followingId: string
    source?: string
    createdAt: string
  }>> => {
    return await api(`/follows/${userId}`, {
      method: 'POST',
      body: { source }
    })
  }

  // 取消关注用户
  const unfollowUser = async (userId: string): Promise<ApiResponse<{ message: string }>> => {
    return await api(`/follows/${userId}`, {
      method: 'DELETE'
    })
  }

  // 获取用户关注列表
  const getUserFollowing = async (userId: string, params?: {
    page?: number
    limit?: number
    search?: string
  }): Promise<ApiResponse<PaginatedResponse<User>>> => {
    const query = new URLSearchParams()
    if (params?.page) query.append('page', params.page.toString())
    if (params?.limit) query.append('limit', params.limit.toString())
    if (params?.search) query.append('search', params.search)
    
    return await api(`/follows/${userId}/following?${query.toString()}`)
  }

  // 获取用户粉丝列表
  const getUserFollowers = async (userId: string, params?: {
    page?: number
    limit?: number
    search?: string
  }): Promise<ApiResponse<PaginatedResponse<User>>> => {
    const query = new URLSearchParams()
    if (params?.page) query.append('page', params.page.toString())
    if (params?.limit) query.append('limit', params.limit.toString())
    if (params?.search) query.append('search', params.search)
    
    return await api(`/follows/${userId}/followers?${query.toString()}`)
  }

  // 检查关注状态
  const checkFollowStatus = async (userId: string): Promise<ApiResponse<{
    isFollowing: boolean
    isFollowed: boolean
  }>> => {
    return await api(`/follows/${userId}/status`)
  }

  // 获取互相关注的用户
  const getMutualFollows = async (userId: string, params?: {
    page?: number
    limit?: number
  }): Promise<ApiResponse<PaginatedResponse<User>>> => {
    const query = new URLSearchParams()
    if (params?.page) query.append('page', params.page.toString())
    if (params?.limit) query.append('limit', params.limit.toString())
    
    return await api(`/follows/${userId}/mutual?${query.toString()}`)
  }

  // 评论系统 API

  // 发表评论
  const createComment = async (data: {
    targetType: 'music' | 'playlist'
    targetId: string
    content: string
    parentId?: string
  }): Promise<ApiResponse<Comment>> => {
    return await api('/comments', {
      method: 'POST',
      body: data
    })
  }

  // 获取评论列表
  const getComments = async (targetType: string, targetId: string, params?: {
    page?: number
    limit?: number
    sortBy?: 'newest' | 'oldest' | 'hot' | 'likes'
    includeReplies?: boolean
  }): Promise<ApiResponse<PaginatedResponse<Comment>>> => {
    const query = new URLSearchParams()
    query.append('targetType', targetType)
    query.append('targetId', targetId)
    if (params?.page) query.append('page', params.page.toString())
    if (params?.limit) query.append('limit', params.limit.toString())
    if (params?.sortBy) query.append('sortBy', params.sortBy)
    if (params?.includeReplies !== undefined) query.append('includeReplies', params.includeReplies.toString())
    
    return await api(`/comments?${query.toString()}`)
  }

  // 更新评论
  const updateComment = async (commentId: string, content: string): Promise<ApiResponse<Comment>> => {
    return await api(`/comments/${commentId}`, {
      method: 'PUT',
      body: { content }
    })
  }

  // 删除评论
  const deleteComment = async (commentId: string): Promise<ApiResponse<{ message: string }>> => {
    return await api(`/comments/${commentId}`, {
      method: 'DELETE'
    })
  }

  // 获取用户评论
  const getUserComments = async (userId: string, params?: {
    page?: number
    limit?: number
    targetType?: 'music' | 'playlist'
  }): Promise<ApiResponse<PaginatedResponse<Comment>>> => {
    const query = new URLSearchParams()
    if (params?.page) query.append('page', params.page.toString())
    if (params?.limit) query.append('limit', params.limit.toString())
    if (params?.targetType) query.append('targetType', params.targetType)
    
    return await api(`/comments/user/${userId}?${query.toString()}`)
  }

  // 点赞系统 API

  // 点赞目标
  const likeTarget = async (targetType: 'music' | 'comment' | 'playlist', targetId: string): Promise<ApiResponse<{
    id: string
    userId: string
    targetType: string
    targetId: string
    createdAt: string
  }>> => {
    return await api('/likes', {
      method: 'POST',
      body: { targetType, targetId }
    })
  }

  // 取消点赞
  const unlikeTarget = async (targetType: 'music' | 'comment' | 'playlist', targetId: string): Promise<ApiResponse<{ message: string }>> => {
    return await api('/likes', {
      method: 'DELETE',
      body: { targetType, targetId }
    })
  }

  // 获取用户点赞列表
  const getUserLikes = async (userId: string, params?: {
    page?: number
    limit?: number
    targetType?: 'music' | 'comment' | 'playlist'
  }): Promise<ApiResponse<PaginatedResponse<any>>> => {
    console.log('=== useSocialApi.getUserLikes 被调用 ===')
    console.log('参数:', { userId, params })

    const query = new URLSearchParams()
    if (params?.page) query.append('page', params.page.toString())
    if (params?.limit) query.append('limit', params.limit.toString())
    if (params?.targetType) query.append('targetType', params.targetType)

    const url = `/likes/user/${userId}?${query.toString()}`
    console.log('请求URL:', url)

    try {
      const response = await api(url)
      console.log('API响应成功:', response)
      return response
    } catch (error) {
      console.error('API请求失败:', error)
      throw error
    }
  }

  // 检查点赞状态
  const checkLikeStatus = async (targetType: string, targetId: string): Promise<ApiResponse<{
    isLiked: boolean
    likeCount: number
  }>> => {
    return await api(`/likes/check?targetType=${targetType}&targetId=${targetId}`)
  }

  // 分享系统 API

  // 分享目标
  const shareTarget = async (targetType: 'music' | 'playlist', targetId: string, platform?: string): Promise<ApiResponse<{
    id: string
    userId: string
    targetType: string
    targetId: string
    platform?: string
    shareUrl: string
    createdAt: string
  }>> => {
    return await api('/shares', {
      method: 'POST',
      body: { targetType, targetId, platform }
    })
  }

  // 获取分享链接
  const getShareUrl = async (targetType: string, targetId: string): Promise<ApiResponse<{
    shareUrl: string
    shortUrl: string
  }>> => {
    return await api(`/shares/url?targetType=${targetType}&targetId=${targetId}`)
  }

  // 获取用户分享列表
  const getUserShares = async (userId: string, params?: {
    page?: number
    limit?: number
    targetType?: 'music' | 'playlist'
  }): Promise<ApiResponse<PaginatedResponse<any>>> => {
    const query = new URLSearchParams()
    if (params?.page) query.append('page', params.page.toString())
    if (params?.limit) query.append('limit', params.limit.toString())
    if (params?.targetType) query.append('targetType', params.targetType)

    return await api(`/shares/user/${userId}?${query.toString()}`)
  }

  // 动态系统 API

  // 发布动态
  const createActivity = async (data: {
    type: 'music_upload' | 'playlist_create' | 'music_like' | 'playlist_like' | 'follow' | 'comment'
    content?: string
    targetId?: string
    targetType?: string
    metadata?: Record<string, any>
  }): Promise<ApiResponse<{
    id: string
    userId: string
    type: string
    content?: string
    targetId?: string
    targetType?: string
    metadata?: Record<string, any>
    createdAt: string
  }>> => {
    return await api('/activities', {
      method: 'POST',
      body: data
    })
  }

  // 获取用户动态时间线
  const getActivityFeed = async (params?: {
    page?: number
    limit?: number
    type?: string
    includeFollowing?: boolean
  }): Promise<ApiResponse<PaginatedResponse<any>>> => {
    const query = new URLSearchParams()
    if (params?.page) query.append('page', params.page.toString())
    if (params?.limit) query.append('limit', params.limit.toString())
    if (params?.type) query.append('type', params.type)
    if (params?.includeFollowing !== undefined) query.append('includeFollowing', params.includeFollowing.toString())

    return await api(`/activities/feed?${query.toString()}`)
  }

  // 获取用户动态
  const getUserActivities = async (userId: string, params?: {
    page?: number
    limit?: number
    type?: string
  }): Promise<ApiResponse<PaginatedResponse<any>>> => {
    const query = new URLSearchParams()
    if (params?.page) query.append('page', params.page.toString())
    if (params?.limit) query.append('limit', params.limit.toString())
    if (params?.type) query.append('type', params.type)

    return await api(`/activities/user/${userId}?${query.toString()}`)
  }

  // 删除动态
  const deleteActivity = async (activityId: string): Promise<ApiResponse<{ message: string }>> => {
    return await api(`/activities/${activityId}`, {
      method: 'DELETE'
    })
  }

  // 获取热门动态
  const getTrendingActivities = async (params?: {
    page?: number
    limit?: number
    timeRange?: 'day' | 'week' | 'month'
  }): Promise<ApiResponse<PaginatedResponse<any>>> => {
    const query = new URLSearchParams()
    if (params?.page) query.append('page', params.page.toString())
    if (params?.limit) query.append('limit', params.limit.toString())
    if (params?.timeRange) query.append('timeRange', params.timeRange)

    return await api(`/activities/trending?${query.toString()}`)
  }

  return {
    // 关注系统
    followUser,
    unfollowUser,
    getUserFollowing,
    getUserFollowers,
    checkFollowStatus,
    getMutualFollows,

    // 评论系统
    createComment,
    getComments,
    updateComment,
    deleteComment,
    getUserComments,

    // 点赞系统
    likeTarget,
    unlikeTarget,
    getUserLikes,
    checkLikeStatus,

    // 分享系统
    shareTarget,
    getShareUrl,
    getUserShares,

    // 动态系统
    createActivity,
    getActivityFeed,
    getUserActivities,
    deleteActivity,
    getTrendingActivities
  }
}
