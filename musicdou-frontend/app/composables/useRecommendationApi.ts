import type { 
  ApiResponse, 
  Music, 
  Playlist, 
  User, 
  RecommendationCategory,
  UserPreferences,
  RecommendationRequest
} from '~/types'

export const useRecommendationApi = () => {
  const { $api } = useNuxtApp()

  /**
   * 获取个性化推荐音乐
   */
  const getPersonalizedRecommendations = async (
    params: RecommendationRequest = {}
  ): Promise<ApiResponse<Music[]>> => {
    try {
      const response = await $api('/api/recommendations/for-you', {
        method: 'GET',
        params: {
          limit: params.limit || 20,
          offset: params.offset || 0,
          genres: params.genres?.join(','),
          excludeGenres: params.excludeGenres?.join(','),
          includeExplicit: params.includeExplicit,
          mood: params.mood,
          activity: params.activity
        }
      })
      return response
    } catch (error) {
      console.error('获取个性化推荐失败:', error)
      throw error
    }
  }

  /**
   * 获取新发布音乐
   */
  const getNewReleases = async (
    params: RecommendationRequest = {}
  ): Promise<ApiResponse<Music[]>> => {
    try {
      const response = await $api('/api/recommendations/new-releases', {
        method: 'GET',
        params: {
          limit: params.limit || 20,
          offset: params.offset || 0,
          genres: params.genres?.join(','),
          timeRange: params.timeRange || 'week'
        }
      })
      return response
    } catch (error) {
      console.error('获取新发布音乐失败:', error)
      throw error
    }
  }

  /**
   * 获取热门音乐
   */
  const getTrendingMusic = async (
    params: RecommendationRequest = {}
  ): Promise<ApiResponse<Music[]>> => {
    try {
      const response = await $api('/api/recommendations/trending', {
        method: 'GET',
        params: {
          limit: params.limit || 20,
          offset: params.offset || 0,
          timeRange: params.timeRange || 'week',
          genres: params.genres?.join(',')
        }
      })
      return response
    } catch (error) {
      console.error('获取热门音乐失败:', error)
      throw error
    }
  }

  /**
   * 获取推荐分类
   */
  const getRecommendationCategories = async (): Promise<ApiResponse<RecommendationCategory[]>> => {
    try {
      const response = await $api('/api/recommendations/categories', {
        method: 'GET'
      })
      return response
    } catch (error) {
      console.error('获取推荐分类失败:', error)
      throw error
    }
  }

  /**
   * 获取分类推荐内容
   */
  const getCategoryRecommendations = async (
    categoryId: string,
    params: RecommendationRequest = {}
  ): Promise<ApiResponse<Music[]>> => {
    try {
      const response = await $api(`/api/recommendations/categories/${categoryId}`, {
        method: 'GET',
        params: {
          limit: params.limit || 20,
          offset: params.offset || 0,
          personalized: params.personalized
        }
      })
      return response
    } catch (error) {
      console.error(`获取分类 ${categoryId} 推荐失败:`, error)
      throw error
    }
  }

  /**
   * 获取相似艺术家
   */
  const getSimilarArtists = async (
    artistId?: string,
    params: { limit?: number } = {}
  ): Promise<ApiResponse<User[]>> => {
    try {
      const endpoint = artistId 
        ? `/api/recommendations/similar-artists/${artistId}`
        : '/api/recommendations/similar-artists'
      
      const response = await $api(endpoint, {
        method: 'GET',
        params: {
          limit: params.limit || 10
        }
      })
      return response
    } catch (error) {
      console.error('获取相似艺术家失败:', error)
      throw error
    }
  }

  /**
   * 获取推荐歌单
   */
  const getRecommendedPlaylists = async (
    params: RecommendationRequest = {}
  ): Promise<ApiResponse<Playlist[]>> => {
    try {
      const response = await $api('/api/recommendations/playlists', {
        method: 'GET',
        params: {
          limit: params.limit || 15,
          offset: params.offset || 0,
          personalized: params.personalized,
          genres: params.genres?.join(',')
        }
      })
      return response
    } catch (error) {
      console.error('获取推荐歌单失败:', error)
      throw error
    }
  }

  /**
   * 获取基于音乐的推荐
   */
  const getMusicBasedRecommendations = async (
    musicId: string,
    params: { limit?: number } = {}
  ): Promise<ApiResponse<Music[]>> => {
    try {
      const response = await $api(`/api/recommendations/music/${musicId}/similar`, {
        method: 'GET',
        params: {
          limit: params.limit || 10
        }
      })
      return response
    } catch (error) {
      console.error('获取基于音乐的推荐失败:', error)
      throw error
    }
  }

  /**
   * 获取基于歌单的推荐
   */
  const getPlaylistBasedRecommendations = async (
    playlistId: string,
    params: { limit?: number } = {}
  ): Promise<ApiResponse<Music[]>> => {
    try {
      const response = await $api(`/api/recommendations/playlists/${playlistId}/similar`, {
        method: 'GET',
        params: {
          limit: params.limit || 10
        }
      })
      return response
    } catch (error) {
      console.error('获取基于歌单的推荐失败:', error)
      throw error
    }
  }

  /**
   * 获取用户偏好
   */
  const getUserPreferences = async (): Promise<ApiResponse<UserPreferences>> => {
    try {
      const response = await $api('/api/user/preferences', {
        method: 'GET'
      })
      return response
    } catch (error) {
      console.error('获取用户偏好失败:', error)
      throw error
    }
  }

  /**
   * 更新用户偏好
   */
  const updateUserPreferences = async (
    preferences: Partial<UserPreferences>
  ): Promise<ApiResponse<UserPreferences>> => {
    try {
      const response = await $api('/api/user/preferences', {
        method: 'PUT',
        body: preferences
      })
      return response
    } catch (error) {
      console.error('更新用户偏好失败:', error)
      throw error
    }
  }

  /**
   * 获取心情推荐
   */
  const getMoodRecommendations = async (
    mood: string,
    params: RecommendationRequest = {}
  ): Promise<ApiResponse<Music[]>> => {
    try {
      const response = await $api(`/api/recommendations/mood/${mood}`, {
        method: 'GET',
        params: {
          limit: params.limit || 20,
          offset: params.offset || 0,
          personalized: params.personalized
        }
      })
      return response
    } catch (error) {
      console.error(`获取心情 ${mood} 推荐失败:`, error)
      throw error
    }
  }

  /**
   * 获取活动推荐
   */
  const getActivityRecommendations = async (
    activity: string,
    params: RecommendationRequest = {}
  ): Promise<ApiResponse<Music[]>> => {
    try {
      const response = await $api(`/api/recommendations/activity/${activity}`, {
        method: 'GET',
        params: {
          limit: params.limit || 20,
          offset: params.offset || 0,
          personalized: params.personalized
        }
      })
      return response
    } catch (error) {
      console.error(`获取活动 ${activity} 推荐失败:`, error)
      throw error
    }
  }

  /**
   * 获取时间段推荐
   */
  const getTimeBasedRecommendations = async (
    timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night',
    params: RecommendationRequest = {}
  ): Promise<ApiResponse<Music[]>> => {
    try {
      const response = await $api(`/api/recommendations/time/${timeOfDay}`, {
        method: 'GET',
        params: {
          limit: params.limit || 20,
          offset: params.offset || 0,
          personalized: params.personalized
        }
      })
      return response
    } catch (error) {
      console.error(`获取时间段 ${timeOfDay} 推荐失败:`, error)
      throw error
    }
  }

  /**
   * 记录用户行为（用于改进推荐）
   */
  const recordUserAction = async (action: {
    type: 'play' | 'like' | 'skip' | 'share' | 'add_to_playlist'
    musicId?: string
    playlistId?: string
    artistId?: string
    duration?: number
    context?: string
  }): Promise<ApiResponse<void>> => {
    try {
      const response = await $api('/api/recommendations/actions', {
        method: 'POST',
        body: {
          ...action,
          timestamp: new Date().toISOString()
        }
      })
      return response
    } catch (error) {
      console.error('记录用户行为失败:', error)
      throw error
    }
  }

  /**
   * 获取推荐统计信息
   */
  const getRecommendationStats = async (): Promise<ApiResponse<{
    totalRecommendations: number
    accuracyRate: number
    topGenres: string[]
    recentActivity: any[]
  }>> => {
    try {
      const response = await $api('/api/recommendations/stats', {
        method: 'GET'
      })
      return response
    } catch (error) {
      console.error('获取推荐统计失败:', error)
      throw error
    }
  }

  /**
   * 刷新推荐缓存
   */
  const refreshRecommendationCache = async (): Promise<ApiResponse<void>> => {
    try {
      const response = await $api('/api/recommendations/refresh', {
        method: 'POST'
      })
      return response
    } catch (error) {
      console.error('刷新推荐缓存失败:', error)
      throw error
    }
  }

  return {
    // 基础推荐
    getPersonalizedRecommendations,
    getNewReleases,
    getTrendingMusic,
    getRecommendationCategories,
    getCategoryRecommendations,
    getSimilarArtists,
    getRecommendedPlaylists,
    
    // 基于内容的推荐
    getMusicBasedRecommendations,
    getPlaylistBasedRecommendations,
    
    // 用户偏好
    getUserPreferences,
    updateUserPreferences,
    
    // 情境推荐
    getMoodRecommendations,
    getActivityRecommendations,
    getTimeBasedRecommendations,
    
    // 行为记录和统计
    recordUserAction,
    getRecommendationStats,
    refreshRecommendationCache
  }
}
