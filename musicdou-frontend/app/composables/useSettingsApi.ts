import type { ApiResponse } from '~/types'

export interface UserSettings {
  notifications: {
    email: boolean
    push: boolean
    newMusic: boolean
    comments: boolean
    follows: boolean
  }
  privacy: {
    profileVisibility: 'public' | 'followers' | 'private'
    publicPlaylists: boolean
    showActivity: boolean
  }
  playback: {
    quality: 'low' | 'medium' | 'high'
    autoplay: boolean
    crossfade: boolean
    volume: number
  }
  appearance: {
    theme: 'system' | 'light' | 'dark'
    language: string
  }
}

export const useSettingsApi = () => {
  const { get, put } = useApi()

  // 获取用户设置
  const getUserSettings = async (): Promise<ApiResponse<UserSettings>> => {
    return await get('/settings')
  }

  // 更新用户设置
  const updateUserSettings = async (settings: Partial<UserSettings>): Promise<ApiResponse<UserSettings>> => {
    return await put('/settings', settings)
  }

  // 重置设置为默认值
  const resetSettings = async (): Promise<ApiResponse<UserSettings>> => {
    return await put('/settings/reset')
  }

  return {
    getUserSettings,
    updateUserSettings,
    resetSettings
  }
}
