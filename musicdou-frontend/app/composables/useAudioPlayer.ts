import { Howl, Howler } from 'howler'
import type { Music } from '~/types'

// 全局音频实例和定时器（确保在所有调用之间共享）
let globalCurrentHowl: Howl | null = null
let globalProgressTimer: NodeJS.Timeout | null = null

export const useAudioPlayer = () => {
  const playerStore = usePlayerStore()

  // 初始化音频播放器
  const initPlayer = () => {
    // 设置全局音量
    Howler.volume(playerStore.state.volume)
  }

  // 加载音频
  const loadTrack = (track: Music): Promise<void> => {
    return new Promise((resolve, reject) => {
      // 停止当前播放
      if (globalCurrentHowl) {
        globalCurrentHowl.stop()
        globalCurrentHowl.unload()
      }

      // 创建新的音频实例
      globalCurrentHowl = new Howl({
        src: [track.url],
        html5: true,
        preload: true,
        volume: playerStore.state.volume,
        
        onload: () => {
          if (globalCurrentHowl) {
            const duration = globalCurrentHowl.duration()
            playerStore.setDuration(duration)
            playerStore.setCurrentTrack(track)
            resolve()
          }
        },
        
        onloaderror: (id, error) => {
          console.error('音频加载失败:', error)
          reject(new Error('音频加载失败'))
        },
        
        onplay: () => {
          playerStore.setPlaying(true)
          startProgressTimer()
        },
        
        onpause: () => {
          playerStore.setPlaying(false)
          stopProgressTimer()
        },
        
        onstop: () => {
          playerStore.setPlaying(false)
          playerStore.setCurrentTime(0)
          stopProgressTimer()
        },
        
        onend: () => {
          playerStore.setPlaying(false)
          stopProgressTimer()
          
          // 根据播放模式决定下一步操作
          if (playerStore.state.repeat === 'one') {
            // 单曲循环
            play()
          } else if (playerStore.canPlayNext) {
            // 播放下一首
            playerStore.playNext()
          } else {
            // 播放结束
            playerStore.stop()
          }
        },
        
        onseek: () => {
          if (globalCurrentHowl) {
            const currentTime = globalCurrentHowl.seek()
            if (typeof currentTime === 'number') {
              playerStore.setCurrentTime(currentTime)
            }
          }
        }
      })
    })
  }

  // 播放
  const play = () => {
    if (globalCurrentHowl && !globalCurrentHowl.playing()) {
      globalCurrentHowl.play()
    }
  }

  // 暂停
  const pause = () => {
    if (globalCurrentHowl && globalCurrentHowl.playing()) {
      globalCurrentHowl.pause()
    }
  }

  // 停止
  const stop = () => {
    if (globalCurrentHowl) {
      globalCurrentHowl.stop()
    }
  }

  // 设置音量
  const setVolume = (volume: number) => {
    const normalizedVolume = Math.max(0, Math.min(1, volume))
    playerStore.setVolume(normalizedVolume)

    // 设置全局音量
    Howler.volume(normalizedVolume)

    // 设置当前音频音量
    if (globalCurrentHowl) {
      globalCurrentHowl.volume(normalizedVolume)
    }
  }

  // 跳转到指定时间
  const seekTo = (time: number) => {
    if (globalCurrentHowl) {
      const duration = globalCurrentHowl.duration()
      const seekTime = Math.max(0, Math.min(duration, time))
      globalCurrentHowl.seek(seekTime)
      playerStore.setCurrentTime(seekTime)
    }
  }

  // 开始进度更新定时器
  const startProgressTimer = () => {
    stopProgressTimer()
    globalProgressTimer = setInterval(() => {
      if (globalCurrentHowl && globalCurrentHowl.playing()) {
        const currentTime = globalCurrentHowl.seek()
        if (typeof currentTime === 'number') {
          playerStore.setCurrentTime(currentTime)
        }
      }
    }, 250) // 更新频率提高到250ms，使进度条更流畅
  }

  // 停止进度更新定时器
  const stopProgressTimer = () => {
    if (globalProgressTimer) {
      clearInterval(globalProgressTimer)
      globalProgressTimer = null
    }
  }

  // 播放指定歌曲
  const playTrack = async (track: Music, queue?: Music[]) => {
    try {
      // 更新播放器状态
      playerStore.playTrack(track, queue)
      
      // 加载并播放音频
      await loadTrack(track)
      play()
    } catch (error) {
      console.error('播放失败:', error)
      // 可以在这里添加错误处理，比如显示通知
    }
  }

  // 切换播放/暂停
  const togglePlay = () => {
    if (playerStore.state.isPlaying) {
      pause()
    } else {
      play()
    }
  }

  // 播放下一首
  const playNext = async () => {
    if (playerStore.canPlayNext) {
      playerStore.playNext()
      if (playerStore.state.currentTrack) {
        await loadTrack(playerStore.state.currentTrack)
        play()
      }
    }
  }

  // 播放上一首
  const playPrevious = async () => {
    if (playerStore.canPlayPrevious) {
      playerStore.playPrevious()
      if (playerStore.state.currentTrack) {
        await loadTrack(playerStore.state.currentTrack)
        play()
      }
    }
  }

  // 清理资源
  const cleanup = () => {
    stopProgressTimer()
    if (currentHowl) {
      currentHowl.stop()
      currentHowl.unload()
      currentHowl = null
    }
  }

  // 监听播放器状态变化
  watch(() => playerStore.state.currentTrack, async (newTrack, oldTrack) => {
    if (newTrack && newTrack.id !== oldTrack?.id) {
      await loadTrack(newTrack)
      if (playerStore.state.isPlaying) {
        play()
      }
    }
  })

  // 监听播放状态变化
  watch(() => playerStore.state.isPlaying, (isPlaying) => {
    if (isPlaying) {
      play()
    } else {
      pause()
    }
  })

  // 监听音量变化
  watch(() => playerStore.state.volume, (volume) => {
    setVolume(volume)
  })

  // 组件卸载时清理资源
  onUnmounted(() => {
    cleanup()
  })

  // 初始化
  onMounted(() => {
    initPlayer()
  })

  return {
    // 播放控制
    playTrack,
    togglePlay,
    playNext,
    playPrevious,
    stop,
    
    // 音频控制
    setVolume,
    seekTo,
    
    // 工具方法
    cleanup
  }
}
