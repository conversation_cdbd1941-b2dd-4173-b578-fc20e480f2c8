export const useTheme = () => {
  const colorMode = useColorMode()

  // 获取当前主题
  const currentTheme = computed(() => colorMode.value)

  // 切换主题
  const toggleTheme = () => {
    colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'
  }

  // 设置主题
  const setTheme = (theme: string) => {
    colorMode.preference = theme
  }

  // 是否为深色主题
  const isDark = computed(() => colorMode.value === 'dark')

  // 是否为浅色主题
  const isLight = computed(() => colorMode.value === 'light')

  // 主题图标
  const themeIcon = computed(() => {
    switch (colorMode.value) {
      case 'dark':
        return 'MoonIcon'
      case 'light':
        return 'SunIcon'
      default:
        return 'ComputerDesktopIcon'
    }
  })

  // 主题名称
  const themeName = computed(() => {
    switch (colorMode.value) {
      case 'dark':
        return '深色模式'
      case 'light':
        return '浅色模式'
      default:
        return '跟随系统'
    }
  })

  return {
    currentTheme,
    toggleTheme,
    setTheme,
    isDark,
    isLight,
    themeIcon,
    themeName
  }
}
