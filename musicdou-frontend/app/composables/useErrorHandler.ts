import type { AppError } from '~/types'

export const useErrorHandler = () => {
  // 错误类型映射
  const errorMessages: Record<string, string> = {
    // 认证错误
    'INVALID_CREDENTIALS': '用户名或密码错误',
    'USER_NOT_FOUND': '用户不存在',
    'EMAIL_ALREADY_EXISTS': '邮箱已被注册',
    'USERNAME_ALREADY_EXISTS': '用户名已被使用',
    'INVALID_TOKEN': '无效的令牌',
    'TOKEN_EXPIRED': '令牌已过期，请重新登录',
    'ACCESS_DENIED': '访问被拒绝',
    'ACCOUNT_DISABLED': '账户已被禁用',
    'EMAIL_NOT_VERIFIED': '邮箱未验证',
    
    // 音乐相关错误
    'MUSIC_NOT_FOUND': '音乐不存在',
    'INVALID_MUSIC_FORMAT': '不支持的音乐格式',
    'MUSIC_TOO_LARGE': '音乐文件过大',
    'UPLOAD_FAILED': '上传失败',
    'MUSIC_ALREADY_EXISTS': '音乐已存在',
    
    // 歌单相关错误
    'PLAYLIST_NOT_FOUND': '歌单不存在',
    'PLAYLIST_ACCESS_DENIED': '无权访问此歌单',
    'SONG_ALREADY_IN_PLAYLIST': '歌曲已在歌单中',
    'SONG_NOT_IN_PLAYLIST': '歌曲不在歌单中',
    'PLAYLIST_LIMIT_EXCEEDED': '歌单数量已达上限',
    
    // 社交相关错误
    'USER_ALREADY_FOLLOWED': '已关注该用户',
    'USER_NOT_FOLLOWED': '未关注该用户',
    'CANNOT_FOLLOW_SELF': '不能关注自己',
    'COMMENT_NOT_FOUND': '评论不存在',
    'COMMENT_ACCESS_DENIED': '无权操作此评论',
    
    // 通用错误
    'VALIDATION_ERROR': '输入数据验证失败',
    'NETWORK_ERROR': '网络连接错误',
    'SERVER_ERROR': '服务器内部错误',
    'RATE_LIMIT_EXCEEDED': '请求过于频繁，请稍后再试',
    'INSUFFICIENT_PERMISSIONS': '权限不足',
    'RESOURCE_NOT_FOUND': '资源不存在',
    'OPERATION_FAILED': '操作失败'
  }

  // 处理API错误
  const handleApiError = (error: any): AppError => {
    console.error('API错误:', error)

    // 如果是网络错误
    if (!error.response) {
      return {
        code: 'NETWORK_ERROR',
        message: errorMessages.NETWORK_ERROR || '网络连接错误',
        details: error
      }
    }

    // 从响应中提取错误信息
    const { status, data } = error.response

    // 处理不同的错误格式
    let errorCode: string
    let errorMessage: string

    if (data?.error) {
      // 后端返回的标准错误格式
      errorCode = data.error
      errorMessage = data.message || errorMessages[data.error] || '未知错误'
    } else if (data?.code) {
      // 备用错误格式
      errorCode = data.code
      errorMessage = data.message || errorMessages[data.code] || '未知错误'
    } else {
      // HTTP状态码错误
      errorCode = `HTTP_${status}`
      errorMessage = data?.message || getHttpStatusMessage(status)
    }

    return {
      code: errorCode,
      message: errorMessage,
      details: data
    }
  }

  // 获取HTTP状态码对应的错误消息
  const getHttpStatusMessage = (status: number): string => {
    const statusMessages: Record<number, string> = {
      400: '请求参数错误',
      401: '未授权访问',
      403: '权限不足',
      404: '资源不存在',
      409: '资源冲突',
      422: '数据验证失败',
      429: '请求过于频繁',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务不可用',
      504: '网关超时'
    }

    return statusMessages[status] || `HTTP错误 ${status}`
  }

  // 显示错误消息
  const showError = (error: AppError | string) => {
    const message = typeof error === 'string' ? error : error.message
    
    // 这里可以集成toast通知组件
    console.error('错误:', message)
    
    // 如果有全局通知系统，在这里调用
    // useNotification().error(message)
  }

  // 处理表单验证错误
  const handleValidationError = (errors: Record<string, string[]>): string => {
    const firstError = Object.values(errors)[0]
    return firstError ? firstError[0] : '表单验证失败'
  }

  // 重试机制
  const withRetry = async <T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> => {
    let lastError: any

    for (let i = 0; i < maxRetries; i++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        
        // 如果是最后一次重试，直接抛出错误
        if (i === maxRetries - 1) {
          throw error
        }
        
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
      }
    }

    throw lastError
  }

  // 检查是否为特定错误类型
  const isErrorType = (error: any, type: string): boolean => {
    if (typeof error === 'string') {
      return error === type
    }
    
    if (error?.code) {
      return error.code === type
    }
    
    if (error?.response?.data?.error) {
      return error.response.data.error === type
    }
    
    return false
  }

  // 获取用户友好的错误消息
  const getFriendlyMessage = (error: any): string => {
    if (typeof error === 'string') {
      return errorMessages[error] || error
    }
    
    if (error?.message) {
      return error.message
    }
    
    if (error?.response?.data?.message) {
      return error.response.data.message
    }
    
    return '发生了未知错误'
  }

  // 记录错误到日志服务
  const logError = (error: AppError, context?: string) => {
    const logData = {
      error,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }
    
    // 这里可以发送到日志服务
    console.error('错误日志:', logData)
  }

  // 处理异步操作的错误
  const safeAsync = async <T>(
    operation: () => Promise<T>,
    fallback?: T
  ): Promise<T | undefined> => {
    try {
      return await operation()
    } catch (error) {
      const appError = handleApiError(error)
      showError(appError)
      logError(appError)
      return fallback
    }
  }

  return {
    handleApiError,
    getHttpStatusMessage,
    showError,
    handleValidationError,
    withRetry,
    isErrorType,
    getFriendlyMessage,
    logError,
    safeAsync,
    errorMessages
  }
}
