// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  avatar?: string
  bio?: string
  userGroup: 'normal' | 'vip' | 'admin'
  points: number
  followersCount: number
  followingCount: number
  isActive: boolean
  lastLoginAt?: string
  createdAt: string
  updatedAt: string
}

export interface UserProfile extends User {
  isFollowing?: boolean
  isFollowed?: boolean
}

export interface UserStats {
  totalPlays: number
  totalLikes: number
  totalShares: number
  totalComments: number
  totalPlaylists: number
  totalFollowers: number
  totalFollowing: number
}

// 音乐相关类型
export interface Music {
  id: string
  title: string
  artist: string
  album?: string
  duration: number
  url: string
  coverUrl?: string
  genre?: string
  tags?: string[]
  language?: string
  quality: 'low' | 'medium' | 'high' | 'lossless'
  fileSize: number
  format: string
  releaseDate?: string
  playCount: number
  likeCount: number
  shareCount: number
  commentCount: number
  isLiked?: boolean
  isPublic: boolean
  status: 'active' | 'pending' | 'rejected' | 'deleted'
  uploaderId: string
  uploader?: User
  createdAt: string
  updatedAt: string
}

export interface MusicWithUser extends Music {
  user: User
}

export interface MusicUpload {
  title: string
  artist: string
  album?: string
  genre?: string
  tags?: string[]
  language?: string
  releaseDate?: string
  isPublic: boolean
  file: File
  cover?: File
}

// 歌单相关类型
export interface Playlist {
  id: string
  name: string
  description?: string
  coverUrl?: string
  isPublic: boolean
  songCount: number
  duration: number
  playCount: number
  likeCount: number
  isLiked?: boolean
  userId: string
  user?: User
  songs?: Music[]
  createdAt: string
  updatedAt: string
}

// 播放器相关类型
export interface PlayerState {
  currentTrack: Music | null
  isPlaying: boolean
  volume: number
  currentTime: number
  duration: number
  queue: Music[]
  currentIndex: number
  shuffle: boolean
  repeat: 'none' | 'one' | 'all'
}

// 评论相关类型
export interface Comment {
  id: string
  content: string
  userId: string
  user: User
  targetId: string
  targetType: 'music' | 'playlist'
  parentId?: string
  replies?: Comment[]
  likeCount: number
  isLiked?: boolean
  createdAt: string
  updatedAt: string
}

// 通知相关类型
export interface Notification {
  id: string
  type: 'like' | 'comment' | 'follow' | 'playlist_share'
  title: string
  message: string
  isRead: boolean
  userId: string
  fromUser?: User
  targetId?: string
  targetType?: string
  createdAt: string
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T = any> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// 认证相关类型
export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  username: string
  email: string
  password: string
  confirmPassword: string
  agreeToTerms: boolean
}

export interface AuthResponse {
  success: boolean
  message: string
  data: {
    user: User
    token: string
    refreshToken?: string
    expiresIn: number
  }
}

export interface AuthUser extends User {
  permissions?: string[]
  roles?: string[]
}

export interface PasswordResetRequest {
  email: string
}

export interface PasswordReset {
  token: string
  password: string
  confirmPassword: string
}

export interface ChangePassword {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

// 表单相关类型
export interface LoginForm {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  confirmPassword: string
  agreeToTerms: boolean
}

export interface PlaylistForm {
  name: string
  description?: string
  isPublic: boolean
  coverFile?: File
}

// 搜索相关类型
export interface SearchResult {
  music: Music[]
  playlists: Playlist[]
  users: User[]
}

export interface SearchFilters {
  type: 'all' | 'music' | 'playlists' | 'users'
  genre?: string
  sortBy: 'relevance' | 'date' | 'popularity'
  dateRange?: 'all' | 'today' | 'week' | 'month' | 'year'
  quality?: 'all' | 'low' | 'medium' | 'high' | 'lossless'
  language?: string
  duration?: {
    min?: number
    max?: number
  }
}

export interface SearchQuery {
  query: string
  filters: SearchFilters
  page: number
  limit: number
}

export interface SearchSuggestion {
  id: string
  text: string
  type: 'query' | 'artist' | 'album' | 'genre'
  count?: number
}

export interface SearchHistory {
  id: string
  query: string
  timestamp: string
  resultCount: number
}

export interface SearchState {
  query: string
  results: SearchResult | null
  suggestions: SearchSuggestion[]
  history: SearchHistory[]
  hotSearches: string[]
  filters: SearchFilters
  isLoading: boolean
  hasMore: boolean
  currentPage: number
  totalResults: number
  cache: Map<string, { data: SearchResult; timestamp: number }>
}

// 主题相关类型
export type ThemeMode = 'light' | 'dark' | 'system'

// 错误类型
export interface AppError {
  code: string
  message: string
  details?: any
}

// 上传相关类型
export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

export interface FileUploadResponse {
  url: string
  filename: string
  size: number
  type: string
}

// 推荐系统相关类型
export interface RecommendationCategory {
  id: string
  name: string
  description: string
  coverUrl?: string
  isPersonalized: boolean
  priority: number
  tags: string[]
}

export interface UserPreferences {
  favoriteGenres: string[]
  favoriteArtists: string[]
  preferredLanguages: string[]
  moodPreferences: string[]
  activityPreferences: string[]
  timeBasedPreferences: {
    morning: string[]
    afternoon: string[]
    evening: string[]
    night: string[]
  }
  explicitContentAllowed: boolean
  discoveryOpenness: number // 0-100, 探索新内容的开放程度
}

export interface RecommendationSettings {
  enablePersonalization: boolean
  includeExplicit: boolean
  preferredGenres: string[]
  excludedGenres: string[]
  discoveryLevel: 'conservative' | 'balanced' | 'adventurous'
  refreshFrequency: 'hourly' | 'daily' | 'weekly'
}

export interface RecommendationRequest {
  limit?: number
  offset?: number
  genres?: string[]
  excludeGenres?: string[]
  includeExplicit?: boolean
  personalized?: boolean
  timeRange?: 'day' | 'week' | 'month' | 'year'
  mood?: string
  activity?: string
}

// 语音识别相关类型
declare global {
  interface Window {
    SpeechRecognition: any
    webkitSpeechRecognition: any
  }
}
