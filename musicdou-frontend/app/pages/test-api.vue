<template>
  <div class="min-h-screen bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold mb-8">API 测试页面</h1>
      
      <!-- 注册测试 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">注册测试</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <input 
            v-model="registerForm.username" 
            placeholder="用户名" 
            class="border rounded px-3 py-2"
          />
          <input 
            v-model="registerForm.email" 
            placeholder="邮箱" 
            type="email"
            class="border rounded px-3 py-2"
          />
          <input 
            v-model="registerForm.password" 
            placeholder="密码" 
            type="password"
            class="border rounded px-3 py-2"
          />
        </div>
        <button 
          @click="testRegister" 
          :disabled="registerLoading"
          class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {{ registerLoading ? '注册中...' : '测试注册' }}
        </button>
        <div v-if="registerResult" class="mt-4 p-3 bg-gray-100 rounded">
          <pre>{{ JSON.stringify(registerResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- 登录测试 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">登录测试</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <input 
            v-model="loginForm.identifier" 
            placeholder="邮箱或用户名" 
            class="border rounded px-3 py-2"
          />
          <input 
            v-model="loginForm.password" 
            placeholder="密码" 
            type="password"
            class="border rounded px-3 py-2"
          />
        </div>
        <button 
          @click="testLogin" 
          :disabled="loginLoading"
          class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
        >
          {{ loginLoading ? '登录中...' : '测试登录' }}
        </button>
        <div v-if="loginResult" class="mt-4 p-3 bg-gray-100 rounded">
          <pre>{{ JSON.stringify(loginResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- 获取用户信息测试 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">获取用户信息测试</h2>
        <button 
          @click="testProfile" 
          :disabled="profileLoading"
          class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50"
        >
          {{ profileLoading ? '获取中...' : '获取用户信息' }}
        </button>
        <div v-if="profileResult" class="mt-4 p-3 bg-gray-100 rounded">
          <pre>{{ JSON.stringify(profileResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- 当前Token -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">当前Token</h2>
        <div class="p-3 bg-gray-100 rounded">
          <pre>{{ currentToken || '无Token' }}</pre>
        </div>
        <button 
          @click="clearToken" 
          class="mt-2 bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
        >
          清除Token
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: false
})

const { api } = useApi()

// 表单数据
const registerForm = ref({
  username: 'testuser' + Date.now(),
  email: 'test' + Date.now() + '@example.com',
  password: '123456'
})

const loginForm = ref({
  identifier: '',
  password: ''
})

// 状态
const registerLoading = ref(false)
const loginLoading = ref(false)
const profileLoading = ref(false)

// 结果
const registerResult = ref(null)
const loginResult = ref(null)
const profileResult = ref(null)

// 当前Token
const currentToken = useCookie('auth-token')

// 测试注册
const testRegister = async () => {
  registerLoading.value = true
  registerResult.value = null
  
  try {
    const result = await api('/auth/register', {
      method: 'POST',
      body: registerForm.value
    })
    registerResult.value = result
    
    // 自动填充登录表单
    loginForm.value.identifier = registerForm.value.email
    loginForm.value.password = registerForm.value.password
  } catch (error) {
    registerResult.value = { error: error.message, details: error }
  } finally {
    registerLoading.value = false
  }
}

// 测试登录
const testLogin = async () => {
  loginLoading.value = true
  loginResult.value = null
  
  try {
    const result = await api('/auth/login', {
      method: 'POST',
      body: loginForm.value
    })
    loginResult.value = result
  } catch (error) {
    loginResult.value = { error: error.message, details: error }
  } finally {
    loginLoading.value = false
  }
}

// 测试获取用户信息
const testProfile = async () => {
  profileLoading.value = true
  profileResult.value = null
  
  try {
    const result = await api('/auth/profile')
    profileResult.value = result
  } catch (error) {
    profileResult.value = { error: error.message, details: error }
  } finally {
    profileLoading.value = false
  }
}

// 清除Token
const clearToken = () => {
  currentToken.value = null
}
</script>
