<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <!-- 面包屑导航 -->
      <nav class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-6">
        <NuxtLink
          to="/"
          class="flex items-center hover:text-gray-700 dark:hover:text-gray-200 transition-colors"
        >
          <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
          </svg>
          首页
        </NuxtLink>
        <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
          <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
        </svg>
        <span class="text-gray-900 dark:text-white font-medium">设置</span>
      </nav>

      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          设置
        </h1>
        <p class="mt-2 text-gray-600 dark:text-gray-400">
          管理你的应用偏好设置和隐私选项
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 侧边栏导航 -->
        <div class="lg:col-span-1">
          <Card class="p-6">
            <nav class="space-y-2">
              <button
                v-for="tab in tabs"
                :key="tab.id"
                @click="activeTab = tab.id"
                :class="[
                  'w-full text-left px-3 py-2 rounded-lg transition-colors',
                  activeTab === tab.id
                    ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                ]"
              >
                <Icon :name="tab.icon" class="w-5 h-5 inline mr-3" />
                {{ tab.name }}
              </button>
            </nav>
          </Card>
        </div>

        <!-- 主内容区域 -->
        <div class="lg:col-span-2">
          <!-- 通知设置 -->
          <Card v-if="activeTab === 'notifications'" class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              通知设置
            </h2>
            
            <div class="space-y-6">
              <!-- 邮件通知 -->
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    邮件通知
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400">
                    接收重要更新和通知邮件
                  </p>
                </div>
                <ToggleSwitch v-model="settings.notifications.email" />
              </div>

              <!-- 推送通知 -->
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    推送通知
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400">
                    接收浏览器推送通知
                  </p>
                </div>
                <ToggleSwitch v-model="settings.notifications.push" />
              </div>

              <!-- 新音乐通知 -->
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    新音乐通知
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400">
                    关注的用户发布新音乐时通知我
                  </p>
                </div>
                <ToggleSwitch v-model="settings.notifications.newMusic" />
              </div>

              <!-- 评论通知 -->
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    评论通知
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400">
                    有人评论我的音乐时通知我
                  </p>
                </div>
                <ToggleSwitch v-model="settings.notifications.comments" />
              </div>

              <!-- 关注通知 -->
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    关注通知
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400">
                    有新用户关注我时通知我
                  </p>
                </div>
                <ToggleSwitch v-model="settings.notifications.follows" />
              </div>
            </div>
          </Card>

          <!-- 隐私设置 -->
          <Card v-if="activeTab === 'privacy'" class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              隐私设置
            </h2>
            
            <div class="space-y-6">
              <!-- 个人资料可见性 -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">
                  个人资料可见性
                </h3>
                <div class="space-y-3">
                  <label class="flex items-center">
                    <input
                      v-model="settings.privacy.profileVisibility"
                      type="radio"
                      value="public"
                      class="mr-3 text-primary-600"
                    >
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">公开</p>
                      <p class="text-sm text-gray-600 dark:text-gray-400">任何人都可以查看我的个人资料</p>
                    </div>
                  </label>
                  <label class="flex items-center">
                    <input
                      v-model="settings.privacy.profileVisibility"
                      type="radio"
                      value="followers"
                      class="mr-3 text-primary-600"
                    >
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">仅关注者</p>
                      <p class="text-sm text-gray-600 dark:text-gray-400">只有关注我的用户可以查看</p>
                    </div>
                  </label>
                  <label class="flex items-center">
                    <input
                      v-model="settings.privacy.profileVisibility"
                      type="radio"
                      value="private"
                      class="mr-3 text-primary-600"
                    >
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">私密</p>
                      <p class="text-sm text-gray-600 dark:text-gray-400">只有我自己可以查看</p>
                    </div>
                  </label>
                </div>
              </div>

              <!-- 播放列表可见性 -->
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    公开播放列表
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400">
                    允许其他用户查看我的播放列表
                  </p>
                </div>
                <ToggleSwitch v-model="settings.privacy.publicPlaylists" />
              </div>

              <!-- 活动可见性 -->
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    显示活动状态
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400">
                    显示我的在线状态和最近活动
                  </p>
                </div>
                <ToggleSwitch v-model="settings.privacy.showActivity" />
              </div>
            </div>
          </Card>

          <!-- 播放设置 -->
          <Card v-if="activeTab === 'playback'" class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              播放设置
            </h2>
            
            <div class="space-y-6">
              <!-- 音质设置 -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">
                  音质偏好
                </h3>
                <select
                  v-model="settings.playback.quality"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  <option value="low">标准音质 (128kbps)</option>
                  <option value="medium">高音质 (320kbps)</option>
                  <option value="high">无损音质 (FLAC)</option>
                </select>
              </div>

              <!-- 自动播放 -->
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    自动播放
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400">
                    歌曲结束后自动播放推荐音乐
                  </p>
                </div>
                <ToggleSwitch v-model="settings.playback.autoplay" />
              </div>

              <!-- 淡入淡出 -->
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    淡入淡出
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400">
                    歌曲切换时使用淡入淡出效果
                  </p>
                </div>
                <ToggleSwitch v-model="settings.playback.crossfade" />
              </div>

              <!-- 音量 -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">
                  默认音量
                </h3>
                <div class="flex items-center space-x-4">
                  <Icon name="volume-down" class="w-5 h-5 text-gray-400" />
                  <input
                    v-model="settings.playback.volume"
                    type="range"
                    min="0"
                    max="100"
                    class="flex-1"
                  >
                  <Icon name="volume-up" class="w-5 h-5 text-gray-400" />
                  <span class="text-sm text-gray-600 dark:text-gray-400 w-12">
                    {{ settings.playback.volume }}%
                  </span>
                </div>
              </div>
            </div>
          </Card>

          <!-- 外观设置 -->
          <Card v-if="activeTab === 'appearance'" class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              外观设置
            </h2>
            
            <div class="space-y-6">
              <!-- 主题设置 -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">
                  主题
                </h3>
                <div class="grid grid-cols-3 gap-3">
                  <button
                    v-for="theme in themes"
                    :key="theme.value"
                    @click="settings.appearance.theme = theme.value"
                    :class="[
                      'p-4 rounded-lg border-2 transition-colors',
                      settings.appearance.theme === theme.value
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    ]"
                  >
                    <Icon :name="theme.icon" class="w-8 h-8 mx-auto mb-2" />
                    <p class="text-sm font-medium">{{ theme.name }}</p>
                  </button>
                </div>
              </div>

              <!-- 语言设置 -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">
                  语言
                </h3>
                <select
                  v-model="settings.appearance.language"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  <option value="zh-CN">简体中文</option>
                  <option value="zh-TW">繁體中文</option>
                  <option value="en-US">English</option>
                  <option value="ja-JP">日本語</option>
                </select>
              </div>
            </div>
          </Card>

          <!-- 账户安全设置 -->
          <Card v-if="activeTab === 'security'" class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              账户安全
            </h2>

            <div class="space-y-8">
              <!-- 修改密码 -->
              <div class="border-b border-gray-200 dark:border-gray-700 pb-8">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  修改密码
                </h3>
                <form @submit.prevent="changePassword" class="space-y-4 max-w-md">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      当前密码
                    </label>
                    <input
                      v-model="passwordForm.currentPassword"
                      type="password"
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                      placeholder="请输入当前密码"
                    >
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      新密码
                    </label>
                    <input
                      v-model="passwordForm.newPassword"
                      type="password"
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                      placeholder="请输入新密码"
                    >
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      确认新密码
                    </label>
                    <input
                      v-model="passwordForm.confirmPassword"
                      type="password"
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                      placeholder="请再次输入新密码"
                    >
                  </div>
                  <Button
                    type="submit"
                    :loading="isChangingPassword"
                    class="px-4 py-2"
                  >
                    修改密码
                  </Button>
                </form>
              </div>

              <!-- 登录设备管理 -->
              <div class="border-b border-gray-200 dark:border-gray-700 pb-8">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  登录设备管理
                </h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                  管理已登录的设备，可以远程注销其他设备
                </p>
                <div class="space-y-3">
                  <div class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div class="flex items-center space-x-3">
                      <Icon name="computer-desktop" class="w-5 h-5 text-gray-500" />
                      <div>
                        <p class="font-medium text-gray-900 dark:text-white">当前设备</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Chrome on macOS</p>
                      </div>
                    </div>
                    <span class="text-sm text-green-600 dark:text-green-400">当前活跃</span>
                  </div>
                </div>
              </div>

              <!-- 两步验证 -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  两步验证
                </h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                  为你的账户添加额外的安全保护
                </p>
                <div class="flex items-center justify-between">
                  <div>
                    <p class="font-medium text-gray-900 dark:text-white">短信验证</p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">通过短信接收验证码</p>
                  </div>
                  <ToggleSwitch v-model="twoFactorEnabled" />
                </div>
              </div>
            </div>
          </Card>

          <!-- 保存按钮 -->
          <div class="mt-8 flex justify-end">
            <Button
              @click="saveSettings"
              :loading="isSaving"
              class="px-6 py-2"
            >
              保存设置
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: 'auth'
})

// 通知和错误处理
const { showSuccess, showError } = useNotification()

// API
const { getUserSettings, updateUserSettings } = useSettingsApi()
const { changePassword: apiChangePassword } = useAuthApi()

// 标签页
const activeTab = ref('notifications')
const tabs = [
  { id: 'notifications', name: '通知设置', icon: 'bell' },
  { id: 'privacy', name: '隐私设置', icon: 'shield-check' },
  { id: 'playback', name: '播放设置', icon: 'musical-note' },
  { id: 'appearance', name: '外观设置', icon: 'paint-brush' },
  { id: 'security', name: '账户安全', icon: 'lock-closed' }
]

// 主题选项
const themes = [
  { value: 'system', name: '跟随系统', icon: 'computer-desktop' },
  { value: 'light', name: '浅色主题', icon: 'sun' },
  { value: 'dark', name: '深色主题', icon: 'moon' }
]

// 设置数据
const settings = reactive({
  notifications: {
    email: true,
    push: true,
    newMusic: true,
    comments: true,
    follows: true
  },
  privacy: {
    profileVisibility: 'public',
    publicPlaylists: true,
    showActivity: true
  },
  playback: {
    quality: 'medium',
    autoplay: true,
    crossfade: false,
    volume: 80
  },
  appearance: {
    theme: 'system',
    language: 'zh-CN'
  }
})

const isSaving = ref(false)
const isLoading = ref(false)
const isChangingPassword = ref(false)
const twoFactorEnabled = ref(false)

// 密码修改表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 加载设置
const loadSettings = async () => {
  isLoading.value = true
  try {
    const response = await getUserSettings()
    if (response.success) {
      Object.assign(settings, response.data)
    }
  } catch (error) {
    console.warn('加载设置失败:', error)
    // 使用默认设置
  } finally {
    isLoading.value = false
  }
}

// 保存设置
const saveSettings = async () => {
  isSaving.value = true
  try {
    const response = await updateUserSettings(settings)
    if (response.success) {
      showSuccess('设置保存成功')
    }
  } catch (error: any) {
    showError(error.message || '保存设置失败')
  } finally {
    isSaving.value = false
  }
}

// 修改密码
const changePassword = async () => {
  // 验证表单
  if (!passwordForm.currentPassword) {
    showError('请输入当前密码')
    return
  }
  if (!passwordForm.newPassword) {
    showError('请输入新密码')
    return
  }
  if (passwordForm.newPassword.length < 6) {
    showError('新密码长度至少6位')
    return
  }
  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    showError('两次输入的密码不一致')
    return
  }

  isChangingPassword.value = true
  try {
    const response = await apiChangePassword({
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword
    })

    if (response.success) {
      showSuccess('密码修改成功')
      // 清空表单
      Object.assign(passwordForm, {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
    }
  } catch (error: any) {
    showError(error.message || '密码修改失败')
  } finally {
    isChangingPassword.value = false
  }
}

// 页面初始化
onMounted(async () => {
  await loadSettings()
})

// 页面标题
useHead({
  title: '设置 - MusicDou'
})
</script>
