<template>
  <div class="h-full bg-gray-50 dark:bg-gray-900 overflow-y-auto">
    <div class="max-w-6xl mx-auto px-6 py-6">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          音乐排行榜
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          发现最热门的音乐作品
        </p>
      </div>

      <!-- 排行榜分类 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <Card class="p-6 hover:shadow-lg transition-all cursor-pointer">
          <div class="flex items-center gap-4 mb-4">
            <div class="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
              <Icon name="fire" class="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">热歌榜</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">最受欢迎的歌曲</p>
            </div>
          </div>
        </Card>

        <Card class="p-6 hover:shadow-lg transition-all cursor-pointer">
          <div class="flex items-center gap-4 mb-4">
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
              <Icon name="star" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">新歌榜</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">最新发布的歌曲</p>
            </div>
          </div>
        </Card>

        <Card class="p-6 hover:shadow-lg transition-all cursor-pointer">
          <div class="flex items-center gap-4 mb-4">
            <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center">
              <Icon name="trophy" class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">原创榜</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">优秀原创作品</p>
            </div>
          </div>
        </Card>
      </div>

      <!-- 排行榜列表 -->
      <Card class="p-6">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">热歌榜 TOP 50</h2>
        <div class="space-y-4">
          <div
            v-for="i in 10"
            :key="i"
            class="flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer"
          >
            <div class="w-8 h-8 flex items-center justify-center">
              <span class="text-lg font-bold" :class="i <= 3 ? 'text-red-500' : 'text-gray-500 dark:text-gray-400'">
                {{ i }}
              </span>
            </div>
            <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center">
              <Icon name="musical-note" class="w-6 h-6 text-white" />
            </div>
            <div class="flex-1">
              <h3 class="font-medium text-gray-900 dark:text-white">热门歌曲 {{ i }}</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">艺术家名称</p>
            </div>
            <div class="flex items-center gap-2">
              <Button size="sm" variant="ghost">
                <Icon name="play" class="w-4 h-4" />
              </Button>
              <Button size="sm" variant="ghost">
                <Icon name="heart" class="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: 'auth'
})

useHead({
  title: '排行榜 - MusicDou',
  meta: [
    { name: 'description', content: '发现最热门的音乐排行榜' }
  ]
})
</script>
