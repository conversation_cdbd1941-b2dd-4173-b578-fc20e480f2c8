<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center">
    <div class="max-w-md w-full bg-white rounded-lg shadow-md p-6">
      <h1 class="text-2xl font-bold text-center mb-6">清除认证状态</h1>
      
      <div class="space-y-4">
        <div class="text-sm text-gray-600">
          <p>当前Token: {{ token || '无' }}</p>
          <p>用户状态: {{ user ? user.username : '未登录' }}</p>
        </div>
        
        <button
          @click="clearAuth"
          class="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700"
        >
          清除认证状态
        </button>
        
        <button
          @click="goToLogin"
          class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
        >
          前往登录页面
        </button>
      </div>
      
      <div v-if="message" class="mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
        {{ message }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  title: '清除认证状态',
  layout: false
})

const message = ref('')
const { user, clearToken } = useAuth()
const token = useCookie('auth-token')

const clearAuth = () => {
  // 清除token
  clearToken()
  
  // 清除cookie
  const authCookie = useCookie('auth-token')
  authCookie.value = null
  
  message.value = '认证状态已清除'
  
  // 刷新页面状态
  setTimeout(() => {
    window.location.reload()
  }, 1000)
}

const goToLogin = () => {
  navigateTo('/login')
}

// 页面标题
useHead({
  title: '清除认证状态 - MusicDou'
})
</script>
