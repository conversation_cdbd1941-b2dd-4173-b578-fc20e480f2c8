<template>
  <div class="h-full bg-gray-50 dark:bg-gray-900 overflow-y-auto">
    <div class="max-w-6xl mx-auto px-6 py-6">
      <!-- 歌单头部 -->
      <div class="flex items-start gap-6 mb-8">
        <div class="w-48 h-48 bg-gradient-to-br from-red-400 to-pink-500 rounded-lg flex items-center justify-center shadow-lg">
          <Icon name="heart" class="w-24 h-24 text-white" />
        </div>
        <div class="flex-1">
          <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            我喜欢的音乐
          </h1>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            收藏你最爱的歌曲
          </p>
          <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-6">
            <span>{{ user?.username || '用户' }}</span>
            <span>•</span>
            <span>{{ likedSongs.length }} 首歌曲</span>
            <span>•</span>
            <span>最后更新：今天</span>
          </div>
          <div class="flex items-center gap-3">
            <Button size="lg" class="px-8">
              <Icon name="play" class="w-5 h-5 mr-2" />
              播放全部
            </Button>
            <Button size="lg" variant="outline">
              <Icon name="arrow-path" class="w-5 h-5 mr-2" />
              随机播放
            </Button>
            <Button size="lg" variant="ghost">
              <Icon name="share" class="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>

      <!-- 歌曲列表 -->
      <Card class="p-6">
        <div class="space-y-2">
          <div
            v-for="(song, index) in likedSongs"
            :key="song.id"
            class="flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer group"
          >
            <div class="w-8 text-center">
              <span class="text-gray-500 dark:text-gray-400 group-hover:hidden">
                {{ index + 1 }}
              </span>
              <Icon name="play" class="w-4 h-4 text-gray-600 dark:text-gray-400 hidden group-hover:block" />
            </div>
            <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg flex items-center justify-center">
              <Icon name="musical-note" class="w-6 h-6 text-white" />
            </div>
            <div class="flex-1 min-w-0">
              <h3 class="font-medium text-gray-900 dark:text-white truncate">
                {{ song.title }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
                {{ song.artist }}
              </p>
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400 hidden md:block">
              {{ song.album }}
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              {{ song.duration }}
            </div>
            <div class="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button size="sm" variant="ghost">
                <Icon name="heart" class="w-4 h-4 text-red-500" />
              </Button>
              <Button size="sm" variant="ghost">
                <Icon name="ellipsis-horizontal" class="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="likedSongs.length === 0" class="text-center py-16">
          <Icon name="heart" class="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            还没有喜欢的音乐
          </h3>
          <p class="text-gray-500 dark:text-gray-400 mb-6">
            点击歌曲旁的爱心按钮来收藏你喜欢的音乐
          </p>
          <Button @click="navigateTo('/')">
            <Icon name="magnifying-glass" class="w-4 h-4 mr-2" />
            发现音乐
          </Button>
        </div>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
const authStore = useAuthStore()
const user = computed(() => authStore.user)

// 模拟喜欢的歌曲数据
const likedSongs = ref([
  {
    id: 1,
    title: '喜欢的歌曲 1',
    artist: '艺术家 1',
    album: '专辑 1',
    duration: '3:45'
  },
  {
    id: 2,
    title: '喜欢的歌曲 2',
    artist: '艺术家 2',
    album: '专辑 2',
    duration: '4:12'
  },
  {
    id: 3,
    title: '喜欢的歌曲 3',
    artist: '艺术家 3',
    album: '专辑 3',
    duration: '3:28'
  }
])

definePageMeta({
  middleware: 'auth'
})

useHead({
  title: '我喜欢的音乐 - MusicDou',
  meta: [
    { name: 'description', content: '管理你收藏的音乐' }
  ]
})
</script>
