<template>
  <div class="h-full bg-gray-50 dark:bg-gray-900 overflow-y-auto">
    <div class="max-w-6xl mx-auto px-6 py-6">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          我的朋友
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          与朋友分享音乐，发现更多精彩
        </p>
      </div>

      <!-- 朋友列表 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card
          v-for="i in 9"
          :key="i"
          class="p-6 hover:shadow-lg transition-all"
        >
          <div class="flex items-center gap-4 mb-4">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-medium">
              {{ `用户${i}`.charAt(0) }}
            </div>
            <div class="flex-1">
              <h3 class="font-medium text-gray-900 dark:text-white">用户{{ i }}</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">在线</p>
            </div>
          </div>
          
          <div class="flex gap-2">
            <Button size="sm" variant="outline" class="flex-1">
              <Icon name="chat-bubble-left" class="w-4 h-4 mr-1" />
              聊天
            </Button>
            <Button size="sm" variant="outline" class="flex-1">
              <Icon name="musical-note" class="w-4 h-4 mr-1" />
              歌单
            </Button>
          </div>
        </Card>
      </div>

      <!-- 空状态 -->
      <div v-if="false" class="text-center py-16">
        <Icon name="users" class="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          还没有朋友
        </h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6">
          搜索用户名或邀请朋友加入MusicDou
        </p>
        <Button>
          <Icon name="plus" class="w-4 h-4 mr-2" />
          添加朋友
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: 'auth'
})

useHead({
  title: '我的朋友 - MusicDou',
  meta: [
    { name: 'description', content: '管理你的音乐朋友' }
  ]
})
</script>
