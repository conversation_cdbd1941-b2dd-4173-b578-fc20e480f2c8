<template>
  <div class="social-page">
    <!-- 页面头部 -->
    <div class="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
            社交动态
          </h1>
          <p class="mt-2 text-gray-600 dark:text-gray-400">
            发现音乐，分享感受，与音乐爱好者互动
          </p>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 主内容区域 -->
        <div class="lg:col-span-2 space-y-6">
          <!-- 动态筛选 -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                动态时间线
              </h2>
              
              <div class="flex items-center space-x-2">
                <label class="text-sm text-gray-600 dark:text-gray-400">筛选:</label>
                <select
                  v-model="activityFilter"
                  class="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  @change="handleFilterChange"
                >
                  <option value="all">全部动态</option>
                  <option value="music_upload">音乐上传</option>
                  <option value="playlist_create">歌单创建</option>
                  <option value="music_like">音乐点赞</option>
                  <option value="follow">关注动态</option>
                  <option value="comment">评论动态</option>
                </select>
              </div>
            </div>
          </div>

          <!-- 动态列表 -->
          <div class="space-y-4">
            <!-- 加载状态 -->
            <div v-if="loading && activities.length === 0" class="space-y-4">
              <div v-for="i in 5" :key="i" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 animate-pulse">
                <div class="flex space-x-4">
                  <div class="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                  <div class="flex-1 space-y-2">
                    <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/3"></div>
                    <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-2/3"></div>
                    <div class="h-20 bg-gray-300 dark:bg-gray-600 rounded"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 动态项 -->
            <div v-else-if="activities.length > 0">
              <SocialActivityItem
                v-for="activity in activities"
                :key="activity.id"
                :activity="activity"
                @like="handleActivityLike"
                @comment="handleActivityComment"
                @share="handleActivityShare"
              />
            </div>

            <!-- 空状态 -->
            <div v-else class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12 text-center">
              <UiIcon name="rss" size="xl" class="mx-auto text-gray-400 mb-4" />
              <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                暂无动态
              </h3>
              <p class="text-gray-600 dark:text-gray-400 mb-4">
                关注更多用户来查看他们的音乐动态
              </p>
              <UiButton
                variant="primary"
                @click="navigateTo('/users')"
              >
                发现用户
              </UiButton>
            </div>
          </div>

          <!-- 加载更多 -->
          <div v-if="hasMore" class="text-center">
            <UiButton
              variant="ghost"
              :loading="loadingMore"
              @click="loadMore"
            >
              加载更多动态
            </UiButton>
          </div>
        </div>

        <!-- 侧边栏 -->
        <div class="space-y-6">
          <!-- 推荐关注 -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              推荐关注
            </h3>
            
            <div v-if="recommendedUsers.length > 0" class="space-y-3">
              <UserUserCard
                v-for="user in recommendedUsers"
                :key="user.id"
                :user="user"
                size="sm"
                :show-bio="false"
                :show-stats="false"
                @followed="handleUserFollowed"
              />
            </div>
            
            <div v-else class="text-center py-4">
              <p class="text-sm text-gray-600 dark:text-gray-400">
                暂无推荐用户
              </p>
            </div>
          </div>

          <!-- 热门话题 -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              热门话题
            </h3>
            
            <div class="space-y-2">
              <div
                v-for="topic in trendingTopics"
                :key="topic.name"
                class="flex items-center justify-between p-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                @click="handleTopicClick(topic.name)"
              >
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  #{{ topic.name }}
                </span>
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  {{ topic.count }}
                </span>
              </div>
            </div>
          </div>

          <!-- 统计信息 -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              我的统计
            </h3>
            
            <div v-if="authStore.isAuthenticated" class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">关注</span>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ authStore.user?.followingCount || 0 }}
                </span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">粉丝</span>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ authStore.user?.followersCount || 0 }}
                </span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">积分</span>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ authStore.user?.points || 0 }}
                </span>
              </div>
            </div>
            
            <div v-else class="text-center py-4">
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                登录查看统计信息
              </p>
              <UiButton
                variant="primary"
                size="sm"
                @click="navigateTo('/login')"
              >
                立即登录
              </UiButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { User } from '~/types'

// 页面元数据
definePageMeta({
  title: '社交动态 - MusicDou',
  description: '发现音乐，分享感受，与音乐爱好者互动'
})

const authStore = useAuthStore()
const socialStore = useSocialStore()

// 响应式数据
const activities = ref<any[]>([])
const recommendedUsers = ref<User[]>([])
const trendingTopics = ref<Array<{ name: string; count: number }>>([])
const loading = ref(false)
const loadingMore = ref(false)
const activityFilter = ref('all')
const currentPage = ref(1)
const hasMore = ref(false)

// 加载动态
const loadActivities = async (page = 1, append = false) => {
  try {
    if (page === 1) {
      loading.value = true
    } else {
      loadingMore.value = true
    }

    const response = await socialStore.loadActivityFeed({
      page,
      limit: 20,
      type: activityFilter.value === 'all' ? undefined : activityFilter.value,
      includeFollowing: true
    })

    if (response.success) {
      if (append) {
        activities.value.push(...response.data.data)
      } else {
        activities.value = response.data.data
      }
      
      hasMore.value = response.data.pagination.page < response.data.pagination.totalPages
      currentPage.value = response.data.pagination.page
    }
  } catch (error) {
    console.error('Load activities error:', error)
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 处理筛选变化
const handleFilterChange = () => {
  currentPage.value = 1
  loadActivities(1, false)
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loadingMore.value) {
    loadActivities(currentPage.value + 1, true)
  }
}

// 处理动态交互
const handleActivityLike = (activityId: string) => {
  console.log('Like activity:', activityId)
}

const handleActivityComment = (activityId: string) => {
  console.log('Comment activity:', activityId)
}

const handleActivityShare = (activityId: string) => {
  console.log('Share activity:', activityId)
}

// 处理用户关注
const handleUserFollowed = (userId: string) => {
  // 从推荐列表中移除已关注的用户
  recommendedUsers.value = recommendedUsers.value.filter(user => user.id !== userId)
}

// 处理话题点击
const handleTopicClick = (topicName: string) => {
  navigateTo(`/search?q=${encodeURIComponent('#' + topicName)}`)
}

// 初始化数据
onMounted(async () => {
  await loadActivities()
  
  // 加载推荐用户和热门话题
  // 这里可以添加相应的API调用
  
  // 模拟数据
  trendingTopics.value = [
    { name: '流行音乐', count: 1234 },
    { name: '古典音乐', count: 856 },
    { name: '摇滚乐', count: 642 },
    { name: '电子音乐', count: 523 },
    { name: '民谣', count: 389 }
  ]
})
</script>

<style scoped>
.social-page {
  @apply min-h-screen bg-gray-50 dark:bg-gray-900;
}
</style>
