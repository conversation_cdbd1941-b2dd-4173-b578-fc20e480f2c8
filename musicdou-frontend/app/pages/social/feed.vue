<template>
  <div class="h-full bg-gray-50 dark:bg-gray-900 overflow-y-auto">
    <div class="max-w-4xl mx-auto px-6 py-6">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          朋友动态
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          看看朋友们在听什么
        </p>
      </div>

      <!-- 发布动态 -->
      <Card class="p-6 mb-6">
        <div class="flex gap-4">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-medium">
            U
          </div>
          <div class="flex-1">
            <textarea
              placeholder="分享你正在听的音乐..."
              class="w-full p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none"
              rows="3"
            ></textarea>
            <div class="flex justify-between items-center mt-3">
              <div class="flex gap-2">
                <Button size="sm" variant="ghost">
                  <Icon name="musical-note" class="w-4 h-4 mr-1" />
                  音乐
                </Button>
                <Button size="sm" variant="ghost">
                  <Icon name="photo" class="w-4 h-4 mr-1" />
                  图片
                </Button>
              </div>
              <Button size="sm">
                发布
              </Button>
            </div>
          </div>
        </div>
      </Card>

      <!-- 动态列表 -->
      <div class="space-y-6">
        <Card
          v-for="i in 5"
          :key="i"
          class="p-6"
        >
          <div class="flex gap-4">
            <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center text-white font-medium">
              {{ `朋友${i}`.charAt(0) }}
            </div>
            <div class="flex-1">
              <div class="flex items-center gap-2 mb-2">
                <h3 class="font-medium text-gray-900 dark:text-white">朋友{{ i }}</h3>
                <span class="text-sm text-gray-500 dark:text-gray-400">2小时前</span>
              </div>
              <p class="text-gray-700 dark:text-gray-300 mb-4">
                正在听这首超棒的歌曲，推荐给大家！
              </p>
              
              <!-- 音乐卡片 -->
              <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-4">
                <div class="flex items-center gap-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center">
                    <Icon name="musical-note" class="w-6 h-6 text-white" />
                  </div>
                  <div class="flex-1">
                    <h4 class="font-medium text-gray-900 dark:text-white">歌曲名称</h4>
                    <p class="text-sm text-gray-500 dark:text-gray-400">艺术家</p>
                  </div>
                  <Button size="sm" variant="ghost">
                    <Icon name="play" class="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <!-- 互动按钮 -->
              <div class="flex items-center gap-4">
                <Button size="sm" variant="ghost">
                  <Icon name="heart" class="w-4 h-4 mr-1" />
                  {{ 5 + i }}
                </Button>
                <Button size="sm" variant="ghost">
                  <Icon name="chat-bubble-left" class="w-4 h-4 mr-1" />
                  {{ 2 + i }}
                </Button>
                <Button size="sm" variant="ghost">
                  <Icon name="share" class="w-4 h-4 mr-1" />
                  分享
                </Button>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <!-- 空状态 -->
      <div v-if="false" class="text-center py-16">
        <Icon name="rss" class="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          暂无动态
        </h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6">
          关注更多朋友，发现精彩音乐动态
        </p>
        <Button>
          <Icon name="plus" class="w-4 h-4 mr-2" />
          寻找朋友
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: 'auth'
})

useHead({
  title: '朋友动态 - MusicDou',
  meta: [
    { name: 'description', content: '查看朋友们的音乐动态' }
  ]
})
</script>
