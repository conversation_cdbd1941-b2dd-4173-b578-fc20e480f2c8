<template>
  <div class="h-full bg-gray-50 dark:bg-gray-900 overflow-y-auto">
    <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          上传音乐
        </h1>
        <p class="mt-2 text-gray-600 dark:text-gray-400">
          分享你的音乐作品，让更多人听到你的声音
        </p>
      </div>

      <!-- 上传表单 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="p-6">
          <form @submit.prevent="handleSubmit">
            <!-- 文件上传区域 -->
            <div class="mb-8">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                选择音乐文件
              </label>
              
              <!-- 拖拽上传区域 -->
              <div
                @drop="handleDrop"
                @dragover.prevent
                @dragenter.prevent
                :class="[
                  'border-2 border-dashed rounded-lg p-8 text-center transition-colors',
                  isDragging 
                    ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20' 
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                ]"
              >
                <div v-if="!selectedFile">
                  <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                  </svg>
                  <p class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    拖拽文件到这里或点击选择
                  </p>
                  <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                    支持 MP3, FLAC, WAV, AAC, OGG 格式，最大 100MB
                  </p>
                  <input
                    ref="fileInput"
                    type="file"
                    accept="audio/*"
                    @change="handleFileSelect"
                    class="hidden"
                  />
                  <button
                    type="button"
                    @click="$refs.fileInput.click()"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    选择文件
                  </button>
                </div>
                
                <!-- 已选择文件显示 -->
                <div v-else class="flex items-center justify-center">
                  <div class="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
                    <svg class="w-8 h-8 text-blue-500 mr-3" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                    </svg>
                    <div class="flex-1 text-left">
                      <p class="text-sm font-medium text-gray-900 dark:text-white">
                        {{ selectedFile.name }}
                      </p>
                      <p class="text-xs text-gray-500 dark:text-gray-400">
                        {{ formatFileSize(selectedFile.size) }}
                      </p>
                    </div>
                    <button
                      type="button"
                      @click="removeFile"
                      class="ml-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 音乐信息表单 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  歌曲标题 *
                </label>
                <input
                  v-model="formData.title"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="请输入歌曲标题"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  艺术家 *
                </label>
                <input
                  v-model="formData.artist"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="请输入艺术家名称"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  专辑名称
                </label>
                <input
                  v-model="formData.album"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="请输入专辑名称"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  音乐流派
                </label>
                <select
                  v-model="formData.genre"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="">请选择流派</option>
                  <option value="pop">流行</option>
                  <option value="rock">摇滚</option>
                  <option value="jazz">爵士</option>
                  <option value="classical">古典</option>
                  <option value="electronic">电子</option>
                  <option value="folk">民谣</option>
                  <option value="rap">说唱</option>
                  <option value="country">乡村</option>
                  <option value="blues">蓝调</option>
                  <option value="other">其他</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  发行年份
                </label>
                <input
                  v-model.number="formData.year"
                  type="number"
                  min="1900"
                  :max="new Date().getFullYear()"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="例如：2024"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  标签
                </label>
                <input
                  v-model="formData.tags"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="用逗号分隔，例如：原创,抒情,治愈"
                />
              </div>
            </div>

            <!-- 隐私设置 -->
            <div class="mb-8">
              <div class="flex items-center">
                <input
                  v-model="formData.isPublic"
                  type="checkbox"
                  id="isPublic"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label for="isPublic" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  公开发布（其他用户可以搜索和播放）
                </label>
              </div>
            </div>

            <!-- 上传进度 -->
            <div v-if="uploadProgress > 0" class="mb-6">
              <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                <span>上传进度</span>
                <span>{{ uploadProgress }}%</span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  :style="`width: ${uploadProgress}%`"
                ></div>
              </div>
            </div>

            <!-- 提交按钮 -->
            <div class="flex justify-end space-x-4">
              <button
                type="button"
                @click="resetForm"
                class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                重置
              </button>
              <button
                type="submit"
                :disabled="!selectedFile || isUploading"
                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {{ isUploading ? '上传中...' : '上传音乐' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: 'auth'
})

// 页面元数据
useHead({
  title: '上传音乐 - MusicDou',
  meta: [
    { name: 'description', content: '上传你的音乐作品到MusicDou平台' }
  ]
})

// 响应式数据
const selectedFile = ref<File | null>(null)
const isDragging = ref(false)
const isUploading = ref(false)
const uploadProgress = ref(0)

// 表单数据
const formData = ref({
  title: '',
  artist: '',
  album: '',
  genre: '',
  year: null as number | null,
  tags: '',
  isPublic: true
})

// 通知系统
const { showSuccess, showError } = useNotification()

// 文件选择处理
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    selectFile(target.files[0])
  }
}

// 拖拽处理
const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragging.value = false

  if (event.dataTransfer?.files && event.dataTransfer.files[0]) {
    selectFile(event.dataTransfer.files[0])
  }
}

// 选择文件
const selectFile = (file: File) => {
  // 验证文件类型
  const allowedTypes = ['audio/mpeg', 'audio/flac', 'audio/wav', 'audio/aac', 'audio/ogg']
  if (!allowedTypes.includes(file.type)) {
    showError('不支持的文件格式', '请选择 MP3, FLAC, WAV, AAC 或 OGG 格式的音频文件')
    return
  }

  // 验证文件大小 (100MB)
  const maxSize = 100 * 1024 * 1024
  if (file.size > maxSize) {
    showError('文件过大', '文件大小不能超过 100MB')
    return
  }

  selectedFile.value = file

  // 尝试从文件名提取信息
  const fileName = file.name.replace(/\.[^/.]+$/, '') // 移除扩展名
  if (!formData.value.title) {
    formData.value.title = fileName
  }
}

// 移除文件
const removeFile = () => {
  selectedFile.value = null
  uploadProgress.value = 0
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 重置表单
const resetForm = () => {
  selectedFile.value = null
  uploadProgress.value = 0
  formData.value = {
    title: '',
    artist: '',
    album: '',
    genre: '',
    year: null,
    tags: '',
    isPublic: true
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!selectedFile.value) {
    showError('请选择文件', '请先选择要上传的音频文件')
    return
  }

  if (!formData.value.title.trim()) {
    showError('请填写歌曲标题', '歌曲标题是必填项')
    return
  }

  if (!formData.value.artist.trim()) {
    showError('请填写艺术家', '艺术家是必填项')
    return
  }

  try {
    isUploading.value = true
    uploadProgress.value = 0

    // 创建 FormData
    const uploadData = new FormData()
    uploadData.append('music', selectedFile.value)
    uploadData.append('title', formData.value.title.trim())
    uploadData.append('artist', formData.value.artist.trim())

    if (formData.value.album?.trim()) {
      uploadData.append('album', formData.value.album.trim())
    }
    if (formData.value.genre) {
      uploadData.append('genre', formData.value.genre)
    }
    if (formData.value.year) {
      uploadData.append('year', formData.value.year.toString())
    }
    if (formData.value.tags?.trim()) {
      uploadData.append('tags', formData.value.tags.trim())
    }
    uploadData.append('isPublic', formData.value.isPublic.toString())

    // 获取认证token
    const token = useCookie('auth-token')

    // 上传文件
    const response = await $fetch('/api/v1/upload/music', {
      method: 'POST',
      body: uploadData,
      headers: {
        'Authorization': `Bearer ${token.value}`
      },
      onUploadProgress: (progress) => {
        uploadProgress.value = Math.round((progress.loaded / progress.total) * 100)
      }
    })

    if (response.success) {
      showSuccess('上传成功', '你的音乐已成功上传，正在处理中...')
      resetForm()

      // 可选：跳转到音乐详情页或我的音乐页面
      // await navigateTo(`/music/${response.data._id}`)
    } else {
      throw new Error(response.message || '上传失败')
    }

  } catch (error: any) {
    console.error('Upload error:', error)

    if (error.status === 413) {
      showError('文件过大', '文件大小超过限制，请选择较小的文件')
    } else if (error.status === 415) {
      showError('文件格式不支持', '请选择支持的音频格式')
    } else if (error.status === 401) {
      showError('认证失败', '请重新登录后再试')
      await navigateTo('/login')
    } else {
      showError('上传失败', error.message || '网络错误，请稍后重试')
    }
  } finally {
    isUploading.value = false
  }
}

// 拖拽事件监听
onMounted(() => {
  const handleDragEnter = () => {
    isDragging.value = true
  }

  const handleDragLeave = (e: DragEvent) => {
    if (!e.relatedTarget) {
      isDragging.value = false
    }
  }

  document.addEventListener('dragenter', handleDragEnter)
  document.addEventListener('dragleave', handleDragLeave)

  onUnmounted(() => {
    document.removeEventListener('dragenter', handleDragEnter)
    document.removeEventListener('dragleave', handleDragLeave)
  })
})
</script>
