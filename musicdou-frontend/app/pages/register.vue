<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
    <!-- 背景装饰元素 -->
    <div class="absolute inset-0 overflow-hidden">
      <!-- 浮动的音符装饰 -->
      <div class="absolute top-20 left-20 text-white/10 text-6xl animate-bounce animation-delay-1000">♪</div>
      <div class="absolute top-40 right-32 text-white/10 text-4xl animate-bounce animation-delay-2000">♫</div>
      <div class="absolute bottom-32 left-40 text-white/10 text-5xl animate-bounce animation-delay-3000">♬</div>
      <div class="absolute bottom-20 right-20 text-white/10 text-3xl animate-bounce animation-delay-4000">♩</div>

      <!-- 渐变光晕 -->
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
      <div class="absolute top-40 left-40 w-60 h-60 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
    </div>

    <div class="max-w-md w-full space-y-8 relative z-10">
      <!-- Header -->
      <div class="text-center">
        <div class="mx-auto mb-8">
          <div class="relative">
            <div class="w-20 h-20 mx-auto bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center shadow-2xl transform hover:scale-110 transition-transform duration-300">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
              </svg>
            </div>
            <!-- 光环效果 -->
            <div class="absolute inset-0 w-20 h-20 mx-auto bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-ping opacity-20"></div>
          </div>
          <h1 class="mt-6 text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-blue-400 to-indigo-400">
            MusicDou
          </h1>
        </div>
        <h2 class="text-3xl font-bold text-white mb-2">
          加入我们！
        </h2>
        <p class="text-lg text-gray-300">
          开始你的音乐之旅
        </p>
      </div>

      <!-- Register Form -->
      <div class="backdrop-blur-lg bg-white/10 border border-white/20 rounded-2xl shadow-2xl p-8">
        <form class="space-y-6" @submit.prevent="handleRegister">
          <!-- Username -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-200">
              用户名
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <input
                v-model="form.username"
                type="text"
                class="block w-full pl-10 pr-3 py-3 border border-white/30 rounded-xl bg-white/10 backdrop-blur-sm text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                placeholder="请输入用户名"
                required
                @blur="validateUsername"
              />
            </div>
            <p v-if="errors.username" class="text-sm text-red-400">{{ errors.username }}</p>
          </div>

          <!-- Email -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-200">
              邮箱地址
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                </svg>
              </div>
              <input
                v-model="form.email"
                type="email"
                class="block w-full pl-10 pr-3 py-3 border border-white/30 rounded-xl bg-white/10 backdrop-blur-sm text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                placeholder="请输入邮箱地址"
                required
                @blur="validateEmail"
              />
            </div>
            <p v-if="errors.email" class="text-sm text-red-400">{{ errors.email }}</p>
          </div>

          <!-- Password -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-200">
              密码
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              </div>
              <input
                v-model="form.password"
                type="password"
                class="block w-full pl-10 pr-3 py-3 border border-white/30 rounded-xl bg-white/10 backdrop-blur-sm text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                placeholder="请输入密码"
                required
                @blur="validatePassword"
              />
            </div>
            <p v-if="errors.password" class="text-sm text-red-400">{{ errors.password }}</p>

            <!-- Password Strength Indicator -->
            <div v-if="form.password" class="mt-3">
              <div class="flex items-center space-x-2">
                <div class="flex-1 bg-white/20 rounded-full h-2">
                  <div
                    :class="passwordStrengthClasses"
                    class="h-2 rounded-full transition-all duration-300"
                    :style="{ width: `${passwordStrength.percentage}%` }"
                  />
                </div>
                <span :class="passwordStrengthTextClasses" class="text-xs font-medium">
                  {{ passwordStrength.text }}
                </span>
              </div>
            </div>
          </div>

          <!-- Confirm Password -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-200">
              确认密码
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              </div>
              <input
                v-model="form.confirmPassword"
                type="password"
                class="block w-full pl-10 pr-3 py-3 border border-white/30 rounded-xl bg-white/10 backdrop-blur-sm text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                placeholder="请再次输入密码"
                required
                @blur="validateConfirmPassword"
              />
            </div>
            <p v-if="errors.confirmPassword" class="text-sm text-red-400">{{ errors.confirmPassword }}</p>
          </div>

          <!-- Terms Agreement -->
          <div class="flex items-start space-x-3">
            <input
              v-model="form.agreeTerms"
              type="checkbox"
              class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded bg-white/10 backdrop-blur-sm mt-1"
              required
            />
            <label class="text-sm text-gray-300">
              我同意
              <NuxtLink to="/terms" class="text-purple-400 hover:text-purple-300 transition-colors duration-200 underline decoration-purple-400/50">
                服务条款
              </NuxtLink>
              和
              <NuxtLink to="/privacy" class="text-purple-400 hover:text-purple-300 transition-colors duration-200 underline decoration-purple-400/50">
                隐私政策
              </NuxtLink>
            </label>
          </div>

          <!-- Submit Button -->
          <button
            type="submit"
            :disabled="!isFormValid || isLoading"
            class="w-full flex justify-center py-3 px-4 border border-transparent rounded-xl text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200 shadow-lg"
          >
            <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isLoading ? '注册中...' : '创建账户' }}
          </button>

          <!-- Error Message -->
          <div v-if="registerError" class="text-center">
            <div class="bg-red-500/20 border border-red-500/30 rounded-lg p-3">
              <p class="text-sm text-red-300">
                {{ registerError }}
              </p>
            </div>
          </div>
        </form>
      </div>



      <!-- Login Link -->
      <div class="text-center">
        <p class="text-gray-300">
          已有账户？
          <NuxtLink
            to="/login"
            class="font-medium text-purple-400 hover:text-purple-300 transition-colors duration-200 underline decoration-purple-400/50 hover:decoration-purple-300"
          >
            立即登录
          </NuxtLink>
        </p>
      </div>

      <!-- 装饰性元素 -->
      <div class="text-center mt-8">
        <div class="flex items-center justify-center space-x-2 text-gray-400">
          <div class="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
          <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse animation-delay-1000"></div>
          <div class="w-2 h-2 bg-indigo-500 rounded-full animate-pulse animation-delay-2000"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  title: '注册',
  layout: false,
  middleware: 'guest'
})

// 表单数据
const form = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

// 错误状态
const errors = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

// 加载状态
const isLoading = ref(false)
const registerError = ref('')

// 认证相关
const { register } = useAuth()
const router = useRouter()

// 表单验证
const validateUsername = () => {
  if (!form.username) {
    errors.username = '请输入用户名'
  } else if (form.username.length < 3) {
    errors.username = '用户名至少需要3个字符'
  } else if (!/^[a-zA-Z0-9_]+$/.test(form.username)) {
    errors.username = '用户名只能包含字母、数字和下划线'
  } else {
    errors.username = ''
  }
}

const validateEmail = () => {
  if (!form.email) {
    errors.email = '请输入邮箱地址'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.email = '请输入有效的邮箱地址'
  } else {
    errors.email = ''
  }
}

const validatePassword = () => {
  if (!form.password) {
    errors.password = '请输入密码'
  } else if (form.password.length < 8) {
    errors.password = '密码至少需要8个字符'
  } else {
    errors.password = ''
  }
}

const validateConfirmPassword = () => {
  if (!form.confirmPassword) {
    errors.confirmPassword = '请确认密码'
  } else if (form.password !== form.confirmPassword) {
    errors.confirmPassword = '两次输入的密码不一致'
  } else {
    errors.confirmPassword = ''
  }
}

// 密码强度检查
const passwordStrength = computed(() => {
  const password = form.password
  if (!password) return { percentage: 0, text: '', level: 0 }

  let score = 0
  let feedback = []

  // 长度检查
  if (password.length >= 8) score += 25
  else feedback.push('至少8个字符')

  // 包含小写字母
  if (/[a-z]/.test(password)) score += 25
  else feedback.push('包含小写字母')

  // 包含大写字母
  if (/[A-Z]/.test(password)) score += 25
  else feedback.push('包含大写字母')

  // 包含数字或特殊字符
  if (/[\d\W]/.test(password)) score += 25
  else feedback.push('包含数字或特殊字符')

  let level = 0
  let text = ''
  
  if (score < 50) {
    level = 1
    text = '弱'
  } else if (score < 75) {
    level = 2
    text = '中等'
  } else if (score < 100) {
    level = 3
    text = '强'
  } else {
    level = 4
    text = '很强'
  }

  return { percentage: score, text, level }
})

const passwordStrengthClasses = computed(() => {
  const level = passwordStrength.value.level
  if (level === 1) return 'bg-red-500'
  if (level === 2) return 'bg-yellow-500'
  if (level === 3) return 'bg-blue-500'
  if (level === 4) return 'bg-green-500'
  return 'bg-gray-300'
})

const passwordStrengthTextClasses = computed(() => {
  const level = passwordStrength.value.level
  if (level === 1) return 'text-red-500'
  if (level === 2) return 'text-yellow-500'
  if (level === 3) return 'text-blue-500'
  if (level === 4) return 'text-green-500'
  return 'text-gray-500'
})

// 表单有效性
const isFormValid = computed(() => {
  return form.username && 
         form.email && 
         form.password && 
         form.confirmPassword &&
         form.agreeTerms &&
         !errors.username && 
         !errors.email && 
         !errors.password && 
         !errors.confirmPassword
})

// 注册处理
const handleRegister = async () => {
  // 验证表单
  validateUsername()
  validateEmail()
  validatePassword()
  validateConfirmPassword()

  if (!isFormValid.value) {
    return
  }

  isLoading.value = true
  registerError.value = ''

  try {
    console.log('开始注册...', {
      username: form.username,
      email: form.email,
      password: '***'
    })

    // 使用真实的后端API注册
    const { register } = useAuth()
    await register({
      username: form.username,
      email: form.email,
      password: form.password
    })

    console.log('注册成功，跳转到主页')
    // 注册成功后会自动跳转到主页
    // 不需要手动处理重定向
  } catch (error: any) {
    console.error('注册失败:', error)
    registerError.value = error.message || '注册失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}

// 社交注册
const handleSocialRegister = async (provider: string) => {
  try {
    // 这里实现社交注册逻辑
    console.log(`Social register with ${provider}`)
  } catch (error: any) {
    registerError.value = error.message || `${provider} 注册失败`
  }
}

// 页面标题
useHead({
  title: '注册 - MusicDou',
  meta: [
    { name: 'description', content: '注册 MusicDou 账户，开始你的音乐之旅' }
  ]
})
</script>

<style scoped>
/* 自定义动画延迟 */
.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-3000 {
  animation-delay: 3s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* 玻璃态效果增强 */
.backdrop-blur-lg {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

/* 输入框聚焦效果 */
input:focus {
  box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.1);
}

/* 按钮悬停效果 */
button:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* 渐变文字效果 */
.bg-clip-text {
  background-clip: text;
  -webkit-background-clip: text;
}
</style>
