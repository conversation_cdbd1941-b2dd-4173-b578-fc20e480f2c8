<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <NuxtLink to="/public" class="flex items-center justify-center space-x-2 mb-6">
          <Icon name="MusicalNoteIcon" class="w-10 h-10 text-primary-500" />
          <span class="text-2xl font-bold text-gray-900 dark:text-white">
            MusicDou
          </span>
        </NuxtLink>
        <h2 class="text-3xl font-bold text-gray-900 dark:text-white">
          忘记密码
        </h2>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
          输入你的邮箱地址，我们将发送重置密码的链接
        </p>
      </div>

      <!-- Form -->
      <Card class="mt-8">
        <form v-if="!emailSent" class="space-y-6" @submit.prevent="handleSubmit">
          <!-- Email -->
          <Input
            v-model="form.email"
            type="email"
            label="邮箱地址"
            placeholder="请输入注册时使用的邮箱地址"
            left-icon="EnvelopeIcon"
            required
            :error="errors.email"
            @blur="validateEmail"
          />

          <!-- Error Message -->
          <div v-if="errorMessage" class="text-red-600 dark:text-red-400 text-sm">
            {{ errorMessage }}
          </div>

          <!-- Submit Button -->
          <Button
            type="submit"
            class="w-full"
            :loading="isLoading"
            :disabled="!isFormValid"
          >
            发送重置链接
          </Button>

          <!-- Back to Login -->
          <div class="text-center">
            <NuxtLink
              to="/login"
              class="text-primary-600 dark:text-primary-400 hover:text-primary-500 text-sm font-medium"
            >
              返回登录
            </NuxtLink>
          </div>
        </form>

        <!-- Success Message -->
        <div v-else class="text-center space-y-6">
          <div class="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
            <Icon name="CheckIcon" class="w-8 h-8 text-green-600 dark:text-green-400" />
          </div>
          
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              邮件已发送
            </h3>
            <p class="text-gray-600 dark:text-gray-400 text-sm">
              我们已向 <strong>{{ form.email }}</strong> 发送了密码重置链接。
              请检查你的邮箱（包括垃圾邮件文件夹）。
            </p>
          </div>

          <div class="space-y-3">
            <Button
              @click="resendEmail"
              variant="outline"
              class="w-full"
              :loading="isResending"
              :disabled="resendCooldown > 0"
            >
              <span v-if="resendCooldown > 0">
                重新发送 ({{ resendCooldown }}s)
              </span>
              <span v-else>
                重新发送邮件
              </span>
            </Button>
            
            <NuxtLink
              to="/login"
              class="block w-full text-center text-primary-600 dark:text-primary-400 hover:text-primary-500 text-sm font-medium"
            >
              返回登录
            </NuxtLink>
          </div>
        </div>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  title: '忘记密码',
  layout: false,
  middleware: 'guest'
})

// 表单数据
const form = reactive({
  email: ''
})

// 错误状态
const errors = reactive({
  email: ''
})

// 状态管理
const isLoading = ref(false)
const errorMessage = ref('')
const emailSent = ref(false)
const isResending = ref(false)
const resendCooldown = ref(0)

// API服务
const { api } = useApi()
const { showError, showSuccess } = useNotification()

// 表单验证
const validateEmail = () => {
  if (!form.email) {
    errors.email = '请输入邮箱地址'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.email = '请输入有效的邮箱地址'
  } else {
    errors.email = ''
  }
}

// 表单有效性
const isFormValid = computed(() => {
  return form.email && !errors.email
})

// 提交表单
const handleSubmit = async () => {
  validateEmail()
  
  if (!isFormValid.value) {
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    await api('/auth/forgot-password', {
      method: 'POST',
      body: { email: form.email }
    })

    emailSent.value = true
    showSuccess('密码重置邮件已发送')
    startResendCooldown()
  } catch (error: any) {
    errorMessage.value = error.message || '发送失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}

// 重新发送邮件
const resendEmail = async () => {
  if (resendCooldown.value > 0) return

  isResending.value = true
  
  try {
    await api('/auth/forgot-password', {
      method: 'POST',
      body: { email: form.email }
    })

    showSuccess('邮件已重新发送')
    startResendCooldown()
  } catch (error: any) {
    showError(error.message || '重新发送失败')
  } finally {
    isResending.value = false
  }
}

// 重发冷却时间
const startResendCooldown = () => {
  resendCooldown.value = 60
  const timer = setInterval(() => {
    resendCooldown.value--
    if (resendCooldown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// 页面标题
useHead({
  title: '忘记密码 - MusicDou',
  meta: [
    { name: 'description', content: '重置你的 MusicDou 账户密码' }
  ]
})
</script>
