<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <NuxtLink to="/public" class="flex items-center justify-center space-x-2 mb-6">
          <Icon name="MusicalNoteIcon" class="w-10 h-10 text-primary-500" />
          <span class="text-2xl font-bold text-gray-900 dark:text-white">
            MusicDou
          </span>
        </NuxtLink>
        <h2 class="text-3xl font-bold text-gray-900 dark:text-white">
          重置密码
        </h2>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
          请输入你的新密码
        </p>
      </div>

      <!-- Form -->
      <Card class="mt-8">
        <form v-if="!resetSuccess" class="space-y-6" @submit.prevent="handleSubmit">
          <!-- New Password -->
          <Input
            v-model="form.password"
            type="password"
            label="新密码"
            placeholder="请输入新密码"
            left-icon="LockClosedIcon"
            required
            :error="errors.password"
            @blur="validatePassword"
          />

          <!-- Confirm Password -->
          <Input
            v-model="form.confirmPassword"
            type="password"
            label="确认新密码"
            placeholder="请再次输入新密码"
            left-icon="LockClosedIcon"
            required
            :error="errors.confirmPassword"
            @blur="validateConfirmPassword"
          />

          <!-- Password Strength Indicator -->
          <div v-if="form.password" class="space-y-2">
            <div class="text-sm text-gray-600 dark:text-gray-400">
              密码强度：
              <span :class="passwordStrengthColor">{{ passwordStrengthText }}</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                :class="passwordStrengthColor.replace('text-', 'bg-')"
                class="h-2 rounded-full transition-all duration-300"
                :style="{ width: passwordStrengthWidth }"
              ></div>
            </div>
          </div>

          <!-- Error Message -->
          <div v-if="errorMessage" class="text-red-600 dark:text-red-400 text-sm">
            {{ errorMessage }}
          </div>

          <!-- Submit Button -->
          <Button
            type="submit"
            class="w-full"
            :loading="isLoading"
            :disabled="!isFormValid"
          >
            重置密码
          </Button>

          <!-- Back to Login -->
          <div class="text-center">
            <NuxtLink
              to="/login"
              class="text-primary-600 dark:text-primary-400 hover:text-primary-500 text-sm font-medium"
            >
              返回登录
            </NuxtLink>
          </div>
        </form>

        <!-- Success Message -->
        <div v-else class="text-center space-y-6">
          <div class="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
            <Icon name="CheckIcon" class="w-8 h-8 text-green-600 dark:text-green-400" />
          </div>
          
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              密码重置成功
            </h3>
            <p class="text-gray-600 dark:text-gray-400 text-sm">
              你的密码已成功重置。现在可以使用新密码登录了。
            </p>
          </div>

          <Button
            @click="$router.push('/login')"
            class="w-full"
          >
            前往登录
          </Button>
        </div>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  title: '重置密码',
  layout: false,
  middleware: 'guest'
})

// 获取URL参数中的token
const route = useRoute()
const token = route.query.token as string

// 如果没有token，重定向到忘记密码页面
if (!token) {
  throw createError({
    statusCode: 400,
    statusMessage: '无效的重置链接'
  })
}

// 表单数据
const form = reactive({
  password: '',
  confirmPassword: ''
})

// 错误状态
const errors = reactive({
  password: '',
  confirmPassword: ''
})

// 状态管理
const isLoading = ref(false)
const errorMessage = ref('')
const resetSuccess = ref(false)

// API服务
const { api } = useApi()
const { showError, showSuccess } = useNotification()

// 密码强度计算
const passwordStrength = computed(() => {
  const password = form.password
  if (!password) return 0

  let score = 0
  
  // 长度检查
  if (password.length >= 8) score += 1
  if (password.length >= 12) score += 1
  
  // 字符类型检查
  if (/[a-z]/.test(password)) score += 1
  if (/[A-Z]/.test(password)) score += 1
  if (/[0-9]/.test(password)) score += 1
  if (/[^A-Za-z0-9]/.test(password)) score += 1

  return Math.min(score, 4)
})

const passwordStrengthText = computed(() => {
  const strength = passwordStrength.value
  if (strength === 0) return '很弱'
  if (strength === 1) return '弱'
  if (strength === 2) return '一般'
  if (strength === 3) return '强'
  return '很强'
})

const passwordStrengthColor = computed(() => {
  const strength = passwordStrength.value
  if (strength === 0) return 'text-red-500'
  if (strength === 1) return 'text-orange-500'
  if (strength === 2) return 'text-yellow-500'
  if (strength === 3) return 'text-blue-500'
  return 'text-green-500'
})

const passwordStrengthWidth = computed(() => {
  return `${(passwordStrength.value / 4) * 100}%`
})

// 表单验证
const validatePassword = () => {
  if (!form.password) {
    errors.password = '请输入新密码'
  } else if (form.password.length < 8) {
    errors.password = '密码至少需要8个字符'
  } else if (passwordStrength.value < 2) {
    errors.password = '密码强度太弱，请包含大小写字母、数字或特殊字符'
  } else {
    errors.password = ''
  }
}

const validateConfirmPassword = () => {
  if (!form.confirmPassword) {
    errors.confirmPassword = '请确认新密码'
  } else if (form.password !== form.confirmPassword) {
    errors.confirmPassword = '两次输入的密码不一致'
  } else {
    errors.confirmPassword = ''
  }
}

// 表单有效性
const isFormValid = computed(() => {
  return form.password && 
         form.confirmPassword && 
         !errors.password && 
         !errors.confirmPassword &&
         passwordStrength.value >= 2
})

// 提交表单
const handleSubmit = async () => {
  validatePassword()
  validateConfirmPassword()
  
  if (!isFormValid.value) {
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    await api('/auth/reset-password', {
      method: 'POST',
      body: {
        token,
        password: form.password,
        confirmPassword: form.confirmPassword
      }
    })

    resetSuccess.value = true
    showSuccess('密码重置成功')
  } catch (error: any) {
    errorMessage.value = error.message || '密码重置失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}

// 页面标题
useHead({
  title: '重置密码 - MusicDou',
  meta: [
    { name: 'description', content: '重置你的 MusicDou 账户密码' }
  ]
})
</script>
