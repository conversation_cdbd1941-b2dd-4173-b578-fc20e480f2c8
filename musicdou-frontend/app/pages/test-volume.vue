<template>
  <div class="min-h-screen bg-gray-100 dark:bg-gray-900 p-8">
    <div class="max-w-2xl mx-auto">
      <h1 class="text-2xl font-bold mb-8 text-gray-900 dark:text-white">音量控制测试页面</h1>
      
      <!-- 测试音量控制 -->
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
        <h2 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">音量控制测试</h2>
        
        <div class="flex items-center gap-4 mb-4">
          <!-- 喇叭图标 -->
          <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="currentColor" viewBox="0 0 24 24">
            <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
          </svg>
          
          <!-- 音量滑块 -->
          <div 
            class="relative w-48 h-2 bg-gray-200 dark:bg-gray-600 rounded-full cursor-pointer group hover:h-3 transition-all duration-200" 
            @mousedown="handleVolumeMouseDown"
            @click="handleVolumeClick"
            @mouseenter="() => console.log('🖱️ Vue事件：鼠标进入音量条')"
            @mouseleave="() => console.log('🖱️ Vue事件：鼠标离开音量条')"
            ref="volumeContainer"
          >
            <div class="h-full bg-blue-500 rounded-full relative transition-all duration-200" :style="`width: ${volume}%`">
              <!-- 音量滑块圆点 -->
              <div class="absolute right-0 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-500 rounded-full opacity-0 group-hover:opacity-100 group-hover:w-5 group-hover:h-5 transition-all duration-200 shadow-lg cursor-grab active:cursor-grabbing"></div>
            </div>
          </div>
          
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300 min-w-[3rem]">{{ volume }}%</span>
        </div>
        
        <div class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
          <p>• 点击音量条任意位置设置音量</p>
          <p>• 按住并拖拽调节音量</p>
          <p>• 悬停查看圆点</p>
          <p>• 打开控制台查看事件日志</p>
        </div>
        
        <!-- 测试按钮 -->
        <div class="mt-6 flex gap-4">
          <button 
            @click="setVolume(0)"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          >
            静音
          </button>
          <button 
            @click="setVolume(50)"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            50%
          </button>
          <button 
            @click="setVolume(100)"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
          >
            100%
          </button>
        </div>
      </div>
      
      <!-- 返回主页 -->
      <div class="mt-8">
        <NuxtLink 
          to="/" 
          class="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
        >
          ← 返回主页
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 音量控制功能
const volume = ref(60) // 默认音量60%
const volumeContainer = ref<HTMLElement>()
const isDragging = ref(false)

// Vue音量控制事件处理
const updateVolume = (clientX: number) => {
  if (!volumeContainer.value) {
    console.log('❌ volumeContainer.value 为空')
    return
  }
  
  const rect = volumeContainer.value.getBoundingClientRect()
  const percentage = Math.max(0, Math.min(100, ((clientX - rect.left) / rect.width) * 100))
  volume.value = Math.round(percentage)
  console.log('🎵 Vue方法：音量更新为:', volume.value + '%')
}

const handleVolumeClick = (e: MouseEvent) => {
  console.log('👆 Vue事件：点击音量条')
  if (!isDragging.value) {
    updateVolume(e.clientX)
  }
}

const handleVolumeMouseDown = (e: MouseEvent) => {
  console.log('🖱️ Vue事件：鼠标按下音量条')
  isDragging.value = true
  updateVolume(e.clientX)
  e.preventDefault()
  
  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging.value) {
      console.log('🔄 Vue事件：拖拽中...', e.clientX)
      updateVolume(e.clientX)
    }
  }
  
  const handleMouseUp = () => {
    console.log('🖱️ Vue事件：鼠标释放')
    isDragging.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 设置音量的辅助函数
const setVolume = (newVolume: number) => {
  volume.value = newVolume
  console.log('🎵 按钮设置音量为:', volume.value + '%')
}

// 页面加载时的日志
onMounted(() => {
  console.log('🚀 测试页面 onMounted 被调用')
  console.log('📊 初始音量:', volume.value + '%')
  console.log('🎯 volumeContainer ref:', volumeContainer.value)
  
  // 延迟检查DOM元素
  nextTick(() => {
    console.log('⏰ nextTick - volumeContainer ref:', volumeContainer.value)
  })
})

// 页面元数据
useHead({
  title: '音量控制测试 - MusicDou'
})
</script>
