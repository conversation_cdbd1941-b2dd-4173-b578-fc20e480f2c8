<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">
        认证状态调试页面
      </h1>
      
      <!-- 用户状态 -->
      <Card class="mb-6">
        <div class="p-6">
          <h2 class="text-xl font-semibold mb-4">用户状态</h2>
          <div class="space-y-2">
            <div>
              <strong>是否登录:</strong> 
              <span :class="authStore.isLoggedIn ? 'text-green-600' : 'text-red-600'">
                {{ authStore.isLoggedIn ? '是' : '否' }}
              </span>
            </div>
            <div>
              <strong>用户对象:</strong>
              <pre class="mt-2 p-3 bg-gray-100 dark:bg-gray-800 rounded text-sm overflow-auto">{{ JSON.stringify(authStore.user, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </Card>

      <!-- Token 状态 -->
      <Card class="mb-6">
        <div class="p-6">
          <h2 class="text-xl font-semibold mb-4">Token 状态</h2>
          <div class="space-y-2">
            <div>
              <strong>Token 存在:</strong> 
              <span :class="!!token ? 'text-green-600' : 'text-red-600'">
                {{ !!token ? '是' : '否' }}
              </span>
            </div>
            <div v-if="token">
              <strong>Token 值:</strong>
              <pre class="mt-2 p-3 bg-gray-100 dark:bg-gray-800 rounded text-sm overflow-auto break-all">{{ token }}</pre>
            </div>
          </div>
        </div>
      </Card>

      <!-- 操作按钮 -->
      <Card class="mb-6">
        <div class="p-6">
          <h2 class="text-xl font-semibold mb-4">操作</h2>
          <div class="flex gap-4 flex-wrap">
            <Button @click="initAuth" :disabled="loading">
              <Icon v-if="loading" name="arrow-path" class="w-4 h-4 mr-2 animate-spin" />
              初始化认证状态
            </Button>
            <Button @click="fetchUserInfo" :disabled="loading">
              获取用户信息
            </Button>
            <Button @click="testApi" :disabled="loading">
              测试API调用
            </Button>
            <Button @click="clearAuth" variant="outline">
              清除认证状态
            </Button>
          </div>
        </div>
      </Card>

      <!-- API 测试结果 -->
      <Card v-if="apiResult">
        <div class="p-6">
          <h2 class="text-xl font-semibold mb-4">API 测试结果</h2>
          <pre class="p-3 bg-gray-100 dark:bg-gray-800 rounded text-sm overflow-auto">{{ JSON.stringify(apiResult, null, 2) }}</pre>
        </div>
      </Card>

      <!-- 错误信息 -->
      <Card v-if="error" class="border-red-200">
        <div class="p-6">
          <h2 class="text-xl font-semibold mb-4 text-red-600">错误信息</h2>
          <pre class="p-3 bg-red-50 dark:bg-red-900/20 rounded text-sm overflow-auto text-red-700 dark:text-red-300">{{ error }}</pre>
        </div>
      </Card>

      <!-- 日志 -->
      <Card v-if="logs.length > 0">
        <div class="p-6">
          <h2 class="text-xl font-semibold mb-4">操作日志</h2>
          <div class="space-y-2 max-h-64 overflow-y-auto">
            <div 
              v-for="(log, index) in logs" 
              :key="index"
              class="text-sm p-2 bg-gray-50 dark:bg-gray-800 rounded"
            >
              <span class="text-gray-500">{{ log.time }}</span> - {{ log.message }}
            </div>
          </div>
          <Button @click="clearLogs" size="sm" variant="outline" class="mt-4">
            清除日志
          </Button>
        </div>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
const authStore = useAuthStore()
const { getUserLikes } = useSocialApi()

const loading = ref(false)
const error = ref<string | null>(null)
const apiResult = ref<any>(null)
const logs = ref<Array<{ time: string, message: string }>>([])

// 获取token
const token = useCookie('auth-token')

// 添加日志
const addLog = (message: string) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message
  })
}

// 初始化认证状态
const initAuth = async () => {
  try {
    loading.value = true
    error.value = null
    addLog('开始初始化认证状态...')
    
    await authStore.initAuth()
    
    addLog('认证状态初始化完成')
    addLog(`用户信息: ${authStore.user ? '已获取' : '未获取'}`)
  } catch (err: any) {
    error.value = err.message || '初始化认证状态失败'
    addLog(`初始化失败: ${err.message}`)
  } finally {
    loading.value = false
  }
}

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    loading.value = true
    error.value = null
    addLog('开始获取用户信息...')
    
    await authStore.fetchUser()
    
    addLog('用户信息获取完成')
    addLog(`用户ID: ${authStore.user?.id || authStore.user?._id || '未知'}`)
  } catch (err: any) {
    error.value = err.message || '获取用户信息失败'
    addLog(`获取用户信息失败: ${err.message}`)
  } finally {
    loading.value = false
  }
}

// 测试API调用
const testApi = async () => {
  try {
    loading.value = true
    error.value = null
    apiResult.value = null
    addLog('开始测试API调用...')
    
    const userId = authStore.user?.id || authStore.user?._id
    if (!userId) {
      throw new Error('用户ID不存在')
    }
    
    addLog(`使用用户ID: ${userId}`)
    
    const response = await getUserLikes(userId, {
      page: 1,
      limit: 5,
      targetType: 'music'
    })
    
    apiResult.value = response
    addLog('API调用成功')
    addLog(`获取到 ${response.data?.likes?.length || 0} 条喜欢记录`)
  } catch (err: any) {
    error.value = err.message || 'API调用失败'
    addLog(`API调用失败: ${err.message}`)
  } finally {
    loading.value = false
  }
}

// 清除认证状态
const clearAuth = () => {
  authStore.resetAuth()
  apiResult.value = null
  error.value = null
  addLog('认证状态已清除')
}

// 清除日志
const clearLogs = () => {
  logs.value = []
}

// 页面标题
useHead({
  title: '认证状态调试 - MusicDou'
})
</script>
