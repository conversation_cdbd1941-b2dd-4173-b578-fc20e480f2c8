<template>
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">
        API 连接测试
      </h1>

      <!-- 连接状态 -->
      <div class="mb-8 p-4 rounded-lg" :class="connectionStatus.class">
        <div class="flex items-center">
          <component :is="connectionStatus.icon" class="w-6 h-6 mr-3" />
          <div>
            <h3 class="font-semibold">{{ connectionStatus.title }}</h3>
            <p class="text-sm">{{ connectionStatus.message }}</p>
          </div>
        </div>
      </div>

      <!-- 测试按钮组 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
        <button
          v-for="test in apiTests"
          :key="test.name"
          @click="runTest(test)"
          :disabled="test.loading"
          class="p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
        >
          <div class="flex items-center justify-between">
            <div class="text-left">
              <h3 class="font-semibold text-gray-900 dark:text-white">{{ test.name }}</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">{{ test.description }}</p>
            </div>
            <div class="ml-4">
              <div v-if="test.loading" class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <component v-else-if="test.status" :is="getStatusIcon(test.status)" :class="getStatusClass(test.status)" class="w-6 h-6" />
              <div v-else class="w-6 h-6 bg-gray-300 rounded-full"></div>
            </div>
          </div>
        </button>
      </div>

      <!-- 测试结果 -->
      <div class="space-y-4">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">测试结果</h2>
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 max-h-96 overflow-y-auto">
          <pre class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{{ testResults }}</pre>
        </div>
      </div>

      <!-- 清除按钮 -->
      <div class="mt-4 flex justify-end">
        <button
          @click="clearResults"
          class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          清除结果
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ExclamationTriangleIcon,
  WifiIcon,
  NoSymbolIcon
} from '@heroicons/vue/24/outline'

// 页面元数据
definePageMeta({
  title: 'API测试',
  description: '测试前后端API连接状态'
})

// 响应式数据
const testResults = ref('')
const isConnected = ref(false)

// API测试配置
const apiTests = ref([
  {
    name: '健康检查',
    description: '检查后端服务状态',
    endpoint: '/health',
    method: 'GET',
    loading: false,
    status: null as 'success' | 'error' | 'warning' | null
  },
  {
    name: 'API信息',
    description: '获取API版本信息',
    endpoint: '/api/v1',
    method: 'GET',
    loading: false,
    status: null as 'success' | 'error' | 'warning' | null
  },
  {
    name: '用户注册',
    description: '测试用户注册接口',
    endpoint: '/auth/register',
    method: 'POST',
    loading: false,
    status: null as 'success' | 'error' | 'warning' | null,
    data: {
      username: `testuser_${Date.now()}`,
      email: `test_${Date.now()}@example.com`,
      password: 'testpassword123',
      confirmPassword: 'testpassword123',
      agreeToTerms: true
    }
  },
  {
    name: '音乐列表',
    description: '获取音乐列表',
    endpoint: '/music',
    method: 'GET',
    loading: false,
    status: null as 'success' | 'error' | 'warning' | null
  },
  {
    name: '热门音乐',
    description: '获取热门音乐',
    endpoint: '/music/popular',
    method: 'GET',
    loading: false,
    status: null as 'success' | 'error' | 'warning' | null
  },
  {
    name: '音乐搜索',
    description: '搜索音乐功能',
    endpoint: '/music/search',
    method: 'GET',
    loading: false,
    status: null as 'success' | 'error' | 'warning' | null,
    params: { q: 'test', limit: 5 }
  }
])

// 计算属性
const connectionStatus = computed(() => {
  if (isConnected.value) {
    return {
      title: '连接正常',
      message: '前后端API连接成功',
      class: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      icon: WifiIcon
    }
  } else {
    return {
      title: '连接异常',
      message: '无法连接到后端API',
      class: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      icon: NoSymbolIcon
    }
  }
})

// 方法
const { get, post } = useApi()

const addResult = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  testResults.value += `[${timestamp}] ${message}\n`
}

const runTest = async (test: any) => {
  test.loading = true
  test.status = null
  
  addResult(`开始测试: ${test.name}`)
  
  try {
    let response
    
    if (test.method === 'GET') {
      response = await get(test.endpoint, test.params)
    } else if (test.method === 'POST') {
      response = await post(test.endpoint, test.data)
    }
    
    test.status = 'success'
    addResult(`✅ ${test.name} - 成功`)
    addResult(`响应: ${JSON.stringify(response, null, 2)}`)
    
    // 如果是健康检查成功，更新连接状态
    if (test.endpoint === '/health') {
      isConnected.value = true
    }
    
  } catch (error: any) {
    test.status = 'error'
    addResult(`❌ ${test.name} - 失败`)
    addResult(`错误: ${error.message || error}`)
    
    if (error.response) {
      addResult(`状态码: ${error.response.status}`)
      addResult(`响应: ${JSON.stringify(error.response.data, null, 2)}`)
    }
  } finally {
    test.loading = false
  }
}

const getStatusIcon = (status: string) => {
  const icons = {
    success: CheckCircleIcon,
    error: XCircleIcon,
    warning: ExclamationTriangleIcon
  }
  return icons[status as keyof typeof icons]
}

const getStatusClass = (status: string) => {
  const classes = {
    success: 'text-green-600',
    error: 'text-red-600',
    warning: 'text-yellow-600'
  }
  return classes[status as keyof typeof classes]
}

const clearResults = () => {
  testResults.value = ''
  apiTests.value.forEach(test => {
    test.status = null
  })
}

// 页面加载时自动运行健康检查
onMounted(() => {
  const healthCheck = apiTests.value.find(test => test.endpoint === '/health')
  if (healthCheck) {
    runTest(healthCheck)
  }
})
</script>
