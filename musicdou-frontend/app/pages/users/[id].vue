<template>
  <div class="user-profile-page min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 用户头部信息 -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-8">
          <!-- 加载状态 -->
          <div v-if="loading" class="animate-pulse">
            <div class="flex items-center space-x-6">
              <div class="w-24 h-24 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
              <div class="flex-1 space-y-3">
                <div class="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3"></div>
                <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-2/3"></div>
                <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
              </div>
            </div>
          </div>

          <!-- 用户信息 -->
          <div v-else-if="user" class="flex items-start space-x-6">
            <!-- 用户头像 -->
            <div class="flex-shrink-0">
              <div class="w-24 h-24 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white font-bold text-2xl overflow-hidden">
                <img 
                  v-if="user.avatar" 
                  :src="user.avatar" 
                  :alt="user.username"
                  class="w-full h-full object-cover"
                />
                <span v-else>
                  {{ user.username.charAt(0).toUpperCase() }}
                </span>
              </div>
            </div>

            <!-- 用户详情 -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-3 mb-2">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
                  {{ user.username }}
                </h1>
                
                <!-- 用户组标识 -->
                <span 
                  v-if="user.userGroup === 'vip'"
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                >
                  VIP
                </span>
                
                <span 
                  v-if="user.userGroup === 'admin'"
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                >
                  管理员
                </span>
              </div>

              <!-- 用户简介 -->
              <p v-if="user.bio" class="text-gray-600 dark:text-gray-400 mb-4">
                {{ user.bio }}
              </p>

              <!-- 统计信息 -->
              <div class="flex items-center space-x-8 mb-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {{ formatNumber(user.followersCount) }}
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">粉丝</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {{ formatNumber(user.followingCount) }}
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">关注</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {{ formatNumber(userStats?.totalPlays || 0) }}
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">播放</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {{ formatNumber(userStats?.totalLikes || 0) }}
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">点赞</div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="flex items-center space-x-3">
                <SocialFollowButton
                  :user-id="user.id"
                  size="md"
                  @followed="handleUserFollowed"
                  @unfollowed="handleUserUnfollowed"
                />
                
                <UiButton
                  variant="ghost"
                  @click="shareProfile"
                >
                  <UiIcon name="share" size="sm" />
                  分享
                </UiButton>
              </div>
            </div>
          </div>

          <!-- 错误状态 -->
          <div v-else class="text-center py-12">
            <UiIcon name="exclamation-triangle" size="xl" class="mx-auto text-gray-400 mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              用户不存在
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
              该用户可能已被删除或不存在
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户内容 -->
    <div v-if="user" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 内容标签页 -->
      <div class="border-b border-gray-200 dark:border-gray-700 mb-8">
        <nav class="-mb-px flex space-x-8">
          <button
            v-for="tab in tabs"
            :key="tab.key"
            @click="activeTab = tab.key"
            class="py-2 px-1 border-b-2 font-medium text-sm transition-colors"
            :class="activeTab === tab.key
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'"
          >
            {{ tab.label }}
            <span v-if="tab.count !== undefined" class="ml-2 text-xs">
              ({{ tab.count }})
            </span>
          </button>
        </nav>
      </div>

      <!-- 标签页内容 -->
      <div class="tab-content">
        <!-- 动态标签页 -->
        <div v-if="activeTab === 'activities'" class="space-y-4">
          <div v-if="loadingActivities && userActivities.length === 0" class="space-y-4">
            <div v-for="i in 3" :key="i" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 animate-pulse">
              <div class="flex space-x-4">
                <div class="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                <div class="flex-1 space-y-2">
                  <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/3"></div>
                  <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-2/3"></div>
                </div>
              </div>
            </div>
          </div>

          <div v-else-if="userActivities.length > 0">
            <SocialActivityItem
              v-for="activity in userActivities"
              :key="activity.id"
              :activity="activity"
              @like="handleActivityLike"
              @comment="handleActivityComment"
              @share="handleActivityShare"
            />
          </div>

          <div v-else class="text-center py-12">
            <UiIcon name="rss" size="xl" class="mx-auto text-gray-400 mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              暂无动态
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
              该用户还没有发布任何动态
            </p>
          </div>
        </div>

        <!-- 音乐标签页 -->
        <div v-else-if="activeTab === 'music'" class="space-y-4">
          <div v-if="userMusic.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <MusicMusicCard
              v-for="music in userMusic"
              :key="music.id"
              :music="music"
              @play="handlePlayMusic"
              @like="handleMusicLike"
            />
          </div>

          <div v-else class="text-center py-12">
            <UiIcon name="musical-note" size="xl" class="mx-auto text-gray-400 mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              暂无音乐
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
              该用户还没有上传任何音乐
            </p>
          </div>
        </div>

        <!-- 歌单标签页 -->
        <div v-else-if="activeTab === 'playlists'" class="space-y-4">
          <div v-if="userPlaylists.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <PlaylistPlaylistCard
              v-for="playlist in userPlaylists"
              :key="playlist.id"
              :playlist="playlist"
              @play="handlePlayPlaylist"
              @like="handlePlaylistLike"
            />
          </div>

          <div v-else class="text-center py-12">
            <UiIcon name="queue-list" size="xl" class="mx-auto text-gray-400 mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              暂无歌单
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
              该用户还没有创建任何歌单
            </p>
          </div>
        </div>

        <!-- 关注标签页 -->
        <div v-else-if="activeTab === 'following'" class="space-y-4">
          <div v-if="followingUsers.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UserUserCard
              v-for="followingUser in followingUsers"
              :key="followingUser.id"
              :user="followingUser"
              @followed="handleUserFollowed"
              @unfollowed="handleUserUnfollowed"
            />
          </div>

          <div v-else class="text-center py-12">
            <UiIcon name="users" size="xl" class="mx-auto text-gray-400 mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              暂无关注
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
              该用户还没有关注任何人
            </p>
          </div>
        </div>

        <!-- 粉丝标签页 -->
        <div v-else-if="activeTab === 'followers'" class="space-y-4">
          <div v-if="followers.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UserUserCard
              v-for="follower in followers"
              :key="follower.id"
              :user="follower"
              @followed="handleUserFollowed"
              @unfollowed="handleUserUnfollowed"
            />
          </div>

          <div v-else class="text-center py-12">
            <UiIcon name="users" size="xl" class="mx-auto text-gray-400 mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              暂无粉丝
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
              该用户还没有任何粉丝
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { User, UserStats, Music, Playlist } from '~/types'

const route = useRoute()
const { formatNumber } = useUtils()
const { showNotification } = useNotification()

// 响应式数据
const user = ref<User | null>(null)
const userStats = ref<UserStats | null>(null)
const userActivities = ref<any[]>([])
const userMusic = ref<Music[]>([])
const userPlaylists = ref<Playlist[]>([])
const followingUsers = ref<User[]>([])
const followers = ref<User[]>([])

const loading = ref(false)
const loadingActivities = ref(false)
const activeTab = ref('activities')

// 标签页配置
const tabs = computed(() => [
  { key: 'activities', label: '动态', count: userActivities.value.length },
  { key: 'music', label: '音乐', count: userMusic.value.length },
  { key: 'playlists', label: '歌单', count: userPlaylists.value.length },
  { key: 'following', label: '关注', count: user.value?.followingCount },
  { key: 'followers', label: '粉丝', count: user.value?.followersCount }
])

// 加载用户信息
const loadUser = async () => {
  try {
    loading.value = true
    const userId = route.params.id as string
    
    // 这里调用用户API
    // const response = await userApi.getUserById(userId)
    
    // 模拟用户数据
    user.value = {
      id: userId,
      username: 'musiclover',
      email: '<EMAIL>',
      avatar: '',
      bio: '热爱音乐的人，分享美好的声音',
      userGroup: 'normal',
      points: 1250,
      followersCount: 156,
      followingCount: 89,
      isActive: true,
      lastLoginAt: '2025-08-01T10:00:00Z',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2025-08-01T10:00:00Z'
    }

    // 加载用户统计
    userStats.value = {
      totalPlays: 12345,
      totalLikes: 567,
      totalShares: 89,
      totalComments: 234,
      totalPlaylists: 12,
      totalFollowers: 156,
      totalFollowing: 89
    }
  } catch (error) {
    console.error('Load user error:', error)
    user.value = null
  } finally {
    loading.value = false
  }
}

// 加载用户动态
const loadUserActivities = async () => {
  if (!user.value) return
  
  try {
    loadingActivities.value = true
    // 这里调用动态API
    // const response = await socialApi.getUserActivities(user.value.id)
    
    // 模拟动态数据
    userActivities.value = []
  } catch (error) {
    console.error('Load user activities error:', error)
  } finally {
    loadingActivities.value = false
  }
}

// 处理用户关注/取消关注
const handleUserFollowed = (userId: string) => {
  if (user.value && user.value.id === userId) {
    user.value.followersCount++
  }
}

const handleUserUnfollowed = (userId: string) => {
  if (user.value && user.value.id === userId) {
    user.value.followersCount = Math.max(0, user.value.followersCount - 1)
  }
}

// 分享用户资料
const shareProfile = async () => {
  if (!user.value) return
  
  try {
    const shareUrl = `${window.location.origin}/users/${user.value.id}`
    await navigator.clipboard.writeText(shareUrl)
    showNotification('链接已复制到剪贴板', 'success')
  } catch (error) {
    console.error('Share profile error:', error)
    showNotification('分享失败', 'error')
  }
}

// 处理各种交互
const handleActivityLike = (activityId: string) => {
  console.log('Like activity:', activityId)
}

const handleActivityComment = (activityId: string) => {
  console.log('Comment activity:', activityId)
}

const handleActivityShare = (activityId: string) => {
  console.log('Share activity:', activityId)
}

const handlePlayMusic = (music: Music) => {
  console.log('Play music:', music)
}

const handleMusicLike = (musicId: string) => {
  console.log('Like music:', musicId)
}

const handlePlayPlaylist = (playlist: Playlist) => {
  console.log('Play playlist:', playlist)
}

const handlePlaylistLike = (playlistId: string) => {
  console.log('Like playlist:', playlistId)
}

// 监听标签页变化
watch(activeTab, (newTab) => {
  if (newTab === 'activities' && userActivities.value.length === 0) {
    loadUserActivities()
  }
  // 其他标签页的数据加载逻辑
})

// 页面元数据
useHead(() => ({
  title: user.value ? `${user.value.username} - MusicDou` : '用户资料 - MusicDou',
  meta: [
    { name: 'description', content: user.value?.bio || '查看用户资料和音乐作品' }
  ]
}))

// 初始化
onMounted(() => {
  loadUser()
})

// 监听路由变化
watch(() => route.params.id, () => {
  loadUser()
})
</script>

<style scoped>
.user-profile-page {
  /* 自定义样式 */
}

.tab-content {
  min-height: 400px;
}
</style>
