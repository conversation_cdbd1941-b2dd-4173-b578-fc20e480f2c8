<template>
  <div class="h-full bg-gray-50 dark:bg-gray-900 overflow-y-auto">
    <!-- 页面头部 -->
    <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 mb-6">
      <div class="max-w-6xl mx-auto">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold mb-2 flex items-center gap-3">
              <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                <path d="M12 11L12 16L14 14L12 11M10 14L12 11L10 14"/>
              </svg>
              最新上传
            </h1>
            <p class="text-blue-100">
              发现社区最新上传的音乐作品
            </p>
          </div>
          <div class="text-right">
            <div class="text-sm text-blue-100">
              共 {{ totalCount }} 首歌曲
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-6xl mx-auto px-6 pb-6">
      <!-- 筛选选项 -->
      <div class="flex flex-wrap items-center gap-4 mb-6">
        <div class="flex items-center gap-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">流派:</label>
          <select 
            v-model="selectedGenre" 
            @change="handleGenreChange"
            class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="">全部</option>
            <option value="Pop">流行</option>
            <option value="Rock">摇滚</option>
            <option value="Jazz">爵士</option>
            <option value="Classical">古典</option>
            <option value="Electronic">电子</option>
            <option value="Hip-Hop">嘻哈</option>
            <option value="Folk">民谣</option>
            <option value="R&B">R&B</option>
          </select>
        </div>
        
        <div class="flex items-center gap-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">显示数量:</label>
          <select 
            v-model="pageSize" 
            @change="handlePageSizeChange"
            class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
          </select>
        </div>

        <button 
          @click="refreshData"
          :disabled="loading"
          class="px-4 py-2 text-sm bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white rounded-lg transition-colors flex items-center gap-2"
        >
          <svg class="w-4 h-4" :class="{ 'animate-spin': loading }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          刷新
        </button>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span class="ml-3 text-gray-600 dark:text-gray-400">加载中...</span>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="text-center py-12">
        <div class="text-red-500 mb-4">
          <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p class="text-lg font-medium">加载失败</p>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ error }}</p>
        </div>
        <button 
          @click="refreshData"
          class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
        >
          重试
        </button>
      </div>

      <!-- 音乐列表 -->
      <div v-else-if="musicList.length > 0" class="space-y-4">
        <div
          v-for="(track, index) in musicList"
          :key="track.id"
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 p-4"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-4 flex-1 min-w-0">
              <!-- 序号 -->
              <div class="flex-shrink-0 w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center text-sm font-medium text-gray-600 dark:text-gray-400">
                {{ index + 1 }}
              </div>

              <!-- 音乐信息 -->
              <div class="flex-1 min-w-0">
                <h3 class="font-medium text-gray-900 dark:text-white truncate">
                  {{ track.title }}
                </h3>
                <div class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mt-1">
                  <span>{{ track.artist }}</span>
                  <span>•</span>
                  <span>{{ track.album || '未知专辑' }}</span>
                  <span>•</span>
                  <span>{{ track.genre || '未知流派' }}</span>
                  <span>•</span>
                  <span>{{ formatDuration(track.duration) }}</span>
                </div>
                <div class="flex items-center gap-2 text-xs text-gray-400 dark:text-gray-500 mt-1">
                  <span>上传时间: {{ formatDate(track.createdAt) }}</span>
                  <span>•</span>
                  <span>播放: {{ track.playCount || 0 }}</span>
                  <span>•</span>
                  <span>音质: {{ getQualityLabel(track.quality) }}</span>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex items-center gap-2 flex-shrink-0">
              <button 
                @click="handlePlayTrack(track)"
                class="w-10 h-10 bg-blue-500 hover:bg-blue-600 text-white rounded-full flex items-center justify-center transition-colors"
                title="播放"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </button>
              
              <button 
                @click="handleLikeTrack(track)"
                class="w-10 h-10 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full flex items-center justify-center transition-colors"
                :class="track.isLiked ? 'text-red-500' : 'text-gray-400'"
                title="喜欢"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="text-center py-12">
        <div class="text-gray-400 dark:text-gray-500">
          <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
          </svg>
          <p class="text-lg font-medium">暂无音乐</p>
          <p class="text-sm mt-1">还没有用户上传音乐</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Music } from '~/types'

// 页面元数据
definePageMeta({
  title: '最新上传',
  layout: 'default',
  middleware: 'auth'
})

// 响应式数据
const musicList = ref<Music[]>([])
const loading = ref(true)
const error = ref('')
const selectedGenre = ref('')
const pageSize = ref(50)
const totalCount = ref(0)

// Composables
const { getLatestMusic, recordPlay, getMusicPlayUrl } = useMusicApi()
const { likeTarget, unlikeTarget, checkLikeStatus } = useSocialApi()
const playerStore = usePlayerStore()
const { handleApiError } = useErrorHandler()
const { success: showSuccess, error: showErrorNotification } = useNotification()

// 检查音乐喜欢状态
const checkMusicLikeStatus = async (musicList: Music[]) => {
  try {
    // 批量检查喜欢状态
    for (const music of musicList) {
      try {
        const response = await checkLikeStatus('music', music.id)
        if (response.success) {
          music.isLiked = response.data.isLiked
        }
      } catch (error) {
        // 忽略单个检查失败的错误
        console.warn(`检查音乐 ${music.id} 喜欢状态失败:`, error)
      }
    }
  } catch (error) {
    console.error('批量检查喜欢状态失败:', error)
  }
}

// 获取最新音乐数据
const fetchLatestMusic = async () => {
  try {
    loading.value = true
    error.value = ''
    
    const params: any = {
      limit: pageSize.value
    }
    
    if (selectedGenre.value) {
      params.genre = selectedGenre.value
    }
    
    const response = await getLatestMusic(params)
    
    if (response.success) {
      musicList.value = response.data || []
      totalCount.value = musicList.value.length

      // 检查喜欢状态
      if (musicList.value.length > 0) {
        await checkMusicLikeStatus(musicList.value)
      }
    } else {
      throw new Error(response.message || '获取音乐列表失败')
    }
  } catch (err: any) {
    error.value = err.message || '获取音乐列表失败'
    handleApiError(err)
  } finally {
    loading.value = false
  }
}

// 处理流派变化
const handleGenreChange = () => {
  fetchLatestMusic()
}

// 处理页面大小变化
const handlePageSizeChange = () => {
  fetchLatestMusic()
}

// 刷新数据
const refreshData = () => {
  fetchLatestMusic()
}

// 播放歌曲
const handlePlayTrack = async (track: Music) => {
  try {
    // 获取音乐播放URL
    const { data: urlData } = await getMusicPlayUrl(track.id)
    const trackWithUrl = { ...track, url: urlData.playUrl }

    // 记录播放行为
    await recordPlay(track.id)

    // 检查队列中是否已存在该歌曲
    const existingIndex = playerStore.state.queue.findIndex(t => t.id === track.id)

    if (existingIndex !== -1) {
      // 如果已存在，直接跳转到该歌曲
      playerStore.setCurrentIndex(existingIndex)
      showSuccess('已切换到该歌曲')
    } else {
      // 如果不存在，添加到播放队列底部
      playerStore.addToQueue(trackWithUrl)

      // 如果当前没有播放音乐，则开始播放新添加的歌曲
      if (!playerStore.hasCurrentTrack) {
        const audioPlayer = useAudioPlayer()
        await audioPlayer.playTrack(trackWithUrl)
      }

      showSuccess('已添加到播放队列')
    }
  } catch (error) {
    console.error('播放失败:', error)
    showErrorNotification('播放失败，请重试')
  }
}

// 喜欢歌曲
const handleLikeTrack = async (track: Music) => {
  try {
    if (track.isLiked) {
      // 取消喜欢
      await unlikeTarget('music', track.id)
      track.isLiked = false
      showSuccess('已从我喜欢的音乐中移除')
    } else {
      // 添加喜欢
      await likeTarget('music', track.id)
      track.isLiked = true
      showSuccess('已添加到我喜欢的音乐')
    }
  } catch (error) {
    console.error('操作失败:', error)
    showErrorNotification('操作失败，请重试')
  }
}

// 格式化时长
const formatDuration = (seconds: number): string => {
  if (!seconds) return '00:00'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 格式化日期
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) {
    return '今天'
  } else if (diffDays === 1) {
    return '昨天'
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

// 获取音质标签
const getQualityLabel = (quality: string): string => {
  const qualityMap: Record<string, string> = {
    'standard': '标准',
    'high': '高品质',
    'super': '超高品质',
    'lossless': '无损'
  }
  return qualityMap[quality] || '未知'
}

// 页面加载时获取数据
onMounted(() => {
  fetchLatestMusic()
})

// 页面标题
useHead({
  title: '最新上传 - MusicDou',
  meta: [
    { name: 'description', content: '发现社区最新上传的音乐作品' }
  ]
})
</script>
