<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
    <div class="max-w-md w-full mx-auto">
      <!-- 欢迎卡片 -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 text-center">
        <!-- 成功图标 -->
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 dark:bg-green-900 mb-6">
          <CheckCircleIcon class="h-8 w-8 text-green-600 dark:text-green-400" />
        </div>

        <!-- 欢迎标题 -->
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          欢迎回来！
        </h1>
        
        <!-- 用户信息 -->
        <div v-if="user" class="mb-6">
          <p class="text-lg text-gray-700 dark:text-gray-300 mb-2">
            {{ user.username || user.email }}
          </p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            登录成功，准备开始你的音乐之旅
          </p>
        </div>

        <!-- 加载状态 -->
        <div v-else class="mb-6">
          <div class="animate-pulse">
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mx-auto mb-2"></div>
            <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mx-auto"></div>
          </div>
        </div>

        <!-- 快速操作按钮 -->
        <div class="space-y-3 mb-6">
          <button
            @click="goToDiscover"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
          >
            <MusicalNoteIcon class="h-5 w-5 flex-shrink-0" />
            <span>发现音乐</span>
          </button>
          
          <button
            @click="goToProfile"
            class="w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
          >
            <UserIcon class="h-5 w-5 flex-shrink-0" />
            <span>个人中心</span>
          </button>
        </div>

        <!-- 自动跳转提示 -->
        <div class="text-center">
          <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">
            {{ countdown > 0 ? `${countdown} 秒后自动跳转到首页` : '正在跳转...' }}
          </p>
          <button
            @click="goToHome"
            class="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
          >
            立即跳转
          </button>
        </div>
      </div>

      <!-- 底部链接 -->
      <div class="mt-6 text-center">
        <p class="text-sm text-gray-500 dark:text-gray-400">
          需要帮助？
          <NuxtLink
            to="/help"
            class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium ml-1"
          >
            查看帮助中心
          </NuxtLink>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  CheckCircleIcon, 
  MusicalNoteIcon, 
  UserIcon 
} from '@heroicons/vue/24/outline'

// 页面元数据
definePageMeta({
  title: '欢迎回来',
  layout: false,
  middleware: 'auth'
})

// 认证状态
const { user } = useAuth()
const router = useRouter()

// 倒计时状态
const countdown = ref(5)
const countdownTimer = ref<NodeJS.Timeout | null>(null)

// 页面方法
const goToHome = () => {
  router.push('/')
}

const goToDiscover = () => {
  router.push('/discover')
}

const goToProfile = () => {
  router.push('/profile')
}

// 启动倒计时
const startCountdown = () => {
  countdownTimer.value = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value!)
      goToHome()
    }
  }, 1000)
}

// 页面挂载时启动倒计时
onMounted(() => {
  startCountdown()
})

// 页面卸载时清除倒计时
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
})

// 页面标题
useHead({
  title: '欢迎回来 - MusicDou',
  meta: [
    { name: 'description', content: '欢迎回来，开始你的音乐之旅' }
  ]
})
</script>

<style scoped>
/* 添加一些自定义动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
