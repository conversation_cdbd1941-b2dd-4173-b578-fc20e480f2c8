<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 搜索头部 -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex items-center space-x-4">
          <!-- 返回按钮 -->
          <button
            @click="$router.back()"
            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 
                   rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
          >
            <Icon name="ArrowLeftIcon" class="w-5 h-5" />
          </button>
          
          <!-- 搜索栏 -->
          <div class="flex-1">
            <SearchBar
              v-model="searchQuery"
              :auto-focus="!hasQuery"
              @search="handleSearch"
              @suggestion-select="handleSuggestionSelect"
            />
          </div>
          
          <!-- 筛选按钮 -->
          <button
            @click="showFilters = !showFilters"
            :class="[
              'p-2 rounded-full transition-colors duration-200',
              showFilters || hasActiveFilters
                ? 'text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900'
                : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
            ]"
          >
            <Icon name="AdjustmentsHorizontalIcon" class="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex gap-6">
        <!-- 侧边栏筛选器 -->
        <div class="w-80 flex-shrink-0">
          <Transition
            enter-active-class="transition-all duration-300 ease-out"
            enter-from-class="opacity-0 -translate-x-4"
            enter-to-class="opacity-100 translate-x-0"
            leave-active-class="transition-all duration-300 ease-in"
            leave-from-class="opacity-100 translate-x-0"
            leave-to-class="opacity-0 -translate-x-4"
          >
            <SearchFilters
              v-if="showFilters"
              :filters="searchFilters"
              @update:filters="updateFilters"
              @apply="applyFilters"
              @reset="resetFilters"
            />
          </Transition>
        </div>

        <!-- 主内容区域 -->
        <div class="flex-1 min-w-0">
          <!-- 搜索状态提示 -->
          <div v-if="searchQuery && !isLoading && searchStore.totalResults > 0" 
               class="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div class="flex items-center space-x-2">
              <Icon name="InformationCircleIcon" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              <span class="text-sm text-blue-800 dark:text-blue-200">
                为 "<strong>{{ searchQuery }}</strong>" 找到 {{ formatNumber(searchStore.totalResults) }} 个结果
                <span v-if="searchTime"> (用时 {{ searchTime }}ms)</span>
              </span>
            </div>
          </div>

          <!-- 搜索结果 -->
          <SearchResults
            :results="searchStore.results"
            :query="searchQuery"
            :is-loading="isLoading"
            :has-more="searchStore.hasMore"
            :total-results="searchStore.totalResults"
            :sort-by="searchFilters.sortBy"
            @load-more="loadMore"
            @sort-change="handleSortChange"
            @suggestion-click="handleSuggestionSelect"
            @play-music="handlePlayMusic"
            @like-music="handleLikeMusic"
            @add-to-playlist="handleAddToPlaylist"
          />
        </div>
      </div>
    </div>

    <!-- 快捷键提示 -->
    <div class="fixed bottom-4 right-4 z-50">
      <Transition
        enter-active-class="transition-all duration-300 ease-out"
        enter-from-class="opacity-0 scale-95 translate-y-2"
        enter-to-class="opacity-100 scale-100 translate-y-0"
        leave-active-class="transition-all duration-200 ease-in"
        leave-from-class="opacity-100 scale-100 translate-y-0"
        leave-to-class="opacity-0 scale-95 translate-y-2"
      >
        <div
          v-if="showKeyboardShortcuts"
          class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 
                 rounded-lg shadow-lg p-4 max-w-sm"
        >
          <div class="flex items-center justify-between mb-3">
            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">快捷键</h4>
            <button
              @click="showKeyboardShortcuts = false"
              class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <Icon name="XMarkIcon" class="w-4 h-4" />
            </button>
          </div>
          <div class="space-y-2 text-xs text-gray-600 dark:text-gray-400">
            <div class="flex justify-between">
              <span>聚焦搜索</span>
              <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">Ctrl+K</kbd>
            </div>
            <div class="flex justify-between">
              <span>重置筛选</span>
              <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">Ctrl+R</kbd>
            </div>
            <div class="flex justify-between">
              <span>切换筛选器</span>
              <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">Ctrl+F</kbd>
            </div>
          </div>
        </div>
      </Transition>
    </div>

    <!-- 快捷键触发按钮 -->
    <button
      @click="showKeyboardShortcuts = !showKeyboardShortcuts"
      class="fixed bottom-4 left-4 p-3 bg-gray-800 dark:bg-gray-700 text-white rounded-full 
             shadow-lg hover:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-200 z-50"
      title="快捷键帮助"
    >
      <Icon name="QuestionMarkCircleIcon" class="w-5 h-5" />
    </button>
  </div>
</template>

<script setup lang="ts">
import type { SearchFilters, Music } from '~/types'

// 页面元数据
definePageMeta({
  title: '搜索',
  description: '搜索音乐、歌单和用户'
})

// 搜索相关状态
const searchStore = useSearchStore()
const { search, loadMore: loadMoreResults } = useSearchApi()
const route = useRoute()
const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const isLoading = ref(false)
const showFilters = ref(false)
const showKeyboardShortcuts = ref(false)
const searchTime = ref<number>()

// 搜索筛选器
const searchFilters = ref<SearchFilters>({
  type: 'all',
  sortBy: 'relevance',
  dateRange: 'all',
  quality: 'all'
})

// 计算属性
const hasQuery = computed(() => !!searchQuery.value.trim())
const hasActiveFilters = computed(() => {
  return searchFilters.value.type !== 'all' ||
         searchFilters.value.sortBy !== 'relevance' ||
         searchFilters.value.dateRange !== 'all' ||
         searchFilters.value.quality !== 'all' ||
         !!searchFilters.value.genre ||
         !!searchFilters.value.language ||
         !!searchFilters.value.duration
})

// 初始化搜索查询
onMounted(() => {
  const query = route.query.q as string
  if (query) {
    searchQuery.value = query
    performSearch(query)
  }
  
  // 显示筛选器如果有活跃筛选条件
  if (hasActiveFilters.value) {
    showFilters.value = true
  }
})

// 监听路由查询参数变化
watch(() => route.query.q, (newQuery) => {
  if (newQuery && newQuery !== searchQuery.value) {
    searchQuery.value = newQuery as string
    performSearch(newQuery as string)
  }
})

// 执行搜索
const performSearch = async (query: string) => {
  if (!query.trim()) return
  
  isLoading.value = true
  const startTime = Date.now()
  
  try {
    await searchStore.search(query, true)
    searchTime.value = Date.now() - startTime
    
    // 更新URL
    if (route.query.q !== query) {
      await router.replace({ query: { ...route.query, q: query } })
    }
  } catch (error) {
    console.error('搜索失败:', error)
    // 这里可以显示错误提示
  } finally {
    isLoading.value = false
  }
}

// 处理搜索
const handleSearch = (query: string) => {
  searchQuery.value = query
  performSearch(query)
}

// 处理建议选择
const handleSuggestionSelect = (suggestion: string) => {
  searchQuery.value = suggestion
  performSearch(suggestion)
}

// 更新筛选器
const updateFilters = (filters: SearchFilters) => {
  searchFilters.value = { ...filters }
}

// 应用筛选器
const applyFilters = (filters: SearchFilters) => {
  searchFilters.value = { ...filters }
  if (searchQuery.value.trim()) {
    performSearch(searchQuery.value)
  }
}

// 重置筛选器
const resetFilters = () => {
  searchFilters.value = {
    type: 'all',
    sortBy: 'relevance',
    dateRange: 'all',
    quality: 'all'
  }
  if (searchQuery.value.trim()) {
    performSearch(searchQuery.value)
  }
}

// 处理排序变化
const handleSortChange = (sortBy: string) => {
  searchFilters.value.sortBy = sortBy as any
  if (searchQuery.value.trim()) {
    performSearch(searchQuery.value)
  }
}

// 加载更多
const loadMore = async () => {
  if (!searchStore.hasMore || isLoading.value) return
  
  try {
    await searchStore.loadMore()
  } catch (error) {
    console.error('加载更多失败:', error)
  }
}

// 处理音乐播放
const handlePlayMusic = (music: Music) => {
  // 这里集成音乐播放器
  console.log('播放音乐:', music)
}

// 处理音乐点赞
const handleLikeMusic = (music: Music) => {
  // 这里处理点赞逻辑
  console.log('点赞音乐:', music)
}

// 处理添加到歌单
const handleAddToPlaylist = (music: Music) => {
  // 这里打开添加到歌单的模态框
  console.log('添加到歌单:', music)
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// 键盘快捷键
onMounted(() => {
  const handleKeydown = (event: KeyboardEvent) => {
    // Ctrl/Cmd + F 切换筛选器
    if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
      event.preventDefault()
      showFilters.value = !showFilters.value
    }
    
    // Ctrl/Cmd + R 重置筛选器
    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
      event.preventDefault()
      resetFilters()
    }
    
    // ? 显示快捷键帮助
    if (event.key === '?' && !event.ctrlKey && !event.metaKey) {
      showKeyboardShortcuts.value = !showKeyboardShortcuts.value
    }
  }
  
  document.addEventListener('keydown', handleKeydown)
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
})

// SEO优化
useHead({
  title: computed(() => {
    if (searchQuery.value) {
      return `搜索: ${searchQuery.value} - MusicDou`
    }
    return '搜索 - MusicDou'
  }),
  meta: [
    {
      name: 'description',
      content: computed(() => {
        if (searchQuery.value) {
          return `在MusicDou中搜索"${searchQuery.value}"的相关音乐、歌单和用户`
        }
        return '在MusicDou中搜索您喜欢的音乐、歌单和用户'
      })
    }
  ]
})
</script>

<style scoped>
/* 搜索页面特定样式 */
.sticky {
  backdrop-filter: blur(8px);
}

/* 快捷键样式 */
kbd {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.75rem;
  font-weight: 600;
}

/* 过渡动画 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .w-80 {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 50;
    background: rgba(0, 0, 0, 0.5);
    padding: 1rem;
  }
  
  .w-80 > div {
    max-width: 400px;
    margin: 0 auto;
    margin-top: 2rem;
  }
}

@media (max-width: 640px) {
  .flex.gap-6 {
    flex-direction: column;
    gap: 1rem;
  }
  
  .w-80 {
    width: 100%;
  }
}
</style>
