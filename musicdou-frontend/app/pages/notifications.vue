<template>
  <div class="notifications-page min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 页面头部 -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
                通知中心
              </h1>
              <p class="mt-2 text-gray-600 dark:text-gray-400">
                查看你的所有通知和消息
              </p>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex items-center space-x-3">
              <UiButton
                v-if="unreadCount > 0"
                variant="ghost"
                size="sm"
                @click="markAllAsRead"
                :loading="markingAllRead"
              >
                全部标记为已读
              </UiButton>
              
              <UiButton
                variant="ghost"
                size="sm"
                @click="showSettings = true"
              >
                <UiIcon name="cog-6-tooth" size="sm" />
                设置
              </UiButton>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 通知筛选 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
        <div class="flex items-center space-x-4">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">筛选:</span>
          
          <div class="flex items-center space-x-2">
            <button
              v-for="filter in notificationFilters"
              :key="filter.value"
              @click="currentFilter = filter.value"
              class="px-3 py-1 text-sm rounded-full transition-colors"
              :class="currentFilter === filter.value 
                ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300' 
                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'"
            >
              {{ filter.label }}
              <span v-if="filter.count > 0" class="ml-1 text-xs">
                ({{ filter.count }})
              </span>
            </button>
          </div>
        </div>
      </div>

      <!-- 通知列表 -->
      <div class="space-y-2">
        <!-- 加载状态 -->
        <div v-if="loading && notifications.length === 0" class="space-y-2">
          <div v-for="i in 5" :key="i" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 animate-pulse">
            <div class="flex space-x-3">
              <div class="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
              <div class="flex-1 space-y-2">
                <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
                <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 通知项 -->
        <div v-else-if="filteredNotifications.length > 0">
          <div
            v-for="notification in filteredNotifications"
            :key="notification.id"
            class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 cursor-pointer transition-all hover:shadow-md"
            :class="{ 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800': !notification.isRead }"
            @click="handleNotificationClick(notification)"
          >
            <div class="flex space-x-3">
              <!-- 通知图标 -->
              <div class="flex-shrink-0">
                <div 
                  class="w-10 h-10 rounded-full flex items-center justify-center"
                  :class="getNotificationIconClass(notification.type)"
                >
                  <UiIcon :name="getNotificationIcon(notification.type)" size="sm" class="text-white" />
                </div>
              </div>

              <!-- 通知内容 -->
              <div class="flex-1 min-w-0">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {{ notification.title }}
                    </h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {{ notification.message }}
                    </p>
                    
                    <!-- 来源用户 -->
                    <div v-if="notification.fromUser" class="flex items-center space-x-2 mt-2">
                      <div class="w-6 h-6 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white font-semibold text-xs overflow-hidden">
                        <img 
                          v-if="notification.fromUser.avatar" 
                          :src="notification.fromUser.avatar" 
                          :alt="notification.fromUser.username"
                          class="w-full h-full object-cover"
                        />
                        <span v-else>
                          {{ notification.fromUser.username.charAt(0).toUpperCase() }}
                        </span>
                      </div>
                      <span class="text-xs text-gray-500 dark:text-gray-400">
                        来自 {{ notification.fromUser.username }}
                      </span>
                    </div>
                  </div>
                  
                  <!-- 时间和状态 -->
                  <div class="flex items-center space-x-2 ml-4">
                    <span class="text-xs text-gray-500 dark:text-gray-400">
                      {{ formatTimeAgo(notification.createdAt) }}
                    </span>
                    <div 
                      v-if="!notification.isRead"
                      class="w-2 h-2 bg-blue-500 rounded-full"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12 text-center">
          <UiIcon name="bell-slash" size="xl" class="mx-auto text-gray-400 mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            暂无通知
          </h3>
          <p class="text-gray-600 dark:text-gray-400">
            当有新的互动时，通知会显示在这里
          </p>
        </div>
      </div>

      <!-- 加载更多 -->
      <div v-if="hasMore" class="mt-6 text-center">
        <UiButton
          variant="ghost"
          :loading="loadingMore"
          @click="loadMore"
        >
          加载更多通知
        </UiButton>
      </div>
    </div>

    <!-- 通知设置模态框 -->
    <UiModal
      v-model="showSettings"
      title="通知设置"
      size="md"
    >
      <div class="space-y-6">
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            通知类型
          </h3>
          
          <div class="space-y-3">
            <div
              v-for="setting in notificationSettings"
              :key="setting.type"
              class="flex items-center justify-between"
            >
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ setting.label }}
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  {{ setting.description }}
                </p>
              </div>
              
              <input
                v-model="setting.enabled"
                type="checkbox"
                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>
          </div>
        </div>
        
        <div class="flex justify-end space-x-3">
          <UiButton
            variant="ghost"
            @click="showSettings = false"
          >
            取消
          </UiButton>
          <UiButton
            variant="primary"
            @click="saveSettings"
            :loading="savingSettings"
          >
            保存设置
          </UiButton>
        </div>
      </div>
    </UiModal>
  </div>
</template>

<script setup lang="ts">
import type { Notification } from '~/types'

// 页面元数据
definePageMeta({
  title: '通知中心 - MusicDou',
  description: '查看你的所有通知和消息',
  middleware: 'auth'
})

const { formatTimeAgo } = useUtils()
const { showNotification } = useNotification()

// 响应式数据
const notifications = ref<Notification[]>([])
const loading = ref(false)
const loadingMore = ref(false)
const markingAllRead = ref(false)
const savingSettings = ref(false)
const showSettings = ref(false)
const currentFilter = ref('all')
const currentPage = ref(1)
const hasMore = ref(false)

// 通知筛选选项
const notificationFilters = ref([
  { label: '全部', value: 'all', count: 0 },
  { label: '点赞', value: 'like', count: 0 },
  { label: '评论', value: 'comment', count: 0 },
  { label: '关注', value: 'follow', count: 0 },
  { label: '分享', value: 'playlist_share', count: 0 }
])

// 通知设置
const notificationSettings = ref([
  { type: 'like', label: '点赞通知', description: '当有人点赞你的内容时通知', enabled: true },
  { type: 'comment', label: '评论通知', description: '当有人评论你的内容时通知', enabled: true },
  { type: 'follow', label: '关注通知', description: '当有人关注你时通知', enabled: true },
  { type: 'playlist_share', label: '分享通知', description: '当有人分享你的歌单时通知', enabled: true }
])

// 计算属性
const unreadCount = computed(() => {
  return notifications.value.filter(n => !n.isRead).length
})

const filteredNotifications = computed(() => {
  if (currentFilter.value === 'all') {
    return notifications.value
  }
  return notifications.value.filter(n => n.type === currentFilter.value)
})

// 获取通知图标
const getNotificationIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    'like': 'heart',
    'comment': 'chat-bubble-left',
    'follow': 'user-plus',
    'playlist_share': 'share'
  }
  return iconMap[type] || 'bell'
}

// 获取通知图标样式
const getNotificationIconClass = (type: string) => {
  const classMap: Record<string, string> = {
    'like': 'bg-red-500',
    'comment': 'bg-blue-500',
    'follow': 'bg-green-500',
    'playlist_share': 'bg-purple-500'
  }
  return classMap[type] || 'bg-gray-500'
}

// 加载通知
const loadNotifications = async (page = 1, append = false) => {
  try {
    if (page === 1) {
      loading.value = true
    } else {
      loadingMore.value = true
    }

    // 这里调用通知API
    // const response = await notificationApi.getNotifications({ page, limit: 20 })
    
    // 模拟API响应
    const mockNotifications: Notification[] = [
      {
        id: '1',
        type: 'like',
        title: '新的点赞',
        message: '你的音乐《夜空中最亮的星》收到了新的点赞',
        isRead: false,
        userId: 'current-user',
        fromUser: {
          id: 'user1',
          username: 'musiclover',
          email: '<EMAIL>',
          avatar: '',
          userGroup: 'normal',
          points: 100,
          followersCount: 50,
          followingCount: 30,
          isActive: true,
          createdAt: '2025-01-01',
          updatedAt: '2025-01-01'
        },
        targetId: 'music1',
        targetType: 'music',
        createdAt: '2025-08-01T10:00:00Z'
      }
    ]

    if (append) {
      notifications.value.push(...mockNotifications)
    } else {
      notifications.value = mockNotifications
    }
    
    // 更新筛选计数
    updateFilterCounts()
    
    hasMore.value = false // 模拟没有更多数据
    currentPage.value = page
  } catch (error) {
    console.error('Load notifications error:', error)
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 更新筛选计数
const updateFilterCounts = () => {
  notificationFilters.value.forEach(filter => {
    if (filter.value === 'all') {
      filter.count = notifications.value.length
    } else {
      filter.count = notifications.value.filter(n => n.type === filter.value).length
    }
  })
}

// 处理通知点击
const handleNotificationClick = async (notification: Notification) => {
  // 标记为已读
  if (!notification.isRead) {
    notification.isRead = true
    // 这里调用标记已读的API
  }
  
  // 根据通知类型跳转到相应页面
  if (notification.targetType === 'music' && notification.targetId) {
    await navigateTo(`/music/${notification.targetId}`)
  } else if (notification.targetType === 'playlist' && notification.targetId) {
    await navigateTo(`/playlists/${notification.targetId}`)
  } else if (notification.fromUser) {
    await navigateTo(`/users/${notification.fromUser.id}`)
  }
}

// 全部标记为已读
const markAllAsRead = async () => {
  try {
    markingAllRead.value = true
    
    // 这里调用标记全部已读的API
    notifications.value.forEach(n => n.isRead = true)
    
    showNotification('所有通知已标记为已读', 'success')
  } catch (error) {
    console.error('Mark all as read error:', error)
    showNotification('操作失败', 'error')
  } finally {
    markingAllRead.value = false
  }
}

// 保存设置
const saveSettings = async () => {
  try {
    savingSettings.value = true
    
    // 这里调用保存设置的API
    
    showNotification('设置已保存', 'success')
    showSettings.value = false
  } catch (error) {
    console.error('Save settings error:', error)
    showNotification('保存失败', 'error')
  } finally {
    savingSettings.value = false
  }
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loadingMore.value) {
    loadNotifications(currentPage.value + 1, true)
  }
}

// 初始化
onMounted(() => {
  loadNotifications()
})

// 监听筛选变化
watch(currentFilter, () => {
  // 可以在这里重新加载数据或者只是客户端筛选
})
</script>

<style scoped>
.notifications-page {
  /* 自定义样式 */
}
</style>
