<template>
  <div class="p-6">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">
        滚动测试页面
      </h1>
      
      <div class="space-y-8">
        <!-- 测试内容块 -->
        <div v-for="i in 50" :key="i" class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            内容块 {{ i }}
          </h2>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            这是第 {{ i }} 个内容块。这个页面用来测试固定顶部导航栏和底部播放器的效果。
            当你滚动这个页面时，顶部的导航栏和底部的播放器应该保持固定位置，只有中间的内容区域会滚动。
          </p>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <h3 class="font-medium text-blue-900 dark:text-blue-100 mb-2">功能特点</h3>
              <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <li>• 顶部导航栏固定</li>
                <li>• 底部播放器固定</li>
                <li>• 中间内容可滚动</li>
                <li>• 左侧导航栏内容可滚动</li>
              </ul>
            </div>
            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <h3 class="font-medium text-green-900 dark:text-green-100 mb-2">布局结构</h3>
              <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                <li>• 使用 Flexbox 布局</li>
                <li>• h-screen 限制总高度</li>
                <li>• overflow-hidden 防止整体滚动</li>
                <li>• flex-shrink-0 固定头部底部</li>
              </ul>
            </div>
          </div>
        </div>
        
        <!-- 最后一个特殊块 -->
        <div class="bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg p-8 text-center">
          <h2 class="text-2xl font-bold mb-4">🎉 滚动到底部了！</h2>
          <p class="text-lg opacity-90">
            如果你能看到这个消息，说明滚动功能正常工作。
            顶部导航栏和底部播放器应该始终保持可见。
          </p>
          <button class="mt-4 bg-white text-purple-600 px-6 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
            返回顶部
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: 'auth'
})

useHead({
  title: '滚动测试 - MusicDou',
  meta: [
    { name: 'description', content: '测试固定布局和滚动效果' }
  ]
})
</script>
