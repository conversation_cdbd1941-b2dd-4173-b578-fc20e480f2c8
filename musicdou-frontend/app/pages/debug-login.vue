<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center">
    <div class="max-w-md w-full bg-white rounded-lg shadow-md p-6">
      <h1 class="text-2xl font-bold text-center mb-6">调试登录</h1>
      
      <form @submit.prevent="handleLogin" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            邮箱
          </label>
          <input
            v-model="form.email"
            type="email"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="<EMAIL>"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            密码
          </label>
          <input
            v-model="form.password"
            type="password"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="password123"
          />
        </div>
        
        <button
          type="submit"
          :disabled="isLoading"
          class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {{ isLoading ? '登录中...' : '登录' }}
        </button>
      </form>
      
      <div v-if="error" class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
        {{ error }}
      </div>
      
      <div v-if="success" class="mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
        {{ success }}
      </div>
      
      <div class="mt-6 space-y-2">
        <h3 class="font-medium">调试信息:</h3>
        <div class="text-sm text-gray-600">
          <p>API Base URL: {{ config.public.apiBase }}</p>
          <p>当前Token: {{ token ? token.substring(0, 20) + '...' : '无' }}</p>
          <p>用户状态: {{ user ? user.username : '未登录' }}</p>
        </div>
      </div>
      
      <div class="mt-4">
        <button
          @click="testApiConnection"
          class="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700"
        >
          测试API连接
        </button>
      </div>
      
      <div v-if="apiTestResult" class="mt-4 p-3 bg-blue-100 border border-blue-400 text-blue-700 rounded">
        <pre>{{ apiTestResult }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  title: '调试登录',
  layout: false
})

// 响应式数据
const form = reactive({
  email: '<EMAIL>',
  password: 'password123'
})

const isLoading = ref(false)
const error = ref('')
const success = ref('')
const apiTestResult = ref('')

// 获取配置和状态
const config = useRuntimeConfig()
const { login, user } = useAuth()
const token = useCookie('auth-token')

// 登录处理
const handleLogin = async () => {
  isLoading.value = true
  error.value = ''
  success.value = ''
  
  try {
    console.log('开始登录...', form)
    
    await login({
      email: form.email,
      password: form.password,
      rememberMe: false
    })
    
    success.value = '登录成功！'
    console.log('登录成功')
  } catch (err: any) {
    error.value = err.message || '登录失败'
    console.error('登录错误:', err)
  } finally {
    isLoading.value = false
  }
}

// 测试API连接
const testApiConnection = async () => {
  try {
    const response = await fetch(`${config.public.apiBase}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        identifier: form.email,
        password: form.password
      })
    })
    
    const data = await response.json()
    apiTestResult.value = JSON.stringify(data, null, 2)
  } catch (err: any) {
    apiTestResult.value = `错误: ${err.message}`
  }
}

// 页面标题
useHead({
  title: '调试登录 - MusicDou'
})
</script>
