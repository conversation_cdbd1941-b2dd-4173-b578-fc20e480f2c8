<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          UI Components Demo
        </h1>
        <p class="text-lg text-gray-600 dark:text-gray-400">
          MusicDou 前端组件库演示
        </p>
      </div>

      <!-- Button Demo -->
      <Card title="Button 组件" class="mb-8">
        <div class="space-y-6">
          <!-- Variants -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">变体样式</h4>
            <div class="flex flex-wrap gap-3">
              <Button variant="primary">Primary</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="danger">Danger</Button>
            </div>
          </div>

          <!-- Sizes -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">尺寸</h4>
            <div class="flex flex-wrap items-center gap-3">
              <Button size="sm">Small</Button>
              <Button size="md">Medium</Button>
              <Button size="lg">Large</Button>
              <Button size="xl">Extra Large</Button>
            </div>
          </div>

          <!-- States -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">状态</h4>
            <div class="flex flex-wrap gap-3">
              <Button>Normal</Button>
              <Button disabled>Disabled</Button>
              <Button :loading="buttonLoading" @click="toggleButtonLoading">
                {{ buttonLoading ? 'Loading...' : 'Click to Load' }}
              </Button>
            </div>
          </div>

          <!-- With Icons -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">图标按钮</h4>
            <div class="flex flex-wrap gap-3">
              <Button icon="PlusIcon">Add Item</Button>
              <Button icon="TrashIcon" variant="danger">Delete</Button>
              <Button icon="ArrowDownTrayIcon" variant="outline" icon-position="right">Download</Button>
            </div>
          </div>
        </div>
      </Card>

      <!-- Input Demo -->
      <Card title="Input 组件" class="mb-8">
        <div class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              v-model="inputValue"
              label="基础输入"
              placeholder="请输入内容"
              help-text="这是帮助文本"
            />
            <Input
              v-model="passwordValue"
              type="password"
              label="密码输入"
              placeholder="请输入密码"
              required
            />
            <Input
              v-model="searchValue"
              type="search"
              label="搜索输入"
              placeholder="搜索..."
              left-icon="MagnifyingGlassIcon"
              clearable
            />
            <Input
              v-model="errorValue"
              label="错误状态"
              placeholder="输入内容"
              error="这是错误信息"
            />
          </div>
        </div>
      </Card>

      <!-- Card Demo -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <Card title="默认卡片" variant="default">
          <p class="text-gray-600 dark:text-gray-400">
            这是一个默认样式的卡片组件，包含标题和内容区域。
          </p>
          <template #footer>
            <div class="flex justify-end">
              <Button size="sm">操作</Button>
            </div>
          </template>
        </Card>

        <Card title="轮廓卡片" variant="outlined">
          <p class="text-gray-600 dark:text-gray-400">
            这是一个轮廓样式的卡片，只有边框没有背景色。
          </p>
        </Card>

        <Card title="悬停效果" hover clickable @click="showToast('success', '卡片被点击了！')">
          <p class="text-gray-600 dark:text-gray-400">
            这个卡片支持悬停效果和点击事件。
          </p>
        </Card>
      </div>

      <!-- Loading Demo -->
      <Card title="Loading 组件" class="mb-8">
        <div class="space-y-6">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div class="text-center">
              <h5 class="text-sm font-medium mb-3">Spinner</h5>
              <Loading type="spinner" />
            </div>
            <div class="text-center">
              <h5 class="text-sm font-medium mb-3">Dots</h5>
              <Loading type="dots" />
            </div>
            <div class="text-center">
              <h5 class="text-sm font-medium mb-3">Bars</h5>
              <Loading type="bars" />
            </div>
            <div class="text-center">
              <h5 class="text-sm font-medium mb-3">Pulse</h5>
              <Loading type="pulse" />
            </div>
          </div>
        </div>
      </Card>

      <!-- Interactive Demo -->
      <Card title="交互演示" class="mb-8">
        <div class="space-y-4">
          <div class="flex flex-wrap gap-3">
            <Button @click="showModal = true">打开模态框</Button>
            <Button @click="showToast('info', '这是一个信息提示')">显示 Toast</Button>
            <Button @click="showToast('success', '操作成功！')">成功提示</Button>
            <Button @click="showToast('warning', '警告信息')">警告提示</Button>
            <Button @click="showToast('error', '错误信息')">错误提示</Button>
          </div>
        </div>
      </Card>
    </div>

    <!-- Modal Demo -->
    <Modal v-model="showModal" title="演示模态框" size="md">
      <div class="space-y-4">
        <p class="text-gray-600 dark:text-gray-400">
          这是一个模态框组件的演示。它支持多种尺寸、可配置的关闭方式，以及键盘导航。
        </p>
        <Input
          v-model="modalInput"
          label="模态框中的输入"
          placeholder="在模态框中输入内容"
        />
      </div>
      <template #footer>
        <div class="flex justify-end space-x-3">
          <Button variant="outline" @click="showModal = false">取消</Button>
          <Button @click="handleModalConfirm">确认</Button>
        </div>
      </template>
    </Modal>

    <!-- Toast Container -->
    <Toast ref="toastRef" />
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  title: 'Components Demo'
})

// 响应式数据
const buttonLoading = ref(false)
const inputValue = ref('')
const passwordValue = ref('')
const searchValue = ref('')
const errorValue = ref('')
const showModal = ref(false)
const modalInput = ref('')
const toastRef = ref()

// 方法
const toggleButtonLoading = () => {
  buttonLoading.value = true
  setTimeout(() => {
    buttonLoading.value = false
  }, 2000)
}

const showToast = (type: 'success' | 'error' | 'warning' | 'info', message: string) => {
  if (toastRef.value) {
    toastRef.value.addToast({
      type,
      message,
      duration: 3000
    })
  }
}

const handleModalConfirm = () => {
  showToast('success', `模态框确认，输入内容：${modalInput.value || '无'}`)
  showModal.value = false
  modalInput.value = ''
}
</script>
