<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <!-- 面包屑导航 -->
      <nav class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-6">
        <NuxtLink
          to="/"
          class="flex items-center hover:text-gray-700 dark:hover:text-gray-200 transition-colors"
        >
          <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
          </svg>
          首页
        </NuxtLink>
        <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
          <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
        </svg>
        <span class="text-gray-900 dark:text-white font-medium">个人资料</span>
      </nav>

      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          个人资料
        </h1>
        <p class="mt-2 text-gray-600 dark:text-gray-400">
          管理你的账户信息和偏好设置
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 侧边栏导航 -->
        <div class="lg:col-span-1">
          <Card class="p-6">
            <nav class="space-y-2">
              <button
                v-for="tab in tabs"
                :key="tab.id"
                @click="activeTab = tab.id"
                :class="[
                  'w-full text-left px-3 py-2 rounded-lg transition-colors',
                  activeTab === tab.id
                    ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                ]"
              >
                <Icon :name="tab.icon" class="w-5 h-5 inline mr-3" />
                {{ tab.name }}
              </button>
            </nav>
          </Card>
        </div>

        <!-- 主内容区域 -->
        <div class="lg:col-span-2">
          <!-- 基本信息 -->
          <Card v-if="activeTab === 'basic'" class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              基本信息
            </h2>

            <!-- 用户统计信息 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              <!-- 积分 -->
              <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-blue-100 text-sm">我的积分</p>
                    <p class="text-2xl font-bold">{{ user?.points || 0 }}</p>
                  </div>
                  <Icon name="star" class="w-8 h-8 text-blue-200" />
                </div>
              </div>

              <!-- 签到状态 -->
              <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-green-100 text-sm">连续签到</p>
                    <p class="text-2xl font-bold">{{ user?.consecutiveSignInDays || 0 }}天</p>
                  </div>
                  <Icon name="calendar" class="w-8 h-8 text-green-200" />
                </div>
              </div>

              <!-- 签到按钮 -->
              <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-purple-100 text-sm">每日签到</p>
                    <button
                      @click="handleDailyCheckin"
                      :disabled="hasCheckedInToday || isCheckingIn"
                      class="text-sm bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:cursor-not-allowed px-3 py-1 rounded-full transition-colors mt-1"
                    >
                      {{ hasCheckedInToday ? '已签到' : '立即签到' }}
                    </button>
                  </div>
                  <Icon name="gift" class="w-8 h-8 text-purple-200" />
                </div>
              </div>
            </div>
            
            <form @submit.prevent="updateProfile" class="space-y-6">
              <!-- 头像上传 -->
              <div class="flex items-center space-x-6">
                <div class="relative">
                  <img
                    :src="user?.avatar || '/default-avatar.png'"
                    :alt="user?.username"
                    class="w-20 h-20 rounded-full object-cover"
                  >
                  <button
                    type="button"
                    @click="triggerAvatarUpload"
                    class="absolute bottom-0 right-0 bg-primary-500 text-white rounded-full p-1 hover:bg-primary-600 transition-colors"
                  >
                    <Icon name="CameraIcon" class="w-4 h-4" />
                  </button>
                  <input
                    ref="avatarInput"
                    type="file"
                    accept="image/*"
                    @change="handleAvatarUpload"
                    class="hidden"
                  >
                </div>
                <div>
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    {{ user?.username }}
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400">
                    点击相机图标更换头像
                  </p>
                </div>
              </div>

              <!-- 用户名 -->
              <Input
                v-model="profileForm.username"
                label="用户名"
                placeholder="请输入用户名"
                :error="profileErrors.username"
                @blur="validateUsername"
              />

              <!-- 邮箱 -->
              <Input
                v-model="profileForm.email"
                type="email"
                label="邮箱地址"
                placeholder="请输入邮箱地址"
                :error="profileErrors.email"
                @blur="validateEmail"
              />

              <!-- 个人简介 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  个人简介
                </label>
                <textarea
                  v-model="profileForm.bio"
                  rows="4"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  placeholder="介绍一下你自己..."
                ></textarea>
              </div>

              <!-- 提交按钮 -->
              <div class="flex justify-end">
                <Button
                  type="submit"
                  :loading="isUpdatingProfile"
                  :disabled="!isProfileFormValid"
                >
                  保存更改
                </Button>
              </div>
            </form>
          </Card>

          <!-- 密码修改 -->
          <Card v-if="activeTab === 'password'" class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              修改密码
            </h2>
            
            <form @submit.prevent="changePassword" class="space-y-6">
              <!-- 当前密码 -->
              <Input
                v-model="passwordForm.currentPassword"
                type="password"
                label="当前密码"
                placeholder="请输入当前密码"
                :error="passwordErrors.currentPassword"
                @blur="validateCurrentPassword"
              />

              <!-- 新密码 -->
              <Input
                v-model="passwordForm.newPassword"
                type="password"
                label="新密码"
                placeholder="请输入新密码"
                :error="passwordErrors.newPassword"
                @blur="validateNewPassword"
              />

              <!-- 确认新密码 -->
              <Input
                v-model="passwordForm.confirmPassword"
                type="password"
                label="确认新密码"
                placeholder="请再次输入新密码"
                :error="passwordErrors.confirmPassword"
                @blur="validateConfirmPassword"
              />

              <!-- 提交按钮 -->
              <div class="flex justify-end">
                <Button
                  type="submit"
                  :loading="isChangingPassword"
                  :disabled="!isPasswordFormValid"
                >
                  修改密码
                </Button>
              </div>
            </form>
          </Card>

          <!-- 账户设置 -->
          <Card v-if="activeTab === 'settings'" class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              账户设置
            </h2>
            
            <div class="space-y-6">
              <!-- 主题设置 -->
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    主题设置
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400">
                    选择你喜欢的界面主题
                  </p>
                </div>
                <select
                  v-model="themePreference"
                  @change="updateTheme"
                  class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  <option value="system">跟随系统</option>
                  <option value="light">浅色主题</option>
                  <option value="dark">深色主题</option>
                </select>
              </div>

              <!-- 邮件通知 -->
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    邮件通知
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400">
                    接收重要更新和通知
                  </p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input
                    v-model="emailNotifications"
                    type="checkbox"
                    class="sr-only peer"
                  >
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                </label>
              </div>

              <!-- 危险区域 -->
              <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 class="text-lg font-medium text-red-600 dark:text-red-400 mb-4">
                  危险区域
                </h3>
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <p class="text-red-700 dark:text-red-300 mb-4">
                    删除账户将永久删除你的所有数据，此操作无法撤销。
                  </p>
                  <Button
                    variant="danger"
                    @click="showDeleteConfirm = true"
                  >
                    删除账户
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>

    <!-- 删除确认模态框 -->
    <Modal
      v-model="showDeleteConfirm"
      title="确认删除账户"
      size="md"
    >
      <div class="space-y-4">
        <p class="text-gray-700 dark:text-gray-300">
          你确定要删除你的账户吗？此操作将永久删除你的所有数据，包括：
        </p>
        <ul class="list-disc list-inside text-gray-600 dark:text-gray-400 space-y-1">
          <li>个人资料和设置</li>
          <li>上传的音乐和歌单</li>
          <li>评论和互动记录</li>
          <li>关注和粉丝关系</li>
        </ul>
        <p class="text-red-600 dark:text-red-400 font-medium">
          此操作无法撤销！
        </p>
      </div>
      
      <template #footer>
        <div class="flex justify-end space-x-3">
          <Button
            variant="outline"
            @click="showDeleteConfirm = false"
          >
            取消
          </Button>
          <Button
            variant="danger"
            @click="deleteAccount"
            :loading="isDeletingAccount"
          >
            确认删除
          </Button>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: 'auth'
})

// 认证和用户信息
const authStore = useAuthStore()
const { user, isLoading } = storeToRefs(authStore)
const { showSuccess, showError } = useNotification()

// API实例
const { updateProfile: apiUpdateProfile, changePassword: apiChangePassword, uploadAvatar: apiUploadAvatar, dailyCheckin, getCheckinStatus } = useAuthApi()

// 标签页
const activeTab = ref('basic')
const tabs = [
  { id: 'basic', name: '基本信息', icon: 'UserIcon' },
  { id: 'password', name: '修改密码', icon: 'LockClosedIcon' },
  { id: 'settings', name: '账户设置', icon: 'CogIcon' }
]

// 个人资料表单
const profileForm = reactive({
  username: user.value?.username || '',
  email: user.value?.email || '',
  bio: user.value?.bio || ''
})

const profileErrors = reactive({
  username: '',
  email: ''
})

const isUpdatingProfile = ref(false)

// 密码修改表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const passwordErrors = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const isChangingPassword = ref(false)

// 设置
const themePreference = ref('system')
const emailNotifications = ref(true)
const showDeleteConfirm = ref(false)
const isDeletingAccount = ref(false)

// 头像上传
const avatarInput = ref<HTMLInputElement>()

// 签到相关
const hasCheckedInToday = ref(false)
const isCheckingIn = ref(false)
const checkinStatus = ref({
  hasCheckedIn: false,
  consecutiveDays: 0,
  totalCheckins: 0,
  nextReward: 0
})

// 表单验证
const validateUsername = () => {
  if (!profileForm.username) {
    profileErrors.username = '请输入用户名'
  } else if (profileForm.username.length < 3) {
    profileErrors.username = '用户名至少需要3个字符'
  } else {
    profileErrors.username = ''
  }
}

const validateEmail = () => {
  if (!profileForm.email) {
    profileErrors.email = '请输入邮箱地址'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(profileForm.email)) {
    profileErrors.email = '请输入有效的邮箱地址'
  } else {
    profileErrors.email = ''
  }
}

const validateCurrentPassword = () => {
  if (!passwordForm.currentPassword) {
    passwordErrors.currentPassword = '请输入当前密码'
  } else {
    passwordErrors.currentPassword = ''
  }
}

const validateNewPassword = () => {
  if (!passwordForm.newPassword) {
    passwordErrors.newPassword = '请输入新密码'
  } else if (passwordForm.newPassword.length < 8) {
    passwordErrors.newPassword = '密码至少需要8个字符'
  } else {
    passwordErrors.newPassword = ''
  }
}

const validateConfirmPassword = () => {
  if (!passwordForm.confirmPassword) {
    passwordErrors.confirmPassword = '请确认新密码'
  } else if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    passwordErrors.confirmPassword = '两次输入的密码不一致'
  } else {
    passwordErrors.confirmPassword = ''
  }
}

// 表单有效性
const isProfileFormValid = computed(() => {
  return profileForm.username && 
         profileForm.email && 
         !profileErrors.username && 
         !profileErrors.email
})

const isPasswordFormValid = computed(() => {
  return passwordForm.currentPassword && 
         passwordForm.newPassword && 
         passwordForm.confirmPassword &&
         !passwordErrors.currentPassword &&
         !passwordErrors.newPassword &&
         !passwordErrors.confirmPassword
})

// 头像上传
const triggerAvatarUpload = () => {
  avatarInput.value?.click()
}

const handleAvatarUpload = async (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  try {
    const response = await apiUploadAvatar(file)
    if (response.success) {
      // 更新用户头像
      await authStore.fetchUser()
      showSuccess('头像上传成功')
    }
  } catch (error: any) {
    showError(error.message || '头像上传失败')
  }
}

// 签到功能
const handleDailyCheckin = async () => {
  if (hasCheckedInToday.value || isCheckingIn.value) return

  isCheckingIn.value = true
  try {
    const response = await dailyCheckin()
    if (response.success) {
      hasCheckedInToday.value = true
      // 更新用户信息以获取最新积分
      await authStore.fetchUser()
      // 更新签到状态
      await fetchCheckinStatus()
      showSuccess(`签到成功！获得 ${response.data.points} 积分`)
    }
  } catch (error: any) {
    showError(error.message || '签到失败')
  } finally {
    isCheckingIn.value = false
  }
}

// 获取签到状态
const fetchCheckinStatus = async () => {
  try {
    const response = await getCheckinStatus()
    if (response.success) {
      checkinStatus.value = response.data
      hasCheckedInToday.value = response.data.hasCheckedIn
    }
  } catch (error) {
    console.warn('获取签到状态失败:', error)
  }
}

// 更新个人资料
const updateProfile = async () => {
  validateUsername()
  validateEmail()

  if (!isProfileFormValid.value) return

  isUpdatingProfile.value = true
  try {
    const response = await apiUpdateProfile({
      username: profileForm.username,
      email: profileForm.email,
      bio: profileForm.bio
    })

    if (response.success) {
      // 更新本地用户信息
      await authStore.fetchUser()
      showSuccess('个人资料更新成功')
    }
  } catch (error: any) {
    showError(error.message || '更新失败')
  } finally {
    isUpdatingProfile.value = false
  }
}

// 修改密码
const changePassword = async () => {
  validateCurrentPassword()
  validateNewPassword()
  validateConfirmPassword()

  if (!isPasswordFormValid.value) return

  isChangingPassword.value = true
  try {
    const response = await apiChangePassword({
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword
    })

    if (response.success) {
      showSuccess('密码修改成功')
      // 清空表单
      Object.assign(passwordForm, {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
    }
  } catch (error: any) {
    showError(error.message || '密码修改失败')
  } finally {
    isChangingPassword.value = false
  }
}

// 主题设置
const updateTheme = async () => {
  try {
    const { updateUserSettings } = useSettingsApi()
    const response = await updateUserSettings({
      appearance: {
        theme: themePreference.value
      }
    })

    if (response.success) {
      showSuccess('主题设置已更新')
    }
  } catch (error: any) {
    showError(error.message || '主题设置更新失败')
  }
}

// 删除账户
const deleteAccount = async () => {
  isDeletingAccount.value = true
  try {
    // TODO: 实现账户删除逻辑
    await logout()
    showSuccess('账户已删除')
  } catch (error: any) {
    showError(error.message || '删除账户失败')
  } finally {
    isDeletingAccount.value = false
    showDeleteConfirm.value = false
  }
}

// 页面初始化
onMounted(async () => {
  // 确保用户信息已加载
  if (!user.value) {
    await authStore.initAuth()
  }

  // 初始化表单数据
  if (user.value) {
    profileForm.username = user.value.username || ''
    profileForm.email = user.value.email || ''
    profileForm.bio = user.value.bio || ''
  }

  // 获取签到状态
  await fetchCheckinStatus()
})

// 监听用户信息变化，更新表单
watch(user, (newUser) => {
  if (newUser) {
    profileForm.username = newUser.username || ''
    profileForm.email = newUser.email || ''
    profileForm.bio = newUser.bio || ''
  }
}, { immediate: true })

// 页面标题
useHead({
  title: '个人资料 - MusicDou'
})
</script>
