@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-white dark:bg-slate-900 text-gray-900 dark:text-gray-100;
    @apply transition-colors duration-200;
    @apply font-sans;
  }

  /* 自定义滚动条 */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-slate-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-slate-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-slate-500;
  }
}

@layer components {
  /* 按钮样式 */
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg;
    @apply transition-colors duration-200 font-medium;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply bg-secondary-500 hover:bg-secondary-600 text-white px-4 py-2 rounded-lg;
    @apply transition-colors duration-200 font-medium;
    @apply focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-outline {
    @apply border border-gray-300 dark:border-slate-600 text-gray-700 dark:text-gray-300;
    @apply hover:bg-gray-50 dark:hover:bg-slate-800 px-4 py-2 rounded-lg;
    @apply transition-colors duration-200 font-medium;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }

  /* 卡片样式 */
  .card {
    @apply bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700;
    @apply transition-colors duration-200;
  }

  .card-hover {
    @apply card hover:shadow-md hover:border-gray-300 dark:hover:border-slate-600;
    @apply transition-all duration-200;
  }

  /* 输入框样式 */
  .input-base {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-slate-600;
    @apply bg-white dark:bg-slate-800 text-gray-900 dark:text-gray-100;
    @apply rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
    @apply transition-colors duration-200;
    @apply placeholder-gray-400 dark:placeholder-gray-500;
  }

  /* 导航样式 */
  .nav-link {
    @apply text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400;
    @apply transition-colors duration-200 font-medium;
  }

  .nav-link-active {
    @apply text-primary-600 dark:text-primary-400 font-semibold;
  }

  /* 音乐播放器样式 */
  .player-control {
    @apply w-10 h-10 flex items-center justify-center rounded-full;
    @apply bg-primary-500 hover:bg-primary-600 text-white;
    @apply transition-colors duration-200 cursor-pointer;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .progress-bar {
    @apply w-full h-2 bg-gray-200 dark:bg-slate-700 rounded-full overflow-hidden;
  }

  .progress-fill {
    @apply h-full bg-primary-500 transition-all duration-100 ease-linear;
  }

  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-500;
  }

  /* 模态框样式 */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
    @apply transition-opacity duration-200;
  }

  .modal-content {
    @apply bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-md w-full mx-4;
    @apply transform transition-all duration-200;
  }
}

@layer utilities {
  /* 文本截断 */
  .text-truncate {
    @apply truncate;
  }

  .text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 玻璃效果 */
  .glass {
    @apply backdrop-blur-sm bg-white/80 dark:bg-slate-900/80;
    @apply border border-white/20 dark:border-slate-700/50;
  }

  /* 渐变背景 */
  .gradient-primary {
    background: linear-gradient(135deg, theme('colors.primary.500'), theme('colors.secondary.500'));
  }

  .gradient-dark {
    background: linear-gradient(135deg, theme('colors.slate.800'), theme('colors.slate.900'));
  }
}

/* 响应式断点工具类 */
@screen sm {
  /* 640px+ */
}

@screen md {
  /* 768px+ */
}

@screen lg {
  /* 1024px+ */
}

@screen xl {
  /* 1280px+ */
}

@screen 2xl {
  /* 1536px+ */
}
