@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100;
    @apply transition-colors duration-200;
    @apply font-sans;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg;
    @apply transition-colors duration-200 font-medium;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-outline {
    @apply border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300;
    @apply hover:bg-gray-50 dark:hover:bg-gray-800 px-4 py-2 rounded-lg;
    @apply transition-colors duration-200 font-medium;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700;
    @apply transition-colors duration-200;
  }

  .card-hover {
    @apply card hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600;
    @apply transition-all duration-200;
  }

  .gradient-primary {
    background: linear-gradient(135deg, theme('colors.primary.500'), theme('colors.secondary.500'));
  }
}
