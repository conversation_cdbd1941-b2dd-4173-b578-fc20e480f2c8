<template>
  <component
    :is="iconComponent"
    :class="iconClass"
    v-bind="$attrs"
  />
</template>

<script setup lang="ts">
import * as HeroIcons from '@heroicons/vue/24/outline'
import * as HeroIconsSolid from '@heroicons/vue/24/solid'

interface Props {
  name: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  solid?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  solid: false
})

// 图标名称映射
const iconNameMap: Record<string, string> = {
  'musical-note': 'MusicalNoteIcon',
  'play': 'PlayIcon',
  'pause': 'PauseIcon',
  'stop': 'StopIcon',
  'forward': 'ForwardIcon',
  'backward': 'BackwardIcon',
  'speaker-wave': 'SpeakerWaveIcon',
  'speaker-x-mark': 'SpeakerXMarkIcon',
  'heart': 'HeartIcon',
  'heart-solid': 'HeartIcon',
  'plus': 'PlusIcon',
  'plus-circle': 'PlusCircleIcon',
  'minus': 'MinusIcon',
  'x-mark': 'XMarkIcon',
  'magnifying-glass': 'MagnifyingGlassIcon',
  'bars-3': 'Bars3Icon',
  'squares-2x2': 'Squares2X2Icon',
  'queue-list': 'QueueListIcon',
  'arrow-path': 'ArrowPathIcon',
  'arrows-right-left': 'ArrowsRightLeftIcon',
  'share': 'ShareIcon',
  'ellipsis-horizontal': 'EllipsisHorizontalIcon',
  'chevron-left': 'ChevronLeftIcon',
  'chevron-right': 'ChevronRightIcon',
  'arrow-left': 'ArrowLeftIcon',
  'users': 'UsersIcon',
  'bookmark': 'BookmarkIcon',
  'trash': 'TrashIcon',
  'computer-desktop': 'ComputerDesktopIcon',
  'device-phone-mobile': 'DevicePhoneMobileIcon',
  'sun': 'SunIcon',
  'moon': 'MoonIcon',
  'chevron-down': 'ChevronDownIcon',
  'arrow-right-on-rectangle': 'ArrowRightOnRectangleIcon',
  'user': 'UserIcon',
  'cog-6-tooth': 'Cog6ToothIcon',
  'bell': 'BellIcon',
  'credit-card': 'CreditCardIcon'
}

// 获取图标组件
const iconComponent = computed(() => {
  // 获取映射后的图标名称
  const mappedName = iconNameMap[props.name] || props.name

  // 首先尝试从solid图标中获取
  if (props.solid || props.name.includes('solid')) {
    const solidIcon = HeroIconsSolid[mappedName as keyof typeof HeroIconsSolid]
    if (solidIcon) return solidIcon
  }

  // 然后从outline图标中获取
  const outlineIcon = HeroIcons[mappedName as keyof typeof HeroIcons]
  if (outlineIcon) return outlineIcon

  // 默认返回MusicalNoteIcon
  return HeroIcons.MusicalNoteIcon
})

// 图标尺寸类
const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-5 h-5',
  lg: 'w-6 h-6',
  xl: 'w-8 h-8'
}

const iconClass = computed(() => sizeClasses[props.size])
</script>
