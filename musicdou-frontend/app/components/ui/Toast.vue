<template>
  <Teleport to="body">
    <div :class="containerClasses">
      <TransitionGroup
        enter-active-class="transition-all duration-300"
        enter-from-class="opacity-0 translate-x-full"
        enter-to-class="opacity-100 translate-x-0"
        leave-active-class="transition-all duration-300"
        leave-from-class="opacity-100 translate-x-0"
        leave-to-class="opacity-0 translate-x-full"
        move-class="transition-transform duration-300"
      >
        <div
          v-for="toast in toasts"
          :key="toast.id"
          :class="toastClasses(toast)"
          @mouseenter="pauseTimer(toast.id)"
          @mouseleave="resumeTimer(toast.id)"
        >
          <!-- Icon -->
          <div class="flex-shrink-0">
            <Icon :name="getIcon(toast.type)" :class="iconClasses(toast.type)" />
          </div>

          <!-- Content -->
          <div class="flex-1 min-w-0">
            <p v-if="toast.title" class="text-sm font-medium text-gray-900 dark:text-gray-100">
              {{ toast.title }}
            </p>
            <p class="text-sm text-gray-600 dark:text-gray-300">
              {{ toast.message }}
            </p>
          </div>

          <!-- Close Button -->
          <button
            v-if="toast.closable !== false"
            type="button"
            class="flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            @click="removeToast(toast.id)"
          >
            <Icon name="XMarkIcon" class="w-5 h-5" />
          </button>

          <!-- Progress Bar -->
          <div
            v-if="toast.duration && toast.duration > 0"
            class="absolute bottom-0 left-0 h-1 bg-current opacity-30 transition-all duration-100"
            :style="{ width: `${getProgress(toast)}%` }"
          />
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue'
interface Toast {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
  closable?: boolean
  startTime?: number
  pausedTime?: number
  remainingTime?: number
}

interface Props {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'
  maxToasts?: number
}

const props = withDefaults(defineProps<Props>(), {
  position: 'top-right',
  maxToasts: 5
})

// Toast状态
const toasts = ref<Toast[]>([])
const timers = new Map<string, NodeJS.Timeout>()

// 添加Toast
const addToast = (toast: Omit<Toast, 'id'>) => {
  const id = Math.random().toString(36).substr(2, 9)
  const newToast: Toast = {
    ...toast,
    id,
    startTime: Date.now(),
    remainingTime: toast.duration || 0
  }

  // 限制最大数量
  if (toasts.value.length >= props.maxToasts) {
    toasts.value.shift()
  }

  toasts.value.push(newToast)

  // 设置自动关闭
  if (newToast.duration && newToast.duration > 0) {
    startTimer(newToast)
  }

  return id
}

// 移除Toast
const removeToast = (id: string) => {
  const index = toasts.value.findIndex(t => t.id === id)
  if (index > -1) {
    clearTimer(id)
    toasts.value.splice(index, 1)
  }
}

// 清空所有Toast
const clearToasts = () => {
  timers.forEach((timer, id) => {
    clearTimeout(timer)
  })
  timers.clear()
  toasts.value = []
}

// 定时器管理
const startTimer = (toast: Toast) => {
  if (!toast.duration || toast.duration <= 0) return

  const timer = setTimeout(() => {
    removeToast(toast.id)
  }, toast.remainingTime || toast.duration)

  timers.set(toast.id, timer)
}

const clearTimer = (id: string) => {
  const timer = timers.get(id)
  if (timer) {
    clearTimeout(timer)
    timers.delete(id)
  }
}

const pauseTimer = (id: string) => {
  const toast = toasts.value.find(t => t.id === id)
  if (!toast || !toast.duration) return

  clearTimer(id)
  
  const elapsed = Date.now() - (toast.startTime || 0) - (toast.pausedTime || 0)
  toast.remainingTime = Math.max(0, toast.duration - elapsed)
}

const resumeTimer = (id: string) => {
  const toast = toasts.value.find(t => t.id === id)
  if (!toast || !toast.remainingTime) return

  toast.pausedTime = (toast.pausedTime || 0) + (Date.now() - (toast.startTime || 0))
  toast.startTime = Date.now()
  
  startTimer(toast)
}

// 获取进度百分比
const getProgress = (toast: Toast) => {
  if (!toast.duration || !toast.startTime) return 100

  const elapsed = Date.now() - toast.startTime - (toast.pausedTime || 0)
  const progress = Math.max(0, Math.min(100, (elapsed / toast.duration) * 100))
  
  return 100 - progress
}

// 获取图标
const getIcon = (type: Toast['type']) => {
  const iconMap = {
    success: 'CheckCircleIcon',
    error: 'XCircleIcon',
    warning: 'ExclamationTriangleIcon',
    info: 'InformationCircleIcon'
  }
  return iconMap[type]
}

// 样式计算
const containerClasses = computed(() => {
  const baseClasses = [
    'fixed',
    'z-50',
    'flex',
    'flex-col',
    'gap-2',
    'p-4',
    'pointer-events-none'
  ]

  const positionClasses = {
    'top-right': ['top-0', 'right-0'],
    'top-left': ['top-0', 'left-0'],
    'bottom-right': ['bottom-0', 'right-0'],
    'bottom-left': ['bottom-0', 'left-0'],
    'top-center': ['top-0', 'left-1/2', '-translate-x-1/2'],
    'bottom-center': ['bottom-0', 'left-1/2', '-translate-x-1/2']
  }

  return [
    ...baseClasses,
    ...positionClasses[props.position]
  ].join(' ')
})

const toastClasses = (toast: Toast) => {
  const baseClasses = [
    'relative',
    'flex',
    'items-start',
    'gap-3',
    'p-4',
    'bg-white',
    'dark:bg-gray-800',
    'border',
    'rounded-lg',
    'shadow-lg',
    'pointer-events-auto',
    'max-w-sm',
    'w-full',
    'overflow-hidden'
  ]

  const typeClasses = {
    success: ['border-green-200', 'dark:border-green-800'],
    error: ['border-red-200', 'dark:border-red-800'],
    warning: ['border-yellow-200', 'dark:border-yellow-800'],
    info: ['border-blue-200', 'dark:border-blue-800']
  }

  return [
    ...baseClasses,
    ...typeClasses[toast.type]
  ].join(' ')
}

const iconClasses = (type: Toast['type']) => {
  const baseClasses = ['w-5', 'h-5']
  
  const colorClasses = {
    success: ['text-green-500'],
    error: ['text-red-500'],
    warning: ['text-yellow-500'],
    info: ['text-blue-500']
  }

  return [
    ...baseClasses,
    ...colorClasses[type]
  ].join(' ')
}

// 暴露方法
defineExpose({
  addToast,
  removeToast,
  clearToasts
})

// 组件卸载时清理定时器
onUnmounted(() => {
  clearToasts()
})
</script>
