<template>
  <button
    :class="buttonClasses"
    :disabled="disabled || loading"
    :type="type"
    @click="handleClick"
  >
    <Icon
      v-if="loading"
      name="ArrowPathIcon"
      :class="iconClasses"
      class="animate-spin"
    />
    <Icon
      v-else-if="icon && iconPosition === 'left'"
      :name="icon"
      :class="iconClasses"
    />
    
    <span v-if="$slots.default" :class="textClasses">
      <slot />
    </span>
    
    <Icon
      v-if="icon && iconPosition === 'right' && !loading"
      :name="icon"
      :class="iconClasses"
    />
  </button>
</template>

<script setup lang="ts">
interface Props {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  disabled?: boolean
  loading?: boolean
  type?: 'button' | 'submit' | 'reset'
  icon?: string
  iconPosition?: 'left' | 'right'
  fullWidth?: boolean
  rounded?: boolean
}

interface Emits {
  (e: 'click', event: MouseEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  disabled: false,
  loading: false,
  type: 'button',
  iconPosition: 'left',
  fullWidth: false,
  rounded: false
})

const emit = defineEmits<Emits>()

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}

// 计算样式类
const buttonClasses = computed(() => {
  const baseClasses = [
    'inline-flex',
    'items-center',
    'justify-center',
    'font-medium',
    'transition-all',
    'duration-200',
    'focus:outline-none',
    'focus:ring-2',
    'focus:ring-offset-2',
    'disabled:opacity-50',
    'disabled:cursor-not-allowed'
  ]

  // 尺寸样式
  const sizeClasses = {
    sm: ['px-3', 'py-1.5', 'text-sm', 'gap-1.5'],
    md: ['px-4', 'py-2', 'text-sm', 'gap-2'],
    lg: ['px-6', 'py-3', 'text-base', 'gap-2'],
    xl: ['px-8', 'py-4', 'text-lg', 'gap-3']
  }

  // 变体样式
  const variantClasses = {
    primary: [
      'bg-primary-500',
      'hover:bg-primary-600',
      'active:bg-primary-700',
      'text-white',
      'border',
      'border-primary-500',
      'focus:ring-primary-500'
    ],
    secondary: [
      'bg-secondary-500',
      'hover:bg-secondary-600',
      'active:bg-secondary-700',
      'text-white',
      'border',
      'border-secondary-500',
      'focus:ring-secondary-500'
    ],
    outline: [
      'bg-transparent',
      'hover:bg-gray-50',
      'dark:hover:bg-gray-800',
      'active:bg-gray-100',
      'dark:active:bg-gray-700',
      'text-gray-700',
      'dark:text-gray-300',
      'border',
      'border-gray-300',
      'dark:border-gray-600',
      'focus:ring-gray-500'
    ],
    ghost: [
      'bg-transparent',
      'hover:bg-gray-100',
      'dark:hover:bg-gray-800',
      'active:bg-gray-200',
      'dark:active:bg-gray-700',
      'text-gray-700',
      'dark:text-gray-300',
      'border-transparent',
      'focus:ring-gray-500'
    ],
    danger: [
      'bg-red-500',
      'hover:bg-red-600',
      'active:bg-red-700',
      'text-white',
      'border',
      'border-red-500',
      'focus:ring-red-500'
    ]
  }

  // 圆角样式
  const roundedClasses = props.rounded ? ['rounded-full'] : ['rounded-lg']

  // 全宽样式
  const widthClasses = props.fullWidth ? ['w-full'] : []

  return [
    ...baseClasses,
    ...sizeClasses[props.size],
    ...variantClasses[props.variant],
    ...roundedClasses,
    ...widthClasses
  ].join(' ')
})

const iconClasses = computed(() => {
  const sizeMap = {
    sm: 'w-4 h-4',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
    xl: 'w-6 h-6'
  }
  return sizeMap[props.size]
})

const textClasses = computed(() => {
  return props.icon ? 'flex-1' : ''
})
</script>
