<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-opacity duration-300"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-opacity duration-300"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="modelValue"
        class="fixed inset-0 z-50 overflow-y-auto"
        @click="handleBackdropClick"
      >
        <!-- Backdrop -->
        <div class="fixed inset-0 bg-black/50 backdrop-blur-sm" />

        <!-- Modal Container -->
        <div class="flex min-h-full items-center justify-center p-4">
          <Transition
            enter-active-class="transition-all duration-300"
            enter-from-class="opacity-0 scale-95 translate-y-4"
            enter-to-class="opacity-100 scale-100 translate-y-0"
            leave-active-class="transition-all duration-300"
            leave-from-class="opacity-100 scale-100 translate-y-0"
            leave-to-class="opacity-0 scale-95 translate-y-4"
          >
            <div
              v-if="modelValue"
              :class="modalClasses"
              @click.stop
            >
              <!-- Header -->
              <div v-if="$slots.header || title || closable" :class="headerClasses">
                <slot name="header">
                  <div class="flex items-center justify-between">
                    <h3 v-if="title" class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {{ title }}
                    </h3>
                    <button
                      v-if="closable"
                      type="button"
                      class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                      @click="close"
                    >
                      <Icon name="XMarkIcon" class="w-6 h-6" />
                    </button>
                  </div>
                </slot>
              </div>

              <!-- Body -->
              <div v-if="$slots.default" :class="bodyClasses">
                <slot />
              </div>

              <!-- Footer -->
              <div v-if="$slots.footer" :class="footerClasses">
                <slot name="footer" />
              </div>
            </div>
          </Transition>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, watch, onMounted, onUnmounted } from 'vue'

interface Props {
  modelValue: boolean
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closable?: boolean
  closeOnBackdrop?: boolean
  closeOnEscape?: boolean
  persistent?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'close'): void
  (e: 'open'): void
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  closable: true,
  closeOnBackdrop: true,
  closeOnEscape: true,
  persistent: false
})

const emit = defineEmits<Emits>()

// 关闭模态框
const close = () => {
  if (!props.persistent) {
    emit('update:modelValue', false)
    emit('close')
  }
}

// 背景点击处理
const handleBackdropClick = () => {
  if (props.closeOnBackdrop) {
    close()
  }
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.closeOnEscape && props.modelValue) {
    close()
  }
}

// 监听键盘事件
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// 监听模态框状态变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    emit('open')
    // 防止背景滚动
    document.body.style.overflow = 'hidden'
  } else {
    // 恢复背景滚动
    document.body.style.overflow = ''
  }
})

// 组件卸载时恢复滚动
onUnmounted(() => {
  document.body.style.overflow = ''
})

// 计算模态框样式
const modalClasses = computed(() => {
  const baseClasses = [
    'relative',
    'bg-white',
    'dark:bg-gray-800',
    'rounded-lg',
    'shadow-xl',
    'border',
    'border-gray-200',
    'dark:border-gray-700',
    'max-h-[90vh]',
    'overflow-hidden',
    'flex',
    'flex-col'
  ]

  // 尺寸样式
  const sizeClasses = {
    sm: ['w-full', 'max-w-md'],
    md: ['w-full', 'max-w-lg'],
    lg: ['w-full', 'max-w-2xl'],
    xl: ['w-full', 'max-w-4xl'],
    full: ['w-full', 'max-w-7xl', 'mx-4', 'h-[90vh]']
  }

  return [
    ...baseClasses,
    ...sizeClasses[props.size]
  ].join(' ')
})

// 计算头部样式
const headerClasses = computed(() => {
  return [
    'px-6',
    'py-4',
    'border-b',
    'border-gray-200',
    'dark:border-gray-700',
    'flex-shrink-0'
  ].join(' ')
})

// 计算主体样式
const bodyClasses = computed(() => {
  return [
    'px-6',
    'py-4',
    'flex-1',
    'overflow-y-auto'
  ].join(' ')
})

// 计算底部样式
const footerClasses = computed(() => {
  return [
    'px-6',
    'py-4',
    'border-t',
    'border-gray-200',
    'dark:border-gray-700',
    'bg-gray-50',
    'dark:bg-gray-900/50',
    'flex-shrink-0'
  ].join(' ')
})
</script>
