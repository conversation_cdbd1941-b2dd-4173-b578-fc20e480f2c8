<template>
  <label class="relative inline-flex items-center cursor-pointer">
    <input
      :checked="modelValue"
      @change="$emit('update:modelValue', $event.target.checked)"
      type="checkbox"
      class="sr-only peer"
      :disabled="disabled"
    >
    <div
      :class="[
        'w-11 h-6 rounded-full peer transition-colors duration-200 ease-in-out',
        'peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800',
        'peer-checked:after:translate-x-full peer-checked:after:border-white',
        'after:content-[\'\'] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all',
        disabled 
          ? 'bg-gray-200 dark:bg-gray-700 cursor-not-allowed' 
          : 'bg-gray-200 peer-checked:bg-primary-600 dark:bg-gray-700 dark:border-gray-600'
      ]"
    ></div>
  </label>
</template>

<script setup lang="ts">
interface Props {
  modelValue: boolean
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>
