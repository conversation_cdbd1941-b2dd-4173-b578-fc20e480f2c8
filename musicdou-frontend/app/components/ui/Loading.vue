<template>
  <div :class="containerClasses">
    <!-- Spinner Loading -->
    <div v-if="type === 'spinner'" :class="spinnerClasses">
      <svg
        class="animate-spin"
        :class="sizeClasses"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        />
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    </div>

    <!-- Dots Loading -->
    <div v-else-if="type === 'dots'" :class="dotsClasses">
      <div
        v-for="i in 3"
        :key="i"
        :class="dotClasses"
        :style="{ animationDelay: `${(i - 1) * 0.2}s` }"
      />
    </div>

    <!-- Bars Loading -->
    <div v-else-if="type === 'bars'" :class="barsClasses">
      <div
        v-for="i in 4"
        :key="i"
        :class="barClasses"
        :style="{ animationDelay: `${(i - 1) * 0.1}s` }"
      />
    </div>

    <!-- Pulse Loading -->
    <div v-else-if="type === 'pulse'" :class="pulseClasses">
      <div :class="pulseCircleClasses" />
    </div>

    <!-- Loading Text -->
    <p v-if="text" :class="textClasses">
      {{ text }}
    </p>
  </div>
</template>

<script setup lang="ts">
interface Props {
  type?: 'spinner' | 'dots' | 'bars' | 'pulse'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'primary' | 'secondary' | 'gray' | 'white'
  text?: string
  overlay?: boolean
  fullscreen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'spinner',
  size: 'md',
  color: 'primary',
  overlay: false,
  fullscreen: false
})

// 计算容器样式
const containerClasses = computed(() => {
  const baseClasses = [
    'flex',
    'flex-col',
    'items-center',
    'justify-center',
    'gap-3'
  ]

  if (props.overlay) {
    baseClasses.push(
      'absolute',
      'inset-0',
      'bg-white/80',
      'dark:bg-gray-900/80',
      'backdrop-blur-sm',
      'z-50'
    )
  }

  if (props.fullscreen) {
    baseClasses.push(
      'fixed',
      'inset-0',
      'bg-white/90',
      'dark:bg-gray-900/90',
      'backdrop-blur-sm',
      'z-50'
    )
  }

  return baseClasses.join(' ')
})

// 计算尺寸样式
const sizeClasses = computed(() => {
  const sizeMap = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }
  return sizeMap[props.size]
})

// 计算颜色样式
const colorClasses = computed(() => {
  const colorMap = {
    primary: 'text-primary-500',
    secondary: 'text-secondary-500',
    gray: 'text-gray-500',
    white: 'text-white'
  }
  return colorMap[props.color]
})

// Spinner样式
const spinnerClasses = computed(() => {
  return [colorClasses.value].join(' ')
})

// Dots样式
const dotsClasses = computed(() => {
  return 'flex space-x-1'
})

const dotClasses = computed(() => {
  const baseClasses = [
    'rounded-full',
    'animate-bounce',
    colorClasses.value.replace('text-', 'bg-')
  ]

  const sizeMap = {
    sm: 'w-1 h-1',
    md: 'w-2 h-2',
    lg: 'w-3 h-3',
    xl: 'w-4 h-4'
  }

  return [
    ...baseClasses,
    sizeMap[props.size]
  ].join(' ')
})

// Bars样式
const barsClasses = computed(() => {
  return 'flex items-end space-x-1'
})

const barClasses = computed(() => {
  const baseClasses = [
    'animate-pulse',
    colorClasses.value.replace('text-', 'bg-')
  ]

  const sizeMap = {
    sm: 'w-1 h-4',
    md: 'w-1 h-6',
    lg: 'w-2 h-8',
    xl: 'w-2 h-12'
  }

  return [
    ...baseClasses,
    sizeMap[props.size]
  ].join(' ')
})

// Pulse样式
const pulseClasses = computed(() => {
  return 'relative'
})

const pulseCircleClasses = computed(() => {
  const baseClasses = [
    'rounded-full',
    'animate-ping',
    colorClasses.value.replace('text-', 'bg-')
  ]

  return [
    ...baseClasses,
    sizeClasses.value
  ].join(' ')
})

// 文本样式
const textClasses = computed(() => {
  const baseClasses = [
    'text-sm',
    'font-medium',
    'text-gray-600',
    'dark:text-gray-400'
  ]

  return baseClasses.join(' ')
})
</script>

<style scoped>
@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.animate-bounce {
  animation: bounce 1.4s infinite ease-in-out both;
}

@keyframes pulse-bar {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

.animate-pulse {
  animation: pulse-bar 1.2s infinite ease-in-out;
}
</style>
