<template>
  <div :class="cardClasses" @click="handleClick">
    <!-- Header -->
    <div v-if="$slots.header || title" :class="headerClasses">
      <slot name="header">
        <div class="flex items-center justify-between">
          <h3 v-if="title" class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {{ title }}
          </h3>
          <div v-if="$slots.actions" class="flex items-center space-x-2">
            <slot name="actions" />
          </div>
        </div>
      </slot>
    </div>

    <!-- Body -->
    <div v-if="$slots.default" :class="bodyClasses">
      <slot />
    </div>

    <!-- Footer -->
    <div v-if="$slots.footer" :class="footerClasses">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title?: string
  variant?: 'default' | 'outlined' | 'elevated' | 'filled'
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full'
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  hover?: boolean
  clickable?: boolean
  bordered?: boolean
}

interface Emits {
  (e: 'click', event: MouseEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  padding: 'md',
  rounded: 'lg',
  shadow: 'sm',
  hover: false,
  clickable: false,
  bordered: true
})

const emit = defineEmits<Emits>()

const handleClick = (event: MouseEvent) => {
  if (props.clickable) {
    emit('click', event)
  }
}

// 计算卡片样式
const cardClasses = computed(() => {
  const baseClasses = [
    'transition-all',
    'duration-200'
  ]

  // 变体样式
  const variantClasses = {
    default: [
      'bg-white',
      'dark:bg-gray-800'
    ],
    outlined: [
      'bg-transparent',
      'border-2',
      'border-gray-200',
      'dark:border-gray-700'
    ],
    elevated: [
      'bg-white',
      'dark:bg-gray-800',
      'shadow-lg',
      'border-0'
    ],
    filled: [
      'bg-gray-50',
      'dark:bg-gray-900',
      'border-0'
    ]
  }

  // 圆角样式
  const roundedClasses = {
    none: [],
    sm: ['rounded-sm'],
    md: ['rounded-md'],
    lg: ['rounded-lg'],
    xl: ['rounded-xl'],
    full: ['rounded-full']
  }

  // 阴影样式
  const shadowClasses = {
    none: [],
    sm: ['shadow-sm'],
    md: ['shadow-md'],
    lg: ['shadow-lg'],
    xl: ['shadow-xl']
  }

  // 边框样式
  const borderClasses = props.bordered && props.variant === 'default'
    ? ['border', 'border-gray-200', 'dark:border-gray-700']
    : []

  // 悬停效果
  const hoverClasses = props.hover
    ? [
        'hover:shadow-md',
        'hover:-translate-y-1',
        'hover:scale-[1.02]'
      ]
    : []

  // 可点击样式
  const clickableClasses = props.clickable
    ? [
        'cursor-pointer',
        'hover:shadow-lg',
        'active:scale-[0.98]'
      ]
    : []

  return [
    ...baseClasses,
    ...variantClasses[props.variant],
    ...roundedClasses[props.rounded],
    ...shadowClasses[props.shadow],
    ...borderClasses,
    ...hoverClasses,
    ...clickableClasses
  ].join(' ')
})

// 计算头部样式
const headerClasses = computed(() => {
  const paddingClasses = {
    none: [],
    sm: ['p-3'],
    md: ['p-4'],
    lg: ['p-6'],
    xl: ['p-8']
  }

  const borderClasses = [
    'border-b',
    'border-gray-200',
    'dark:border-gray-700'
  ]

  return [
    ...paddingClasses[props.padding],
    ...borderClasses
  ].join(' ')
})

// 计算主体样式
const bodyClasses = computed(() => {
  const paddingClasses = {
    none: [],
    sm: ['p-3'],
    md: ['p-4'],
    lg: ['p-6'],
    xl: ['p-8']
  }

  return paddingClasses[props.padding].join(' ')
})

// 计算底部样式
const footerClasses = computed(() => {
  const paddingClasses = {
    none: [],
    sm: ['p-3'],
    md: ['p-4'],
    lg: ['p-6'],
    xl: ['p-8']
  }

  const borderClasses = [
    'border-t',
    'border-gray-200',
    'dark:border-gray-700',
    'bg-gray-50',
    'dark:bg-gray-900/50'
  ]

  return [
    ...paddingClasses[props.padding],
    ...borderClasses
  ].join(' ')
})
</script>
