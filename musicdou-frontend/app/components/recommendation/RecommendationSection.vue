<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
    <!-- 标题和操作 -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center space-x-3">
        <Icon v-if="icon" :name="icon" class="w-6 h-6 text-primary-600 dark:text-primary-400" />
        <div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
            {{ title }}
          </h2>
          <p v-if="description" class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {{ description }}
          </p>
        </div>
      </div>
      
      <div class="flex items-center space-x-2">
        <!-- 刷新按钮 -->
        <button
          v-if="showRefresh"
          @click="handleRefresh"
          :disabled="isLoading"
          class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 
                 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 
                 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          title="刷新推荐"
        >
          <Icon 
            name="ArrowPathIcon" 
            :class="['w-5 h-5', { 'animate-spin': isLoading }]" 
          />
        </button>
        
        <!-- 查看全部按钮 -->
        <button
          v-if="showViewAll && items.length > 0"
          @click="$emit('view-all')"
          class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 
                 font-medium transition-colors duration-200"
        >
          查看全部
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading && items.length === 0" class="flex justify-center py-12">
      <Loading size="lg" />
    </div>

    <!-- 推荐内容 -->
    <div v-else-if="items.length > 0">
      <!-- 网格布局 -->
      <div v-if="layout === 'grid'" 
           :class="[
             'grid gap-4',
             gridCols === 2 ? 'grid-cols-2' :
             gridCols === 3 ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' :
             gridCols === 4 ? 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-4' :
             gridCols === 5 ? 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-5' :
             'grid-cols-1 sm:grid-cols-2 lg:grid-cols-6'
           ]">
        <div
          v-for="(item, index) in displayItems"
          :key="getItemKey(item, index)"
          class="group cursor-pointer"
          @click="handleItemClick(item)"
        >
          <MusicCard
            v-if="type === 'music'"
            :music="item as Music"
            :show-actions="showActions"
            @play="$emit('play', item)"
            @like="$emit('like', item)"
            @add-to-playlist="$emit('add-to-playlist', item)"
          />
          <PlaylistCard
            v-else-if="type === 'playlist'"
            :playlist="item as Playlist"
            :show-actions="showActions"
            @play="$emit('play', item)"
            @like="$emit('like', item)"
          />
          <UserCard
            v-else
            :user="item as User"
            :show-follow-button="showActions"
            @follow="$emit('follow', item)"
          />
        </div>
      </div>

      <!-- 水平滚动布局 -->
      <div v-else-if="layout === 'horizontal'" class="relative">
        <div 
          ref="scrollContainer"
          class="flex space-x-4 overflow-x-auto scrollbar-hide pb-2"
          @scroll="handleScroll"
        >
          <div
            v-for="(item, index) in displayItems"
            :key="getItemKey(item, index)"
            class="flex-shrink-0 w-48 group cursor-pointer"
            @click="handleItemClick(item)"
          >
            <MusicCard
              v-if="type === 'music'"
              :music="item as Music"
              :show-actions="showActions"
              @play="$emit('play', item)"
              @like="$emit('like', item)"
              @add-to-playlist="$emit('add-to-playlist', item)"
            />
            <PlaylistCard
              v-else-if="type === 'playlist'"
              :playlist="item as Playlist"
              :show-actions="showActions"
              @play="$emit('play', item)"
              @like="$emit('like', item)"
            />
            <UserCard
              v-else
              :user="item as User"
              :show-follow-button="showActions"
              @follow="$emit('follow', item)"
            />
          </div>
        </div>
        
        <!-- 滚动按钮 -->
        <button
          v-if="canScrollLeft"
          @click="scrollLeft"
          class="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-2 z-10
                 w-10 h-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700
                 rounded-full shadow-lg flex items-center justify-center
                 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100
                 transition-all duration-200 hover:scale-105"
        >
          <Icon name="ChevronLeftIcon" class="w-5 h-5" />
        </button>
        
        <button
          v-if="canScrollRight"
          @click="scrollRight"
          class="absolute right-0 top-1/2 -translate-y-1/2 translate-x-2 z-10
                 w-10 h-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700
                 rounded-full shadow-lg flex items-center justify-center
                 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100
                 transition-all duration-200 hover:scale-105"
        >
          <Icon name="ChevronRightIcon" class="w-5 h-5" />
        </button>
      </div>

      <!-- 列表布局 -->
      <div v-else class="space-y-3">
        <div
          v-for="(item, index) in displayItems"
          :key="getItemKey(item, index)"
          class="group cursor-pointer"
          @click="handleItemClick(item)"
        >
          <MusicCard
            v-if="type === 'music'"
            :music="item as Music"
            :layout="'list'"
            :show-actions="showActions"
            @play="$emit('play', item)"
            @like="$emit('like', item)"
            @add-to-playlist="$emit('add-to-playlist', item)"
          />
          <PlaylistCard
            v-else-if="type === 'playlist'"
            :playlist="item as Playlist"
            :layout="'list'"
            :show-actions="showActions"
            @play="$emit('play', item)"
            @like="$emit('like', item)"
          />
          <UserCard
            v-else
            :user="item as User"
            :layout="'list'"
            :show-follow-button="showActions"
            @follow="$emit('follow', item)"
          />
        </div>
      </div>

      <!-- 加载更多 -->
      <div v-if="hasMore && !isLoading" class="flex justify-center mt-6">
        <Button
          @click="$emit('load-more')"
          variant="outline"
          size="sm"
        >
          <Icon name="PlusIcon" class="w-4 h-4 mr-2" />
          加载更多
        </Button>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="text-center py-12">
      <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
        <Icon :name="emptyIcon" class="w-8 h-8 text-gray-400" />
      </div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        {{ emptyTitle }}
      </h3>
      <p class="text-gray-500 dark:text-gray-400 mb-4">
        {{ emptyDescription }}
      </p>
      <Button
        v-if="showEmptyAction"
        @click="$emit('empty-action')"
        variant="outline"
        size="sm"
      >
        {{ emptyActionText }}
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Music, Playlist, User } from '~/types'

interface Props {
  title: string
  description?: string
  icon?: string
  items: (Music | Playlist | User)[]
  type: 'music' | 'playlist' | 'user'
  layout?: 'grid' | 'horizontal' | 'list'
  gridCols?: 2 | 3 | 4 | 5 | 6
  maxItems?: number
  isLoading?: boolean
  hasMore?: boolean
  showRefresh?: boolean
  showViewAll?: boolean
  showActions?: boolean
  showEmptyAction?: boolean
  emptyTitle?: string
  emptyDescription?: string
  emptyActionText?: string
  emptyIcon?: string
}

interface Emits {
  (e: 'refresh'): void
  (e: 'view-all'): void
  (e: 'load-more'): void
  (e: 'item-click', item: Music | Playlist | User): void
  (e: 'play', item: Music | Playlist): void
  (e: 'like', item: Music | Playlist): void
  (e: 'add-to-playlist', item: Music): void
  (e: 'follow', item: User): void
  (e: 'empty-action'): void
}

const props = withDefaults(defineProps<Props>(), {
  layout: 'grid',
  gridCols: 4,
  maxItems: 12,
  showRefresh: true,
  showViewAll: true,
  showActions: true,
  showEmptyAction: false,
  emptyTitle: '暂无推荐',
  emptyDescription: '暂时没有相关推荐内容',
  emptyActionText: '刷新推荐',
  emptyIcon: 'MusicalNoteIcon'
})

const emit = defineEmits<Emits>()

// 响应式数据
const scrollContainer = ref<HTMLElement>()
const canScrollLeft = ref(false)
const canScrollRight = ref(false)

// 计算属性
const displayItems = computed(() => {
  return props.maxItems ? props.items.slice(0, props.maxItems) : props.items
})

// 方法
const getItemKey = (item: any, index: number) => {
  return item.id || `item-${index}`
}

const handleItemClick = (item: Music | Playlist | User) => {
  emit('item-click', item)
}

const handleRefresh = () => {
  emit('refresh')
}

const handleScroll = () => {
  if (!scrollContainer.value) return
  
  const { scrollLeft, scrollWidth, clientWidth } = scrollContainer.value
  canScrollLeft.value = scrollLeft > 0
  canScrollRight.value = scrollLeft < scrollWidth - clientWidth - 10
}

const scrollLeft = () => {
  if (!scrollContainer.value) return
  scrollContainer.value.scrollBy({ left: -200, behavior: 'smooth' })
}

const scrollRight = () => {
  if (!scrollContainer.value) return
  scrollContainer.value.scrollBy({ left: 200, behavior: 'smooth' })
}

// 监听滚动容器变化
watch(() => props.items, () => {
  nextTick(() => {
    handleScroll()
  })
}, { deep: true })

// 组件挂载时检查滚动状态
onMounted(() => {
  nextTick(() => {
    handleScroll()
  })
})
</script>

<style scoped>
/* 隐藏滚动条 */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* 平滑滚动 */
.overflow-x-auto {
  scroll-behavior: smooth;
}

/* 卡片悬停效果 */
.group:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease-in-out;
}

/* 滚动按钮阴影 */
.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dark .shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* 响应式网格调整 */
@media (max-width: 640px) {
  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

@media (max-width: 768px) {
  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .sm\:grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
</style>
