<template>
  <Modal
    :show="true"
    @close="$emit('close')"
    title="推荐设置"
    size="lg"
  >
    <div class="space-y-6">
      <!-- 个性化推荐 -->
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
            个性化推荐
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            基于你的听歌历史和偏好提供推荐
          </p>
        </div>
        <Toggle
          v-model="localSettings.enablePersonalization"
          @update:model-value="updateSetting('enablePersonalization', $event)"
        />
      </div>

      <!-- 显示成人内容 -->
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
            显示成人内容
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            在推荐中包含标记为成人内容的音乐
          </p>
        </div>
        <Toggle
          v-model="localSettings.includeExplicit"
          @update:model-value="updateSetting('includeExplicit', $event)"
        />
      </div>

      <!-- 探索程度 -->
      <div>
        <label class="block text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
          探索程度
        </label>
        <div class="space-y-3">
          <label
            v-for="level in discoveryLevels"
            :key="level.value"
            class="flex items-center space-x-3 cursor-pointer"
          >
            <input
              type="radio"
              :value="level.value"
              v-model="localSettings.discoveryLevel"
              @change="updateSetting('discoveryLevel', level.value)"
              class="w-4 h-4 text-primary-600 border-gray-300 dark:border-gray-600 
                     focus:ring-primary-500 dark:focus:ring-primary-400"
            />
            <div>
              <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ level.label }}
              </div>
              <div class="text-xs text-gray-600 dark:text-gray-400">
                {{ level.description }}
              </div>
            </div>
          </label>
        </div>
      </div>

      <!-- 刷新频率 -->
      <div>
        <label class="block text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
          推荐刷新频率
        </label>
        <select
          v-model="localSettings.refreshFrequency"
          @change="updateSetting('refreshFrequency', $event.target.value)"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
                 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        >
          <option value="hourly">每小时</option>
          <option value="daily">每天</option>
          <option value="weekly">每周</option>
        </select>
      </div>

      <!-- 偏好风格 -->
      <div>
        <label class="block text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
          偏好风格
        </label>
        <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
          <button
            v-for="genre in availableGenres"
            :key="genre"
            @click="togglePreferredGenre(genre)"
            :class="[
              'px-3 py-2 text-sm rounded-md border transition-all duration-200',
              localSettings.preferredGenres.includes(genre)
                ? 'bg-primary-50 dark:bg-primary-900 text-primary-700 dark:text-primary-300 border-primary-200 dark:border-primary-700'
                : 'bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600'
            ]"
          >
            {{ genre }}
          </button>
        </div>
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
          选择你喜欢的音乐风格，最多选择5个
        </p>
      </div>

      <!-- 排除风格 -->
      <div>
        <label class="block text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
          排除风格
        </label>
        <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
          <button
            v-for="genre in availableGenres"
            :key="genre"
            @click="toggleExcludedGenre(genre)"
            :class="[
              'px-3 py-2 text-sm rounded-md border transition-all duration-200',
              localSettings.excludedGenres.includes(genre)
                ? 'bg-red-50 dark:bg-red-900 text-red-700 dark:text-red-300 border-red-200 dark:border-red-700'
                : 'bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600'
            ]"
          >
            {{ genre }}
          </button>
        </div>
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
          选择你不喜欢的音乐风格
        </p>
      </div>

      <!-- 重置按钮 -->
      <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          @click="resetToDefaults"
          class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300
                 transition-colors duration-200"
        >
          重置为默认设置
        </button>
      </div>
    </div>

    <template #footer>
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          设置将自动保存
        </div>
        <div class="flex space-x-3">
          <Button
            @click="$emit('close')"
            variant="outline"
          >
            取消
          </Button>
          <Button
            @click="saveSettings"
            :loading="isSaving"
          >
            保存设置
          </Button>
        </div>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import type { RecommendationSettings } from '~/types'

interface Emits {
  (e: 'close'): void
  (e: 'settings-updated'): void
}

const emit = defineEmits<Emits>()

// 状态管理
const recommendationStore = useRecommendationStore()

// 响应式数据
const isSaving = ref(false)
const localSettings = ref<RecommendationSettings>({ ...recommendationStore.settings })

// 可选项数据
const discoveryLevels = [
  {
    value: 'conservative',
    label: '保守',
    description: '主要推荐与你已知喜好相似的内容'
  },
  {
    value: 'balanced',
    label: '平衡',
    description: '在熟悉和新颖内容之间保持平衡'
  },
  {
    value: 'adventurous',
    label: '冒险',
    description: '更多推荐新颖和不同类型的内容'
  }
]

const availableGenres = [
  '流行', '摇滚', '民谣', '电子', '古典', '爵士',
  '说唱', '乡村', '蓝调', '雷鬼', '朋克', '金属',
  '新世纪', '世界音乐', '实验', '环境音乐'
]

// 方法
const updateSetting = async (key: keyof RecommendationSettings, value: any) => {
  localSettings.value[key] = value
  
  // 实时保存设置
  try {
    await recommendationStore.updateSettings({ [key]: value })
  } catch (error) {
    console.error('更新设置失败:', error)
  }
}

const togglePreferredGenre = (genre: string) => {
  const genres = [...localSettings.value.preferredGenres]
  const index = genres.indexOf(genre)
  
  if (index > -1) {
    genres.splice(index, 1)
  } else {
    if (genres.length < 5) {
      genres.push(genre)
    } else {
      // 提示最多选择5个
      return
    }
  }
  
  localSettings.value.preferredGenres = genres
  updateSetting('preferredGenres', genres)
}

const toggleExcludedGenre = (genre: string) => {
  const genres = [...localSettings.value.excludedGenres]
  const index = genres.indexOf(genre)
  
  if (index > -1) {
    genres.splice(index, 1)
  } else {
    genres.push(genre)
  }
  
  localSettings.value.excludedGenres = genres
  updateSetting('excludedGenres', genres)
}

const resetToDefaults = async () => {
  const defaultSettings: RecommendationSettings = {
    enablePersonalization: true,
    includeExplicit: false,
    preferredGenres: [],
    excludedGenres: [],
    discoveryLevel: 'balanced',
    refreshFrequency: 'daily'
  }
  
  localSettings.value = { ...defaultSettings }
  
  try {
    await recommendationStore.updateSettings(defaultSettings)
  } catch (error) {
    console.error('重置设置失败:', error)
  }
}

const saveSettings = async () => {
  isSaving.value = true
  
  try {
    await recommendationStore.updateSettings(localSettings.value)
    emit('settings-updated')
    emit('close')
  } catch (error) {
    console.error('保存设置失败:', error)
  } finally {
    isSaving.value = false
  }
}

// 监听store设置变化
watch(() => recommendationStore.settings, (newSettings) => {
  localSettings.value = { ...newSettings }
}, { deep: true })
</script>

<style scoped>
/* 单选按钮样式 */
input[type="radio"]:checked {
  background-color: rgb(37 99 235);
  border-color: rgb(37 99 235);
}

.dark input[type="radio"]:checked {
  background-color: rgb(96 165 250);
  border-color: rgb(96 165 250);
}

/* 按钮悬停效果 */
button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}

/* 网格响应式调整 */
@media (max-width: 640px) {
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 768px) {
  .sm\:grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* 过渡动画 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
