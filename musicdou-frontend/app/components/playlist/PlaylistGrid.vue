<template>
  <div>
    <!-- 加载状态 -->
    <div v-if="loading" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <div
        v-for="i in skeletonCount"
        :key="i"
        class="animate-pulse"
      >
        <div class="bg-gray-300 dark:bg-gray-600 aspect-square rounded-lg mb-3"></div>
        <div class="space-y-2">
          <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
          <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
          <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded w-2/3"></div>
        </div>
      </div>
    </div>

    <!-- 歌单网格 -->
    <div
      v-else-if="playlists.length > 0"
      class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
    >
      <PlaylistCard
        v-for="playlist in playlists"
        :key="playlist.id"
        :playlist="playlist"
        :show-actions="showActions"
        @play="$emit('play', $event)"
        @edit="$emit('edit', $event)"
        @delete="$emit('delete', $event)"
        @like="$emit('like', $event)"
      />
    </div>

    <!-- 空状态 -->
    <div v-else class="text-center py-12">
      <div class="max-w-sm mx-auto">
        <UiIcon 
          :name="emptyIcon" 
          class="w-16 h-16 text-gray-400 mx-auto mb-4" 
        />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {{ emptyTitle }}
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          {{ emptyDescription }}
        </p>
        <slot name="empty-action">
          <UiButton
            v-if="showCreateButton"
            @click="$emit('create')"
            variant="primary"
          >
            <UiIcon name="plus" class="w-4 h-4" />
            创建歌单
          </UiButton>
        </slot>
      </div>
    </div>

    <!-- 加载更多 -->
    <div v-if="showLoadMore" class="flex justify-center mt-8">
      <UiButton
        @click="$emit('load-more')"
        variant="outline"
        :loading="loadingMore"
        :disabled="loadingMore"
      >
        {{ loadingMore ? '加载中...' : '加载更多' }}
      </UiButton>
    </div>

    <!-- 分页 -->
    <div v-if="showPagination && totalPages > 1" class="flex justify-center mt-8">
      <nav class="flex items-center gap-2">
        <!-- 上一页 -->
        <button
          @click="$emit('page-change', currentPage - 1)"
          :disabled="currentPage === 1"
          class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300 transition-colors"
        >
          <UiIcon name="chevron-left" class="w-4 h-4" />
        </button>

        <!-- 页码 -->
        <div class="flex items-center gap-1">
          <template v-for="page in visiblePages" :key="page">
            <button
              v-if="page !== '...'"
              @click="$emit('page-change', page)"
              :class="[
                'px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                page === currentPage
                  ? 'bg-primary-500 text-white'
                  : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'
              ]"
            >
              {{ page }}
            </button>
            <span v-else class="px-2 py-2 text-sm text-gray-500 dark:text-gray-400">
              ...
            </span>
          </template>
        </div>

        <!-- 下一页 -->
        <button
          @click="$emit('page-change', currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300 transition-colors"
        >
          <UiIcon name="chevron-right" class="w-4 h-4" />
        </button>
      </nav>

      <!-- 页面信息 -->
      <div class="mt-4 text-center">
        <span class="text-sm text-gray-700 dark:text-gray-300">
          第 {{ currentPage }} 页，共 {{ totalPages }} 页
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Playlist } from '~/types'

interface Props {
  playlists: Playlist[]
  loading?: boolean
  loadingMore?: boolean
  showActions?: boolean
  showLoadMore?: boolean
  showPagination?: boolean
  showCreateButton?: boolean
  currentPage?: number
  totalPages?: number
  skeletonCount?: number
  emptyTitle?: string
  emptyDescription?: string
  emptyIcon?: string
}

interface Emits {
  (e: 'play', playlist: Playlist): void
  (e: 'edit', playlist: Playlist): void
  (e: 'delete', playlist: Playlist): void
  (e: 'like', playlist: Playlist): void
  (e: 'create'): void
  (e: 'load-more'): void
  (e: 'page-change', page: number): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  loadingMore: false,
  showActions: true,
  showLoadMore: false,
  showPagination: false,
  showCreateButton: true,
  currentPage: 1,
  totalPages: 1,
  skeletonCount: 8,
  emptyTitle: '还没有歌单',
  emptyDescription: '创建您的第一个歌单，开始收集喜爱的音乐',
  emptyIcon: 'musical-note'
})

const emit = defineEmits<Emits>()

// 计算可见的页码
const visiblePages = computed(() => {
  const pages: (number | string)[] = []
  const { currentPage, totalPages } = props

  if (totalPages <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i)
    }
  } else {
    // 总是显示第一页
    pages.push(1)

    if (currentPage <= 4) {
      // 当前页在前面
      for (let i = 2; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(totalPages)
    } else if (currentPage >= totalPages - 3) {
      // 当前页在后面
      pages.push('...')
      for (let i = totalPages - 4; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // 当前页在中间
      pages.push('...')
      for (let i = currentPage - 1; i <= currentPage + 1; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(totalPages)
    }
  }

  return pages
})
</script>
