<template>
  <div class="group relative bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
    <!-- 封面图片 -->
    <div class="relative aspect-square overflow-hidden">
      <img
        :src="playlist.coverUrl || '/default-playlist-cover.jpg'"
        :alt="playlist.name"
        class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
      />
      
      <!-- 悬停时显示的播放按钮 -->
      <div class="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
        <button
          @click.stop="$emit('play', playlist)"
          class="bg-primary-500 hover:bg-primary-600 text-white rounded-full p-3 transform scale-90 group-hover:scale-100 transition-transform duration-200"
        >
          <UiIcon name="play" class="w-6 h-6" />
        </button>
      </div>

      <!-- 歌曲数量标签 -->
      <div class="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
        {{ playlist.songCount }} 首
      </div>

      <!-- 公开/私有标识 -->
      <div v-if="!playlist.isPublic" class="absolute top-2 left-2 bg-gray-800 bg-opacity-80 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
        <UiIcon name="lock-closed" class="w-3 h-3" />
        私有
      </div>
    </div>

    <!-- 歌单信息 -->
    <div class="p-4">
      <div class="flex items-start justify-between mb-2">
        <h3 
          class="font-semibold text-gray-900 dark:text-white truncate cursor-pointer hover:text-primary-500 transition-colors"
          @click="navigateToDetail"
        >
          {{ playlist.name }}
        </h3>
        
        <!-- 操作菜单 -->
        <div class="relative" v-if="showActions">
          <button
            @click.stop="showMenu = !showMenu"
            class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <UiIcon name="ellipsis-vertical" class="w-4 h-4" />
          </button>

          <!-- 下拉菜单 -->
          <div
            v-if="showMenu"
            v-click-outside="() => showMenu = false"
            class="absolute right-0 top-full mt-1 w-32 bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 py-1 z-10"
          >
            <button
              @click.stop="handleEdit"
              class="w-full px-3 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center gap-2"
            >
              <UiIcon name="pencil" class="w-4 h-4" />
              编辑
            </button>
            <button
              @click.stop="handleDelete"
              class="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center gap-2"
            >
              <UiIcon name="trash" class="w-4 h-4" />
              删除
            </button>
          </div>
        </div>
      </div>

      <!-- 描述 -->
      <p v-if="playlist.description" class="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
        {{ playlist.description }}
      </p>

      <!-- 创建者信息 -->
      <div v-if="playlist.user" class="flex items-center gap-2 mb-3">
        <img
          :src="playlist.user.avatar || '/default-avatar.jpg'"
          :alt="playlist.user.username"
          class="w-5 h-5 rounded-full"
        />
        <span class="text-xs text-gray-500 dark:text-gray-400 truncate">
          {{ playlist.user.username }}
        </span>
      </div>

      <!-- 统计信息 -->
      <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
        <div class="flex items-center gap-3">
          <span class="flex items-center gap-1">
            <UiIcon name="play" class="w-3 h-3" />
            {{ formatNumber(playlist.playCount) }}
          </span>
          <span class="flex items-center gap-1">
            <UiIcon name="heart" class="w-3 h-3" />
            {{ formatNumber(playlist.likeCount) }}
          </span>
        </div>
        <span>{{ formatDate(playlist.updatedAt) }}</span>
      </div>

      <!-- 操作按钮 -->
      <div class="flex items-center gap-2 mt-3">
        <UiButton
          @click.stop="$emit('play', playlist)"
          variant="primary"
          size="sm"
          class="flex-1"
        >
          <UiIcon name="play" class="w-4 h-4" />
          播放
        </UiButton>
        
        <UiButton
          @click.stop="handleLike"
          :variant="playlist.isLiked ? 'primary' : 'outline'"
          size="sm"
        >
          <UiIcon name="heart" class="w-4 h-4" />
        </UiButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Playlist } from '~/types'

interface Props {
  playlist: Playlist
  showActions?: boolean
}

interface Emits {
  (e: 'play', playlist: Playlist): void
  (e: 'edit', playlist: Playlist): void
  (e: 'delete', playlist: Playlist): void
  (e: 'like', playlist: Playlist): void
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true
})

const emit = defineEmits<Emits>()

// 响应式数据
const showMenu = ref(false)

// Composables
const router = useRouter()
const { likePlaylist, unlikePlaylist } = usePlaylistApi()
const { showNotification } = useNotification()
const { handleError } = useErrorHandler()

// 导航到详情页
const navigateToDetail = () => {
  router.push(`/playlists/${props.playlist.id}`)
}

// 编辑歌单
const handleEdit = () => {
  showMenu.value = false
  emit('edit', props.playlist)
}

// 删除歌单
const handleDelete = () => {
  showMenu.value = false
  emit('delete', props.playlist)
}

// 点赞/取消点赞
const handleLike = async () => {
  try {
    const response = props.playlist.isLiked
      ? await unlikePlaylist(props.playlist.id)
      : await likePlaylist(props.playlist.id)

    // 更新本地状态
    props.playlist.isLiked = response.data.isLiked
    props.playlist.likeCount = response.data.likeCount

    showNotification(
      response.data.isLiked ? '已添加到收藏' : '已取消收藏',
      'success'
    )

    emit('like', props.playlist)
  } catch (error) {
    handleError(error, '操作失败')
  }
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return '今天'
  } else if (diffDays === 1) {
    return '昨天'
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else if (diffDays < 30) {
    return `${Math.floor(diffDays / 7)}周前`
  } else if (diffDays < 365) {
    return `${Math.floor(diffDays / 30)}个月前`
  } else {
    return `${Math.floor(diffDays / 365)}年前`
  }
}

// 点击外部关闭菜单
const vClickOutside = {
  mounted(el: HTMLElement, binding: any) {
    el._clickOutside = (event: Event) => {
      if (!(el === event.target || el.contains(event.target as Node))) {
        binding.value()
      }
    }
    document.addEventListener('click', el._clickOutside)
  },
  unmounted(el: HTMLElement) {
    document.removeEventListener('click', el._clickOutside)
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
