<template>
  <div class="activity-item bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <div class="flex space-x-4">
      <!-- 用户头像 -->
      <div class="flex-shrink-0">
        <NuxtLink :to="`/users/${activity.user.id}`">
          <div class="w-12 h-12 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white font-semibold text-lg overflow-hidden hover:ring-2 hover:ring-primary-300 transition-all">
            <img 
              v-if="activity.user.avatar" 
              :src="activity.user.avatar" 
              :alt="activity.user.username"
              class="w-full h-full object-cover"
            />
            <span v-else>
              {{ activity.user.username.charAt(0).toUpperCase() }}
            </span>
          </div>
        </NuxtLink>
      </div>

      <!-- 动态内容 -->
      <div class="flex-1 min-w-0">
        <!-- 动态头部 -->
        <div class="flex items-center space-x-2 mb-2">
          <NuxtLink 
            :to="`/users/${activity.user.id}`"
            class="font-semibold text-gray-900 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
          >
            {{ activity.user.username }}
          </NuxtLink>
          
          <span class="text-gray-500 dark:text-gray-400">
            {{ getActivityText(activity.type) }}
          </span>
          
          <span class="text-sm text-gray-500 dark:text-gray-400">
            {{ formatTimeAgo(activity.createdAt) }}
          </span>
        </div>

        <!-- 动态描述 -->
        <p v-if="activity.content" class="text-gray-700 dark:text-gray-300 mb-3">
          {{ activity.content }}
        </p>

        <!-- 动态目标内容 -->
        <div v-if="activity.target" class="mb-4">
          <!-- 音乐目标 -->
          <div 
            v-if="activity.targetType === 'music'"
            class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600"
          >
            <div class="flex items-center space-x-3">
              <div class="w-16 h-16 rounded-lg bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center overflow-hidden">
                <img 
                  v-if="activity.target.coverUrl" 
                  :src="activity.target.coverUrl" 
                  :alt="activity.target.title"
                  class="w-full h-full object-cover"
                />
                <UiIcon v-else name="musical-note" size="lg" class="text-white" />
              </div>
              
              <div class="flex-1 min-w-0">
                <h4 class="font-medium text-gray-900 dark:text-gray-100 truncate">
                  {{ activity.target.title }}
                </h4>
                <p class="text-sm text-gray-600 dark:text-gray-400 truncate">
                  {{ activity.target.artist }}
                </p>
                <div class="flex items-center space-x-4 mt-1 text-xs text-gray-500 dark:text-gray-400">
                  <span class="flex items-center space-x-1">
                    <UiIcon name="play" size="sm" />
                    <span>{{ formatNumber(activity.target.playCount) }}</span>
                  </span>
                  <span class="flex items-center space-x-1">
                    <UiIcon name="heart" size="sm" />
                    <span>{{ formatNumber(activity.target.likeCount) }}</span>
                  </span>
                </div>
              </div>
              
              <UiButton
                variant="ghost"
                size="sm"
                @click="playMusic(activity.target)"
              >
                <UiIcon name="play" size="sm" />
              </UiButton>
            </div>
          </div>

          <!-- 歌单目标 -->
          <div 
            v-else-if="activity.targetType === 'playlist'"
            class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600"
          >
            <div class="flex items-center space-x-3">
              <div class="w-16 h-16 rounded-lg bg-gradient-to-br from-secondary-400 to-secondary-600 flex items-center justify-center overflow-hidden">
                <img 
                  v-if="activity.target.coverUrl" 
                  :src="activity.target.coverUrl" 
                  :alt="activity.target.name"
                  class="w-full h-full object-cover"
                />
                <UiIcon v-else name="queue-list" size="lg" class="text-white" />
              </div>
              
              <div class="flex-1 min-w-0">
                <h4 class="font-medium text-gray-900 dark:text-gray-100 truncate">
                  {{ activity.target.name }}
                </h4>
                <p class="text-sm text-gray-600 dark:text-gray-400 truncate">
                  {{ activity.target.description }}
                </p>
                <div class="flex items-center space-x-4 mt-1 text-xs text-gray-500 dark:text-gray-400">
                  <span class="flex items-center space-x-1">
                    <UiIcon name="musical-note" size="sm" />
                    <span>{{ activity.target.songCount }} 首歌曲</span>
                  </span>
                  <span class="flex items-center space-x-1">
                    <UiIcon name="heart" size="sm" />
                    <span>{{ formatNumber(activity.target.likeCount) }}</span>
                  </span>
                </div>
              </div>
              
              <UiButton
                variant="ghost"
                size="sm"
                @click="viewPlaylist(activity.target)"
              >
                <UiIcon name="eye" size="sm" />
              </UiButton>
            </div>
          </div>
        </div>

        <!-- 动态操作 -->
        <div class="flex items-center space-x-6 pt-3 border-t border-gray-200 dark:border-gray-700">
          <!-- 点赞 -->
          <button
            @click="handleLike"
            class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
            :class="{ 'text-red-500 dark:text-red-400': isLiked }"
          >
            <UiIcon 
              :name="isLiked ? 'heart-solid' : 'heart'" 
              size="sm"
              :class="{ 'text-red-500': isLiked }"
            />
            <span>{{ likeCount > 0 ? likeCount : '点赞' }}</span>
          </button>

          <!-- 评论 -->
          <button
            @click="handleComment"
            class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
          >
            <UiIcon name="chat-bubble-left" size="sm" />
            <span>{{ commentCount > 0 ? commentCount : '评论' }}</span>
          </button>

          <!-- 分享 -->
          <button
            @click="handleShare"
            class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
          >
            <UiIcon name="share" size="sm" />
            <span>分享</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  activity: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  like: [activityId: string]
  comment: [activityId: string]
  share: [activityId: string]
}>()

const authStore = useAuthStore()
const socialStore = useSocialStore()
const playerStore = usePlayerStore()
const { formatTimeAgo, formatNumber } = useUtils()
const { showNotification } = useNotification()

// 计算属性
const isLiked = computed(() => {
  return socialStore.isLiked('activity', props.activity.id)
})

const likeCount = computed(() => {
  return socialStore.getLikeCount('activity', props.activity.id) || props.activity.likeCount || 0
})

const commentCount = computed(() => {
  return socialStore.getCommentCount('activity', props.activity.id) || props.activity.commentCount || 0
})

// 获取动态类型文本
const getActivityText = (type: string) => {
  const typeMap: Record<string, string> = {
    'music_upload': '上传了音乐',
    'playlist_create': '创建了歌单',
    'music_like': '点赞了音乐',
    'playlist_like': '点赞了歌单',
    'follow': '关注了用户',
    'comment': '发表了评论'
  }
  return typeMap[type] || '发布了动态'
}

// 处理点赞
const handleLike = async () => {
  if (!authStore.isAuthenticated) {
    showNotification('请先登录', 'warning')
    return
  }

  try {
    await socialStore.toggleLike('activity', props.activity.id)
    emit('like', props.activity.id)
  } catch (error) {
    console.error('Like activity error:', error)
  }
}

// 处理评论
const handleComment = () => {
  emit('comment', props.activity.id)
}

// 处理分享
const handleShare = () => {
  emit('share', props.activity.id)
}

// 播放音乐
const playMusic = (music: any) => {
  playerStore.playMusic(music)
}

// 查看歌单
const viewPlaylist = (playlist: any) => {
  navigateTo(`/playlists/${playlist.id}`)
}

// 初始化点赞状态
onMounted(async () => {
  if (authStore.isAuthenticated) {
    try {
      await socialStore.loadLikeStatus('activity', props.activity.id)
    } catch (error) {
      console.error('Load like status error:', error)
    }
  }
})
</script>

<style scoped>
.activity-item {
  @apply transition-all duration-200;
}

.activity-item:hover {
  @apply shadow-md;
}
</style>
