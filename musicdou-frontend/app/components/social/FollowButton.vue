<template>
  <UiButton
    :variant="isFollowing ? 'secondary' : 'primary'"
    :size="size"
    :loading="loading"
    :disabled="disabled || isCurrentUser"
    @click="handleToggleFollow"
    class="follow-button"
  >
    <template #icon>
      <UiIcon 
        :name="isFollowing ? 'user-minus' : 'user-plus'" 
        :size="size === 'sm' ? 'sm' : 'md'"
      />
    </template>
    
    <span v-if="showText">
      {{ isFollowing ? (unfollowText || '取消关注') : (followText || '关注') }}
    </span>
  </UiButton>
</template>

<script setup lang="ts">
interface Props {
  userId: string
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
  followText?: string
  unfollowText?: string
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  showText: true,
  disabled: false
})

const emit = defineEmits<{
  followed: [userId: string]
  unfollowed: [userId: string]
  error: [error: any]
}>()

const authStore = useAuthStore()
const socialStore = useSocialStore()
const { showNotification } = useNotification()

const loading = ref(false)

// 计算属性
const isCurrentUser = computed(() => {
  return authStore.user?.id === props.userId
})

const isFollowing = computed(() => {
  return socialStore.isFollowing(props.userId)
})

// 切换关注状态
const handleToggleFollow = async () => {
  if (!authStore.isAuthenticated) {
    showNotification('请先登录', 'warning')
    await navigateTo('/login')
    return
  }

  if (isCurrentUser.value) {
    return
  }

  loading.value = true

  try {
    if (isFollowing.value) {
      // 取消关注
      const response = await socialStore.unfollowUser(props.userId)
      if (response.success) {
        showNotification('已取消关注', 'success')
        emit('unfollowed', props.userId)
      }
    } else {
      // 关注用户
      const response = await socialStore.followUser(props.userId, 'profile')
      if (response.success) {
        showNotification('关注成功', 'success')
        emit('followed', props.userId)
      }
    }
  } catch (error: any) {
    console.error('Follow toggle error:', error)
    showNotification(error.message || '操作失败', 'error')
    emit('error', error)
  } finally {
    loading.value = false
  }
}

// 初始化关注状态
onMounted(async () => {
  if (authStore.isAuthenticated && !isCurrentUser.value) {
    try {
      await socialStore.loadFollowStatus(props.userId)
    } catch (error) {
      console.error('Load follow status error:', error)
    }
  }
})
</script>

<style scoped>
.follow-button {
  @apply transition-all duration-200;
}

.follow-button:hover {
  @apply transform scale-105;
}

.follow-button:active {
  @apply transform scale-95;
}
</style>
