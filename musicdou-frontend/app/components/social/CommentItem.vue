<template>
  <div class="comment-item">
    <div class="flex space-x-3">
      <!-- 用户头像 -->
      <div class="flex-shrink-0">
        <NuxtLink :to="`/users/${comment.user.id}`">
          <div class="w-8 h-8 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white font-semibold text-sm overflow-hidden hover:ring-2 hover:ring-primary-300 transition-all">
            <img 
              v-if="comment.user.avatar" 
              :src="comment.user.avatar" 
              :alt="comment.user.username"
              class="w-full h-full object-cover"
            />
            <span v-else>
              {{ comment.user.username.charAt(0).toUpperCase() }}
            </span>
          </div>
        </NuxtLink>
      </div>

      <!-- 评论内容 -->
      <div class="flex-1 min-w-0">
        <!-- 用户信息和时间 -->
        <div class="flex items-center space-x-2 mb-1">
          <NuxtLink 
            :to="`/users/${comment.user.id}`"
            class="font-medium text-sm text-gray-900 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
          >
            {{ comment.user.username }}
          </NuxtLink>
          
          <!-- VIP 标识 -->
          <span 
            v-if="comment.user.userGroup === 'vip'"
            class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
          >
            VIP
          </span>
          
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ formatTimeAgo(comment.createdAt) }}
          </span>
        </div>

        <!-- 评论文本 -->
        <div class="text-sm text-gray-700 dark:text-gray-300 mb-2 whitespace-pre-wrap">
          {{ comment.content }}
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-center space-x-4">
          <!-- 点赞按钮 -->
          <button
            @click="handleLike"
            class="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
            :class="{ 'text-red-500 dark:text-red-400': isLiked }"
          >
            <UiIcon 
              :name="isLiked ? 'heart-solid' : 'heart'" 
              size="sm"
              :class="{ 'text-red-500': isLiked }"
            />
            <span>{{ likeCount > 0 ? likeCount : '点赞' }}</span>
          </button>

          <!-- 回复按钮 -->
          <button
            @click="handleReply"
            class="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
          >
            <UiIcon name="chat-bubble-left" size="sm" />
            <span>回复</span>
          </button>

          <!-- 删除按钮 (仅作者可见) -->
          <button
            v-if="canDelete"
            @click="handleDelete"
            class="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
          >
            <UiIcon name="trash" size="sm" />
            <span>删除</span>
          </button>
        </div>

        <!-- 回复列表 -->
        <div v-if="comment.replies && comment.replies.length > 0" class="mt-4 space-y-3">
          <div
            v-for="reply in comment.replies"
            :key="reply.id"
            class="flex space-x-3 pl-4 border-l-2 border-gray-200 dark:border-gray-700"
          >
            <!-- 回复用户头像 -->
            <div class="flex-shrink-0">
              <NuxtLink :to="`/users/${reply.user.id}`">
                <div class="w-6 h-6 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white font-semibold text-xs overflow-hidden hover:ring-2 hover:ring-primary-300 transition-all">
                  <img 
                    v-if="reply.user.avatar" 
                    :src="reply.user.avatar" 
                    :alt="reply.user.username"
                    class="w-full h-full object-cover"
                  />
                  <span v-else>
                    {{ reply.user.username.charAt(0).toUpperCase() }}
                  </span>
                </div>
              </NuxtLink>
            </div>

            <!-- 回复内容 -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-2 mb-1">
                <NuxtLink 
                  :to="`/users/${reply.user.id}`"
                  class="font-medium text-xs text-gray-900 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                >
                  {{ reply.user.username }}
                </NuxtLink>
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  {{ formatTimeAgo(reply.createdAt) }}
                </span>
              </div>
              
              <div class="text-xs text-gray-700 dark:text-gray-300 mb-1 whitespace-pre-wrap">
                {{ reply.content }}
              </div>

              <!-- 回复操作 -->
              <div class="flex items-center space-x-3">
                <button
                  @click="() => handleLikeReply(reply.id)"
                  class="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                  :class="{ 'text-red-500 dark:text-red-400': socialStore.isLiked('comment', reply.id) }"
                >
                  <UiIcon 
                    :name="socialStore.isLiked('comment', reply.id) ? 'heart-solid' : 'heart'" 
                    size="sm"
                    :class="{ 'text-red-500': socialStore.isLiked('comment', reply.id) }"
                  />
                  <span>{{ socialStore.getLikeCount('comment', reply.id) || '点赞' }}</span>
                </button>

                <button
                  v-if="canDeleteReply(reply)"
                  @click="() => handleDeleteReply(reply.id)"
                  class="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
                >
                  <UiIcon name="trash" size="sm" />
                  <span>删除</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Comment } from '~/types'

interface Props {
  comment: Comment
  targetType: 'music' | 'playlist'
  targetId: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  reply: [comment: Comment]
  delete: [commentId: string]
  like: [commentId: string]
}>()

const authStore = useAuthStore()
const socialStore = useSocialStore()
const { formatTimeAgo } = useUtils()
const { showNotification } = useNotification()

// 计算属性
const canDelete = computed(() => {
  return authStore.user?.id === props.comment.userId || authStore.user?.userGroup === 'admin'
})

const canDeleteReply = (reply: Comment) => {
  return authStore.user?.id === reply.userId || authStore.user?.userGroup === 'admin'
}

const isLiked = computed(() => {
  return socialStore.isLiked('comment', props.comment.id)
})

const likeCount = computed(() => {
  return socialStore.getLikeCount('comment', props.comment.id) || props.comment.likeCount
})

// 处理点赞
const handleLike = async () => {
  if (!authStore.isAuthenticated) {
    showNotification('请先登录', 'warning')
    return
  }

  try {
    await socialStore.toggleLike('comment', props.comment.id)
    emit('like', props.comment.id)
  } catch (error) {
    console.error('Like comment error:', error)
  }
}

// 处理回复点赞
const handleLikeReply = async (replyId: string) => {
  if (!authStore.isAuthenticated) {
    showNotification('请先登录', 'warning')
    return
  }

  try {
    await socialStore.toggleLike('comment', replyId)
  } catch (error) {
    console.error('Like reply error:', error)
  }
}

// 处理回复
const handleReply = () => {
  if (!authStore.isAuthenticated) {
    showNotification('请先登录', 'warning')
    return
  }
  
  emit('reply', props.comment)
}

// 处理删除
const handleDelete = async () => {
  if (confirm('确定要删除这条评论吗？')) {
    emit('delete', props.comment.id)
  }
}

// 处理删除回复
const handleDeleteReply = async (replyId: string) => {
  if (confirm('确定要删除这条回复吗？')) {
    try {
      await socialStore.deleteComment(replyId, props.targetType, props.targetId)
      
      // 从回复列表中移除
      if (props.comment.replies) {
        props.comment.replies = props.comment.replies.filter(r => r.id !== replyId)
      }
      
      showNotification('回复已删除', 'success')
    } catch (error) {
      console.error('Delete reply error:', error)
      showNotification('删除失败', 'error')
    }
  }
}

// 初始化点赞状态
onMounted(async () => {
  if (authStore.isAuthenticated) {
    try {
      await socialStore.loadLikeStatus('comment', props.comment.id)
      
      // 加载回复的点赞状态
      if (props.comment.replies) {
        for (const reply of props.comment.replies) {
          await socialStore.loadLikeStatus('comment', reply.id)
        }
      }
    } catch (error) {
      console.error('Load like status error:', error)
    }
  }
})
</script>

<style scoped>
.comment-item {
  @apply transition-all duration-200;
}

.comment-item:hover {
  @apply bg-gray-50 dark:bg-gray-800/50 rounded-lg p-2 -m-2;
}
</style>
