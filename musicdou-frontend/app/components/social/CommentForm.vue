<template>
  <div class="comment-form">
    <div class="flex space-x-3">
      <!-- 用户头像 -->
      <div class="flex-shrink-0">
        <div class="w-8 h-8 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white font-semibold text-sm overflow-hidden">
          <img 
            v-if="authStore.user?.avatar" 
            :src="authStore.user.avatar" 
            :alt="authStore.user.username"
            class="w-full h-full object-cover"
          />
          <span v-else>
            {{ authStore.user?.username?.charAt(0).toUpperCase() }}
          </span>
        </div>
      </div>

      <!-- 评论输入区域 -->
      <div class="flex-1">
        <div class="relative">
          <textarea
            v-model="content"
            :placeholder="placeholder"
            :disabled="loading || !authStore.isAuthenticated"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
            :class="{ 'opacity-50 cursor-not-allowed': loading || !authStore.isAuthenticated }"
            rows="3"
            maxlength="500"
            @keydown="handleKeydown"
          />
          
          <!-- 字符计数 -->
          <div class="absolute bottom-2 right-2 text-xs text-gray-400">
            {{ content.length }}/500
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-center justify-between mt-3">
          <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
            <UiIcon name="information-circle" size="sm" />
            <span>支持 Ctrl+Enter 快速发送</span>
          </div>

          <div class="flex items-center space-x-2">
            <UiButton
              v-if="parentId"
              variant="ghost"
              size="sm"
              @click="handleCancel"
            >
              取消
            </UiButton>
            
            <UiButton
              variant="primary"
              size="sm"
              :loading="loading"
              :disabled="!canSubmit"
              @click="handleSubmit"
            >
              {{ parentId ? '回复' : '发表评论' }}
            </UiButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 登录提示 -->
    <div 
      v-if="!authStore.isAuthenticated"
      class="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
    >
      <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
        <UiIcon name="exclamation-circle" size="sm" />
        <span>请先</span>
        <NuxtLink 
          to="/login" 
          class="text-primary-600 dark:text-primary-400 hover:underline font-medium"
        >
          登录
        </NuxtLink>
        <span>后发表评论</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  targetType: 'music' | 'playlist'
  targetId: string
  parentId?: string
  placeholder?: string
  autoFocus?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '写下你的评论...',
  autoFocus: false
})

const emit = defineEmits<{
  submitted: [comment: any]
  cancelled: []
}>()

const authStore = useAuthStore()
const socialStore = useSocialStore()
const { showNotification } = useNotification()

const content = ref('')
const loading = ref(false)

// 计算属性
const canSubmit = computed(() => {
  return content.value.trim().length > 0 && content.value.length <= 500 && authStore.isAuthenticated
})

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.ctrlKey && event.key === 'Enter') {
    event.preventDefault()
    handleSubmit()
  }
}

// 提交评论
const handleSubmit = async () => {
  if (!canSubmit.value) return

  loading.value = true

  try {
    const response = await socialStore.addComment(
      props.targetType,
      props.targetId,
      content.value.trim(),
      props.parentId
    )

    if (response.success) {
      showNotification(props.parentId ? '回复成功' : '评论发表成功', 'success')
      emit('submitted', response.data)
      content.value = ''
    }
  } catch (error: any) {
    console.error('Submit comment error:', error)
    showNotification(error.message || '发表失败', 'error')
  } finally {
    loading.value = false
  }
}

// 取消回复
const handleCancel = () => {
  content.value = ''
  emit('cancelled')
}

// 自动聚焦
onMounted(() => {
  if (props.autoFocus) {
    nextTick(() => {
      const textarea = document.querySelector('.comment-form textarea') as HTMLTextAreaElement
      textarea?.focus()
    })
  }
})
</script>

<style scoped>
.comment-form textarea {
  @apply transition-all duration-200;
}

.comment-form textarea:focus {
  @apply shadow-sm;
}
</style>
