<template>
  <div class="comment-list">
    <!-- 评论统计和排序 -->
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
        评论 ({{ totalComments }})
      </h3>
      
      <div class="flex items-center space-x-2">
        <label class="text-sm text-gray-600 dark:text-gray-400">排序:</label>
        <select
          v-model="sortBy"
          class="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          @change="handleSortChange"
        >
          <option value="newest">最新</option>
          <option value="oldest">最早</option>
          <option value="hot">最热</option>
          <option value="likes">最多点赞</option>
        </select>
      </div>
    </div>

    <!-- 发表评论表单 -->
    <div class="mb-8">
      <SocialCommentForm
        :target-type="targetType"
        :target-id="targetId"
        @submitted="handleCommentSubmitted"
      />
    </div>

    <!-- 评论列表 -->
    <div class="space-y-6">
      <!-- 加载状态 -->
      <div v-if="loading && comments.length === 0" class="space-y-4">
        <div v-for="i in 3" :key="i" class="animate-pulse">
          <div class="flex space-x-3">
            <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
            <div class="flex-1 space-y-2">
              <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/4"></div>
              <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
              <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 评论项 -->
      <div v-else-if="comments.length > 0">
        <SocialCommentItem
          v-for="comment in comments"
          :key="comment.id"
          :comment="comment"
          :target-type="targetType"
          :target-id="targetId"
          @reply="handleReply"
          @delete="handleDelete"
          @like="handleLike"
        />
      </div>

      <!-- 空状态 -->
      <div v-else class="text-center py-12">
        <UiIcon name="chat-bubble-left-ellipsis" size="xl" class="mx-auto text-gray-400 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          暂无评论
        </h3>
        <p class="text-gray-600 dark:text-gray-400">
          成为第一个发表评论的人吧！
        </p>
      </div>
    </div>

    <!-- 加载更多 -->
    <div v-if="hasMore" class="mt-8 text-center">
      <UiButton
        variant="ghost"
        :loading="loadingMore"
        @click="loadMore"
      >
        加载更多评论
      </UiButton>
    </div>

    <!-- 回复模态框 -->
    <UiModal
      v-model="showReplyModal"
      title="回复评论"
      size="md"
    >
      <div v-if="replyingComment" class="space-y-4">
        <!-- 原评论 -->
        <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="flex items-center space-x-2 mb-2">
            <span class="font-medium text-sm text-gray-900 dark:text-gray-100">
              {{ replyingComment.user.username }}
            </span>
            <span class="text-xs text-gray-500 dark:text-gray-400">
              {{ formatTimeAgo(replyingComment.createdAt) }}
            </span>
          </div>
          <p class="text-sm text-gray-700 dark:text-gray-300">
            {{ replyingComment.content }}
          </p>
        </div>

        <!-- 回复表单 -->
        <SocialCommentForm
          :target-type="targetType"
          :target-id="targetId"
          :parent-id="replyingComment.id"
          :placeholder="`回复 @${replyingComment.user.username}...`"
          auto-focus
          @submitted="handleReplySubmitted"
          @cancelled="closeReplyModal"
        />
      </div>
    </UiModal>
  </div>
</template>

<script setup lang="ts">
import type { Comment } from '~/types'

interface Props {
  targetType: 'music' | 'playlist'
  targetId: string
}

const props = defineProps<Props>()

const socialStore = useSocialStore()
const { formatTimeAgo } = useUtils()

const comments = ref<Comment[]>([])
const loading = ref(false)
const loadingMore = ref(false)
const sortBy = ref<'newest' | 'oldest' | 'hot' | 'likes'>('newest')
const currentPage = ref(1)
const hasMore = ref(false)
const totalComments = ref(0)

// 回复相关状态
const showReplyModal = ref(false)
const replyingComment = ref<Comment | null>(null)

// 加载评论
const loadComments = async (page = 1, append = false) => {
  try {
    if (page === 1) {
      loading.value = true
    } else {
      loadingMore.value = true
    }

    const response = await socialStore.loadComments(props.targetType, props.targetId, {
      page,
      limit: 20,
      sortBy: sortBy.value,
      includeReplies: true
    })

    if (response.success) {
      if (append) {
        comments.value.push(...response.data.data)
      } else {
        comments.value = response.data.data
      }
      
      totalComments.value = response.data.pagination.total
      hasMore.value = response.data.pagination.page < response.data.pagination.totalPages
      currentPage.value = response.data.pagination.page
    }
  } catch (error) {
    console.error('Load comments error:', error)
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 处理排序变化
const handleSortChange = () => {
  currentPage.value = 1
  loadComments(1, false)
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loadingMore.value) {
    loadComments(currentPage.value + 1, true)
  }
}

// 处理新评论提交
const handleCommentSubmitted = (comment: Comment) => {
  comments.value.unshift(comment)
  totalComments.value++
}

// 处理回复
const handleReply = (comment: Comment) => {
  replyingComment.value = comment
  showReplyModal.value = true
}

// 处理回复提交
const handleReplySubmitted = (reply: Comment) => {
  // 找到父评论并添加回复
  const parentComment = comments.value.find(c => c.id === reply.parentId)
  if (parentComment) {
    if (!parentComment.replies) {
      parentComment.replies = []
    }
    parentComment.replies.push(reply)
  }
  
  totalComments.value++
  closeReplyModal()
}

// 关闭回复模态框
const closeReplyModal = () => {
  showReplyModal.value = false
  replyingComment.value = null
}

// 处理删除评论
const handleDelete = async (commentId: string) => {
  try {
    await socialStore.deleteComment(commentId, props.targetType, props.targetId)
    
    // 从列表中移除评论
    comments.value = comments.value.filter(c => c.id !== commentId)
    totalComments.value = Math.max(0, totalComments.value - 1)
  } catch (error) {
    console.error('Delete comment error:', error)
  }
}

// 处理点赞评论
const handleLike = async (commentId: string) => {
  try {
    await socialStore.toggleLike('comment', commentId)
  } catch (error) {
    console.error('Like comment error:', error)
  }
}

// 初始化
onMounted(() => {
  loadComments()
})

// 监听目标变化
watch(() => [props.targetType, props.targetId], () => {
  currentPage.value = 1
  loadComments()
})
</script>

<style scoped>
.comment-list {
  @apply space-y-4;
}
</style>
