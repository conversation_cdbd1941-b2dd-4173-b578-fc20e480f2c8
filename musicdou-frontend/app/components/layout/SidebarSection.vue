<template>
  <div class="mb-4">
    <!-- 分组标题 -->
    <button
      v-if="!collapsed"
      @click="$emit('toggle')"
      class="w-full flex items-center justify-between px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
    >
      <h3 class="text-sm font-medium text-gray-900 dark:text-white">
        {{ title }}
      </h3>
      
      <div class="flex items-center gap-2">
        <!-- 加载状态 -->
        <Loading v-if="loading" size="sm" />
        
        <!-- 创建按钮 -->
        <button
          v-if="showCreateButton"
          @click.stop="$emit('create')"
          class="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          title="创建歌单"
        >
          <Icon name="plus" class="w-3 h-3 text-gray-500 dark:text-gray-400" />
        </button>
        
        <!-- 展开/收起图标 -->
        <Icon 
          :name="expanded ? 'chevron-down' : 'chevron-right'" 
          class="w-4 h-4 text-gray-500 dark:text-gray-400 transition-transform duration-200" 
        />
      </div>
    </button>
    
    <!-- 折叠状态下的图标 -->
    <div v-else class="flex justify-center py-2">
      <Icon 
        name="musical-note" 
        class="w-5 h-5 text-gray-500 dark:text-gray-400" 
      />
    </div>

    <!-- 内容区域 -->
    <Transition
      enter-active-class="transition-all duration-200 ease-out"
      enter-from-class="opacity-0 max-h-0"
      enter-to-class="opacity-100 max-h-96"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 max-h-96"
      leave-to-class="opacity-0 max-h-0"
    >
      <div v-if="expanded || collapsed" class="overflow-hidden">
        <div class="space-y-1 mt-2">
          <slot />
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
  collapsed: boolean
  expanded: boolean
  loading?: boolean
  showCreateButton?: boolean
}

interface Emits {
  (e: 'toggle'): void
  (e: 'create'): void
}

withDefaults(defineProps<Props>(), {
  loading: false,
  showCreateButton: false
})

defineEmits<Emits>()
</script>
