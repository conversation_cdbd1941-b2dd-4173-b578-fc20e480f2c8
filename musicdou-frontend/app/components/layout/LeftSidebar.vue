<template>
  <aside class="h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
    <!-- 侧边栏头部 -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <h2 v-if="!collapsed" class="text-lg font-semibold text-gray-900 dark:text-white">
          MusicDou
        </h2>
        <button
          @click="toggleCollapse"
          class="p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          <Icon
            :name="collapsed ? 'chevron-right' : 'chevron-left'"
            class="w-5 h-5 text-gray-500 dark:text-gray-400"
          />
        </button>
      </div>
    </div>

    <!-- 导航菜单 -->
    <nav class="flex-1 overflow-y-auto p-2">
      <!-- 推荐音乐 -->
      <div class="space-y-1 mb-6">
        <div v-if="!collapsed" class="px-3 py-2">
          <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            推荐
          </h3>
        </div>
        <SidebarNavItem
          v-for="item in recommendNavItems"
          :key="item.name"
          :item="item"
          :collapsed="collapsed"
          @click="handleNavClick(item)"
        />
      </div>

      <!-- 社交功能 -->
      <div class="space-y-1 mb-6">
        <div v-if="!collapsed" class="px-3 py-2">
          <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            社交
          </h3>
        </div>
        <SidebarNavItem
          v-for="item in socialNavItems"
          :key="item.name"
          :item="item"
          :collapsed="collapsed"
          @click="handleNavClick(item)"
        />
      </div>

      <!-- 歌单分组 -->
      <div class="mb-6">
        <div v-if="!collapsed" class="px-3 py-2">
          <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            歌单
          </h3>
        </div>

        <!-- 默认歌单 -->
        <SidebarSection
          title="默认歌单"
          :collapsed="collapsed"
          :expanded="defaultPlaylistsExpanded"
          :loading="false"
          @toggle="toggleDefaultPlaylists"
        >
          <SidebarNavItem
            v-for="item in defaultPlaylistItems"
            :key="item.name"
            :item="item"
            :collapsed="collapsed"
            @click="handleNavClick(item)"
          />
        </SidebarSection>
      </div>

      <!-- 我的歌单 -->
      <div class="mb-6">
        <SidebarSection
          title="我的歌单"
          :collapsed="collapsed"
          :expanded="myPlaylistsExpanded"
          :loading="loadingMyPlaylists"
          :showCreateButton="true"
          @toggle="toggleMyPlaylists"
          @create="showCreatePlaylistModal = true"
        >
          <SidebarPlaylistItem
            v-for="playlist in myPlaylists"
            :key="playlist.id"
            :playlist="playlist"
            :collapsed="collapsed"
            @click="navigateToPlaylist(playlist.id)"
          />

          <div v-if="myPlaylists.length === 0 && !loadingMyPlaylists" class="px-4 py-2">
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ collapsed ? '' : '暂无歌单' }}
            </p>
          </div>
        </SidebarSection>
      </div>

      <!-- 收藏歌单 -->
      <div class="mb-6">
        <SidebarSection
          title="收藏歌单"
          :collapsed="collapsed"
          :expanded="likedPlaylistsExpanded"
          :loading="loadingLikedPlaylists"
          @toggle="toggleLikedPlaylists"
        >
          <SidebarPlaylistItem
            v-for="playlist in likedPlaylists"
            :key="playlist.id"
            :playlist="playlist"
            :collapsed="collapsed"
            @click="navigateToPlaylist(playlist.id)"
          />

          <div v-if="likedPlaylists.length === 0 && !loadingLikedPlaylists" class="px-4 py-2">
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ collapsed ? '' : '暂无收藏' }}
            </p>
          </div>
        </SidebarSection>
      </div>
    </nav>

    <!-- 底部操作 -->
    <div v-if="!collapsed" class="p-4 border-t border-gray-200 dark:border-gray-700">
      <Button
        @click="showCreatePlaylistModal = true"
        variant="outline"
        size="sm"
        class="w-full flex items-center justify-center gap-2"
      >
        <Icon name="plus" class="w-4 h-4" />
        创建歌单
      </Button>
    </div>

    <!-- 创建歌单模态框 -->
    <PlaylistForm
      v-if="showCreatePlaylistModal"
      @close="showCreatePlaylistModal = false"
      @success="handleCreateSuccess"
    />
  </aside>
</template>

<script setup lang="ts">
import type { Playlist } from '~/types'

// 注入的状态
const collapsed = inject('sidebarCollapsed', ref(false))
const toggleSidebar = inject('toggleSidebar', () => {})

// 响应式状态
const myPlaylistsExpanded = ref(true)
const likedPlaylistsExpanded = ref(true)
const defaultPlaylistsExpanded = ref(true)
const showCreatePlaylistModal = ref(false)

// 数据状态
const myPlaylists = ref<Playlist[]>([])
const likedPlaylists = ref<Playlist[]>([])
const loadingMyPlaylists = ref(false)
const loadingLikedPlaylists = ref(false)

// Composables
const { getUserPlaylists, getLikedPlaylists } = usePlaylistApi()
const { user } = useAuth()
const { showNotification } = useNotification()
const { handleError } = useErrorHandler()

// 推荐导航项
const recommendNavItems = [
  {
    name: '发现音乐',
    icon: 'musical-note',
    route: '/',
    active: false
  },
  {
    name: '每日推荐',
    icon: 'calendar-days',
    route: '/daily-recommend',
    active: false
  },
  {
    name: '排行榜',
    icon: 'trophy',
    route: '/charts',
    active: false
  }
]

// 社交导航项
const socialNavItems = [
  {
    name: '朋友',
    icon: 'users',
    route: '/social/friends',
    active: false
  },
  {
    name: '动态',
    icon: 'rss',
    route: '/social/feed',
    active: false
  },
  {
    name: '附近的人',
    icon: 'map-pin',
    route: '/social/nearby',
    active: false
  }
]

// 默认歌单项
const defaultPlaylistItems = [
  {
    name: '我喜欢的音乐',
    icon: 'heart',
    route: '/playlists/liked',
    active: false
  },
  {
    name: '最近播放',
    icon: 'clock',
    route: '/playlists/recent',
    active: false
  },
  {
    name: '下载的音乐',
    icon: 'arrow-down-tray',
    route: '/playlists/downloaded',
    active: false
  }
]

// 当前路由
const route = useRoute()

// 计算当前激活的导航项
watchEffect(() => {
  // 更新推荐导航项状态
  recommendNavItems.forEach(item => {
    item.active = route.path === item.route || (item.route === '/' && route.path === '/')
  })

  // 更新社交导航项状态
  socialNavItems.forEach(item => {
    item.active = route.path.startsWith(item.route)
  })

  // 更新默认歌单项状态
  defaultPlaylistItems.forEach(item => {
    item.active = route.path.startsWith(item.route)
  })
})

// 切换折叠状态
const toggleCollapse = () => {
  toggleSidebar()
}

// 获取我的歌单
const fetchMyPlaylists = async () => {
  try {
    loadingMyPlaylists.value = true
    const response = await getUserPlaylists({ limit: 20 })
    myPlaylists.value = response.data.items || []
  } catch (error) {
    handleError(error, '获取我的歌单失败')
  } finally {
    loadingMyPlaylists.value = false
  }
}

// 获取收藏的歌单
const fetchLikedPlaylists = async () => {
  try {
    loadingLikedPlaylists.value = true
    const response = await getLikedPlaylists({ limit: 20 })
    likedPlaylists.value = response.data.items || []
  } catch (error) {
    handleError(error, '获取收藏歌单失败')
  } finally {
    loadingLikedPlaylists.value = false
  }
}

// 处理导航点击
const handleNavClick = (item: any) => {
  navigateTo(item.route)
}

// 跳转到歌单详情
const navigateToPlaylist = (playlistId: string) => {
  navigateTo(`/playlists/${playlistId}`)
}

// 切换我的歌单展开状态
const toggleMyPlaylists = () => {
  myPlaylistsExpanded.value = !myPlaylistsExpanded.value
  if (myPlaylistsExpanded.value && myPlaylists.value.length === 0) {
    fetchMyPlaylists()
  }
}

// 切换默认歌单展开状态
const toggleDefaultPlaylists = () => {
  defaultPlaylistsExpanded.value = !defaultPlaylistsExpanded.value
}

// 切换收藏歌单展开状态
const toggleLikedPlaylists = () => {
  likedPlaylistsExpanded.value = !likedPlaylistsExpanded.value
  if (likedPlaylistsExpanded.value && likedPlaylists.value.length === 0) {
    fetchLikedPlaylists()
  }
}

// 创建歌单成功回调
const handleCreateSuccess = (playlist: Playlist) => {
  myPlaylists.value.unshift(playlist)
  showNotification('歌单创建成功', 'success')
}

// 初始化数据
onMounted(() => {
  if (user.value) {
    fetchMyPlaylists()
    fetchLikedPlaylists()
  }
})

// 监听用户状态变化
watch(user, (newUser) => {
  if (newUser) {
    fetchMyPlaylists()
    fetchLikedPlaylists()
  } else {
    myPlaylists.value = []
    likedPlaylists.value = []
  }
}, { immediate: true })
</script>
