<template>
  <aside class="h-full bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 flex flex-col">
    <!-- 播放队列头部 -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div v-if="!collapsed" class="flex items-center gap-2">
          <Icon name="queue-list" class="w-5 h-5 text-gray-600 dark:text-gray-400" />
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            播放列表
          </h3>
        </div>

        <div class="flex items-center gap-2">
          <!-- 队列操作按钮 -->
          <div v-if="!collapsed" class="flex items-center gap-1">
            <button
              @click="toggleShuffle"
              :class="[
                'p-1.5 rounded-lg transition-colors text-xs',
                playerStore.state.shuffle
                  ? 'bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400'
              ]"
              title="随机播放"
            >
              <Icon name="arrow-path-rounded-square" class="w-4 h-4" />
            </button>

            <button
              @click="toggleRepeat"
              :class="[
                'p-1.5 rounded-lg transition-colors text-xs',
                playerStore.state.repeat !== 'none'
                  ? 'bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400'
              ]"
              :title="repeatTitle"
            >
              <Icon :name="repeatIcon" class="w-4 h-4" />
            </button>
          </div>

          <!-- 折叠按钮 -->
          <button
            @click="toggleCollapse"
            class="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <Icon
              :name="collapsed ? 'chevron-left' : 'chevron-right'"
              class="w-4 h-4 text-gray-500 dark:text-gray-400"
            />
          </button>
        </div>
      </div>

      <!-- 队列信息和操作 -->
      <div v-if="!collapsed && playerStore.hasQueue" class="mt-3 flex items-center justify-between">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          总{{ playerStore.state.queue.length }}首
        </div>
        <div class="flex items-center gap-2 text-xs">
          <button
            @click="collectAll"
            class="text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
          >
            收藏全部
          </button>
          <span class="text-gray-300 dark:text-gray-600">|</span>
          <button
            @click="clearQueue"
            class="text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
          >
            清空
          </button>
        </div>
      </div>
    </div>

    <!-- 当前播放歌曲 -->
    <div v-if="currentTrack && !collapsed" class="p-4 border-b border-gray-200 dark:border-gray-700 bg-red-50 dark:bg-red-900/10">
      <div class="text-xs font-medium text-red-600 dark:text-red-400 mb-2 flex items-center gap-1">
        <Icon name="play" class="w-3 h-3" />
        正在播放
      </div>
      <CurrentTrackItem :track="currentTrack" />
    </div>

    <!-- 播放队列列表 -->
    <div class="flex-1 overflow-y-auto">
      <div v-if="!playerStore.hasQueue" class="flex flex-col items-center justify-center h-full p-6 text-center">
        <Icon name="musical-note" class="w-16 h-16 text-gray-300 dark:text-gray-600 mb-4" />
        <h4 v-if="!collapsed" class="text-base font-medium text-gray-500 dark:text-gray-400 mb-2">
          你还没有添加任何歌曲
        </h4>
        <p v-if="!collapsed" class="text-sm text-gray-400 dark:text-gray-500">
          去首页发现音乐，或在歌曲上右键添加到播放列表
        </p>
      </div>

      <!-- 下一首播放 -->
      <div v-if="!collapsed && playerStore.hasQueue && nextTrack" class="p-3 bg-gray-50 dark:bg-gray-700/30">
        <div class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">
          下一首播放
        </div>
        <div class="flex items-center gap-3 p-2 rounded-lg hover:bg-white dark:hover:bg-gray-700 cursor-pointer transition-colors">
          <div class="w-10 h-10 rounded overflow-hidden bg-gray-200 dark:bg-gray-600 flex-shrink-0">
            <img
              v-if="nextTrack.coverUrl"
              :src="nextTrack.coverUrl"
              :alt="nextTrack.title"
              class="w-full h-full object-cover"
            />
            <div v-else class="w-full h-full flex items-center justify-center">
              <Icon name="musical-note" class="w-4 h-4 text-gray-400" />
            </div>
          </div>
          <div class="flex-1 min-w-0">
            <div class="text-sm font-medium text-gray-900 dark:text-white truncate">
              {{ nextTrack.title }}
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400 truncate">
              {{ nextTrack.artist }}
            </div>
          </div>
        </div>
      </div>

      <draggable
        v-if="playerStore.hasQueue"
        v-model="queueList"
        item-key="id"
        @end="onDragEnd"
        class="divide-y divide-gray-100 dark:divide-gray-700"
        :disabled="collapsed"
      >
        <template #item="{ element: track, index }">
          <QueueTrackItem
            :track="track"
            :index="index"
            :is-current="index === playerStore.state.currentIndex"
            :is-playing="playerStore.state.isPlaying && index === playerStore.state.currentIndex"
            :collapsed="collapsed"
            @play="playTrack(index)"
            @remove="removeTrack(index)"
          />
        </template>
      </draggable>
    </div>
  </aside>
</template>

<script setup lang="ts">
import draggable from 'vuedraggable'
import type { Music } from '~/types'

// 注入的状态
const collapsed = inject('playQueueCollapsed', ref(false))
const togglePlayQueue = inject('togglePlayQueue', () => {})

// 播放器状态
const playerStore = usePlayerStore()

// 计算属性
const currentTrack = computed(() => playerStore.state.currentTrack)

const nextTrack = computed(() => {
  const currentIndex = playerStore.state.currentIndex
  const queue = playerStore.state.queue
  if (currentIndex >= 0 && currentIndex < queue.length - 1) {
    return queue[currentIndex + 1]
  }
  return null
})

const queueList = computed({
  get: () => playerStore.state.queue,
  set: (value) => playerStore.setQueue(value)
})

const repeatIcon = computed(() => {
  switch (playerStore.state.repeat) {
    case 'one':
      return 'arrow-path'
    case 'all':
      return 'arrow-path'
    default:
      return 'arrow-path'
  }
})

const repeatTitle = computed(() => {
  switch (playerStore.state.repeat) {
    case 'one':
      return '单曲循环'
    case 'all':
      return '列表循环'
    default:
      return '循环播放'
  }
})

// 方法
const toggleCollapse = () => {
  togglePlayQueue()
}

const toggleShuffle = () => {
  playerStore.toggleShuffle()
}

const toggleRepeat = () => {
  const modes: Array<'none' | 'one' | 'all'> = ['none', 'one', 'all']
  const currentIndex = modes.indexOf(playerStore.state.repeat)
  const nextIndex = (currentIndex + 1) % modes.length
  playerStore.setRepeat(modes[nextIndex])
}

const playTrack = (index: number) => {
  playerStore.setCurrentIndex(index)
}

const removeTrack = (index: number) => {
  playerStore.removeFromQueue(index)
}

const clearQueue = () => {
  playerStore.clearQueue()
}

const collectAll = () => {
  // TODO: 实现收藏全部功能
  console.log('收藏全部歌曲')
}

const saveAsPlaylist = () => {
  // TODO: 实现保存为歌单功能
  console.log('保存为歌单')
}

const formatTotalDuration = (): string => {
  const totalSeconds = playerStore.state.queue.reduce((total, track) => total + (track.duration || 0), 0)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  }
  return `${minutes}分钟`
}

const onDragEnd = (event: any) => {
  // 拖拽结束后更新当前播放索引
  const { oldIndex, newIndex } = event
  if (oldIndex === playerStore.state.currentIndex) {
    playerStore.setCurrentIndex(newIndex)
  } else if (oldIndex < playerStore.state.currentIndex && newIndex >= playerStore.state.currentIndex) {
    playerStore.setCurrentIndex(playerStore.state.currentIndex - 1)
  } else if (oldIndex > playerStore.state.currentIndex && newIndex <= playerStore.state.currentIndex) {
    playerStore.setCurrentIndex(playerStore.state.currentIndex + 1)
  }
}
</script>
