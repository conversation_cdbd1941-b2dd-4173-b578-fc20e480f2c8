<template>
  <button
    @click="$emit('click')"
    :class="[
      'w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-all duration-200',
      'hover:bg-gray-100 dark:hover:bg-gray-700',
      item.active 
        ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 border-r-2 border-primary-500' 
        : 'text-gray-700 dark:text-gray-300'
    ]"
  >
    <Icon 
      :name="item.icon" 
      :class="[
        'w-5 h-5 flex-shrink-0',
        item.active ? 'text-primary-600 dark:text-primary-400' : 'text-gray-500 dark:text-gray-400'
      ]" 
    />
    
    <span 
      v-if="!collapsed" 
      :class="[
        'font-medium truncate',
        item.active ? 'text-primary-600 dark:text-primary-400' : 'text-gray-700 dark:text-gray-300'
      ]"
    >
      {{ item.name }}
    </span>
  </button>
</template>

<script setup lang="ts">
interface NavItem {
  name: string
  icon: string
  route: string
  active: boolean
}

interface Props {
  item: NavItem
  collapsed: boolean
}

interface Emits {
  (e: 'click'): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>
