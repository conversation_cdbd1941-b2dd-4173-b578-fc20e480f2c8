<template>
  <header class="h-16 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-4 lg:px-6">
    <!-- 左侧：Logo 和移动端菜单按钮 -->
    <div class="flex items-center gap-4">
      <!-- 移动端菜单按钮 -->
      <button
        @click="toggleMobileSidebar"
        class="lg:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
      >
        <Icon name="bars-3" class="w-5 h-5 text-gray-600 dark:text-gray-400" />
      </button>
      
      <!-- Logo -->
      <NuxtLink to="/" class="flex items-center gap-2">
        <Icon name="musical-note" class="w-8 h-8 text-primary-500" />
        <span class="text-xl font-bold text-gray-900 dark:text-white hidden sm:block">
          MusicDou
        </span>
      </NuxtLink>
    </div>

    <!-- 中间：搜索框 -->
    <div class="flex-1 max-w-md mx-4 hidden md:block">
      <div class="relative">
        <Icon 
          name="magnifying-glass" 
          class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" 
        />
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索音乐、歌单、用户..."
          class="w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-700 border border-transparent rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          @keydown.enter="handleSearch"
        />
      </div>
    </div>

    <!-- 右侧：用户操作区域 -->
    <div class="flex items-center gap-3">
      <!-- 搜索按钮 (移动端) -->
      <button
        @click="showMobileSearch = true"
        class="md:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
      >
        <Icon name="magnifying-glass" class="w-5 h-5 text-gray-600 dark:text-gray-400" />
      </button>

      <!-- 主题切换 -->
      <button
        @click="toggleTheme"
        class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        :title="themeName"
      >
        <Icon 
          :name="isDark ? 'sun' : 'moon'" 
          class="w-5 h-5 text-gray-600 dark:text-gray-400" 
        />
      </button>

      <!-- 通知 -->
      <button
        @click="showNotifications = true"
        class="relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
      >
        <Icon name="bell" class="w-5 h-5 text-gray-600 dark:text-gray-400" />
        <span
          v-if="unreadCount > 0"
          class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"
        >
          {{ unreadCount > 9 ? '9+' : unreadCount }}
        </span>
      </button>

      <!-- 用户头像和下拉菜单 -->
      <div class="relative" ref="userMenuRef">
        <button
          @click="toggleUserMenu"
          @mouseenter="showUserMenu = true"
          class="flex items-center gap-2 p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          <div class="w-8 h-8 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700">
            <img
              v-if="user?.avatar"
              :src="user.avatar"
              :alt="user.username"
              class="w-full h-full object-cover"
            />
            <div v-else class="w-full h-full flex items-center justify-center">
              <Icon name="user" class="w-5 h-5 text-gray-400" />
            </div>
          </div>
          
          <span v-if="user" class="text-sm font-medium text-gray-700 dark:text-gray-300 hidden lg:block">
            {{ user.username }}
          </span>
          
          <Icon 
            name="chevron-down" 
            :class="[
              'w-4 h-4 text-gray-400 transition-transform duration-200 hidden lg:block',
              showUserMenu ? 'rotate-180' : ''
            ]" 
          />
        </button>

        <!-- 用户下拉菜单 -->
        <Transition
          enter-active-class="transition ease-out duration-200"
          enter-from-class="opacity-0 scale-95"
          enter-to-class="opacity-100 scale-100"
          leave-active-class="transition ease-in duration-150"
          leave-from-class="opacity-100 scale-100"
          leave-to-class="opacity-0 scale-95"
        >
          <div
            v-if="showUserMenu"
            @mouseleave="showUserMenu = false"
            class="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-50"
          >
            <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
              <p class="text-sm font-medium text-gray-900 dark:text-white">
                {{ user?.username }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ user?.email }}
              </p>
            </div>
            
            <div class="py-1">
              <UserMenuItem
                v-for="item in userMenuItems"
                :key="item.name"
                :item="item"
                @click="handleMenuClick(item)"
              />
            </div>
            
            <div class="border-t border-gray-200 dark:border-gray-700 py-1">
              <UserMenuItem
                :item="{ name: '退出登录', icon: 'arrow-right-on-rectangle', action: 'logout' }"
                @click="handleLogout"
              />
            </div>
          </div>
        </Transition>
      </div>
    </div>

    <!-- 移动端搜索模态框 -->
    <Modal
      v-if="showMobileSearch"
      @close="showMobileSearch = false"
      title="搜索"
    >
      <div class="p-4">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索音乐、歌单、用户..."
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          @keydown.enter="handleMobileSearch"
          autofocus
        />
      </div>
    </Modal>
  </header>
</template>

<script setup lang="ts">
// 注入的方法
const toggleMobileSidebar = inject('toggleMobileSidebar', () => {})

// 响应式状态
const showUserMenu = ref(false)
const showMobileSearch = ref(false)
const showNotifications = ref(false)
const searchQuery = ref('')
const unreadCount = ref(0)

// Composables
const { user, logout } = useAuth()
const { toggleTheme, isDark } = useTheme()

// 用户菜单引用
const userMenuRef = ref<HTMLElement>()

// 主题名称
const themeName = computed(() => {
  return isDark.value ? '切换到浅色模式' : '切换到深色模式'
})

// 用户菜单项
const userMenuItems = [
  { name: '个人资料', icon: 'user', route: '/profile' },
  { name: '我的歌单', icon: 'queue-list', route: '/playlists' },
  { name: '收藏', icon: 'heart', route: '/favorites' },
  { name: '设置', icon: 'cog-6-tooth', route: '/settings' }
]

// 切换用户菜单
const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

// 处理菜单点击
const handleMenuClick = (item: any) => {
  showUserMenu.value = false
  if (item.route) {
    navigateTo(item.route)
  }
}

// 处理搜索
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    navigateTo(`/search?q=${encodeURIComponent(searchQuery.value)}`)
  }
}

// 处理移动端搜索
const handleMobileSearch = () => {
  showMobileSearch.value = false
  handleSearch()
}

// 处理退出登录
const handleLogout = async () => {
  showUserMenu.value = false
  await logout()
}

// 点击外部关闭菜单
const handleClickOutside = (event: Event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    showUserMenu.value = false
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
