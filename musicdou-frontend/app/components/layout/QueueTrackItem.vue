<template>
  <div
    :class="[
      'flex items-center gap-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer group transition-colors',
      isCurrent ? 'bg-primary-50 dark:bg-primary-900/20' : ''
    ]"
    @click="$emit('play')"
  >
    <!-- 拖拽手柄 -->
    <div v-if="!collapsed" class="drag-handle opacity-0 group-hover:opacity-100 transition-opacity cursor-move">
      <Icon name="bars-3" class="w-3 h-3 text-gray-400" />
    </div>
    
    <!-- 序号或播放状态 -->
    <div class="w-6 flex items-center justify-center flex-shrink-0">
      <div v-if="isCurrent && isPlaying" class="flex items-center gap-0.5">
        <div class="w-0.5 h-2 bg-primary-500 rounded-full animate-pulse"></div>
        <div class="w-0.5 h-3 bg-primary-500 rounded-full animate-pulse" style="animation-delay: 0.1s"></div>
        <div class="w-0.5 h-2 bg-primary-500 rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
      </div>
      <Icon v-else-if="isCurrent" name="pause" class="w-4 h-4 text-primary-500" />
      <span v-else class="text-xs text-gray-500 dark:text-gray-400">
        {{ index + 1 }}
      </span>
    </div>
    
    <!-- 歌曲封面 -->
    <div v-if="!collapsed" class="w-10 h-10 rounded overflow-hidden bg-gray-200 dark:bg-gray-700 flex-shrink-0">
      <img
        v-if="track.coverUrl"
        :src="track.coverUrl"
        :alt="track.title"
        class="w-full h-full object-cover"
      />
      <div v-else class="w-full h-full flex items-center justify-center">
        <Icon name="musical-note" class="w-4 h-4 text-gray-400" />
      </div>
    </div>
    
    <!-- 歌曲信息 -->
    <div v-if="!collapsed" class="flex-1 min-w-0">
      <h4 
        :class="[
          'text-sm font-medium truncate',
          isCurrent ? 'text-primary-600 dark:text-primary-400' : 'text-gray-900 dark:text-white'
        ]"
      >
        {{ track.title }}
      </h4>
      <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
        {{ track.artist }}
      </p>
    </div>
    
    <!-- 时长和操作 -->
    <div v-if="!collapsed" class="flex items-center gap-2 flex-shrink-0">
      <span class="text-xs text-gray-500 dark:text-gray-400">
        {{ formatDuration(track.duration) }}
      </span>
      
      <button
        @click.stop="$emit('remove')"
        class="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-all"
        title="从队列中移除"
      >
        <Icon name="x-mark" class="w-3 h-3 text-gray-500 dark:text-gray-400" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Music } from '~/types'

interface Props {
  track: Music
  index: number
  isCurrent: boolean
  isPlaying: boolean
  collapsed: boolean
}

interface Emits {
  (e: 'play'): void
  (e: 'remove'): void
}

defineProps<Props>()
defineEmits<Emits>()

// 格式化时长
const formatDuration = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0:00'
  
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}
</script>
