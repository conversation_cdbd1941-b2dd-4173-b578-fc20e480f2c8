<template>
  <button
    @click="$emit('click')"
    class="w-full flex items-center gap-3 px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
  >
    <Icon 
      :name="item.icon" 
      class="w-4 h-4 text-gray-500 dark:text-gray-400" 
    />
    <span class="text-sm text-gray-700 dark:text-gray-300">
      {{ item.name }}
    </span>
  </button>
</template>

<script setup lang="ts">
interface MenuItem {
  name: string
  icon: string
  route?: string
  action?: string
}

interface Props {
  item: MenuItem
}

interface Emits {
  (e: 'click'): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>
