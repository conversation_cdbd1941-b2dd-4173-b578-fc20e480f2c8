<template>
  <button
    @click="$emit('click')"
    :class="[
      'w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-all duration-200',
      'hover:bg-gray-100 dark:hover:bg-gray-700 group',
      isActive ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' : 'text-gray-700 dark:text-gray-300'
    ]"
  >
    <!-- 歌单封面 -->
    <div class="w-8 h-8 rounded overflow-hidden flex-shrink-0 bg-gray-200 dark:bg-gray-700">
      <img
        v-if="playlist.coverUrl"
        :src="playlist.coverUrl"
        :alt="playlist.name"
        class="w-full h-full object-cover"
      />
      <div v-else class="w-full h-full flex items-center justify-center">
        <Icon 
          name="musical-note" 
          class="w-4 h-4 text-gray-400 dark:text-gray-500" 
        />
      </div>
    </div>
    
    <!-- 歌单信息 -->
    <div v-if="!collapsed" class="flex-1 min-w-0">
      <div class="flex items-center justify-between">
        <span 
          :class="[
            'text-sm font-medium truncate',
            isActive ? 'text-primary-600 dark:text-primary-400' : 'text-gray-700 dark:text-gray-300'
          ]"
          :title="playlist.name"
        >
          {{ playlist.name }}
        </span>
        
        <!-- 歌曲数量 -->
        <span class="text-xs text-gray-500 dark:text-gray-400 ml-2 flex-shrink-0">
          {{ playlist.songCount || 0 }}
        </span>
      </div>
      
      <!-- 创建者信息 -->
      <div class="flex items-center gap-1 mt-1">
        <span class="text-xs text-gray-500 dark:text-gray-400 truncate">
          {{ playlist.creator?.username || '未知' }}
        </span>
        
        <!-- 私有标识 -->
        <Icon 
          v-if="!playlist.isPublic" 
          name="lock-closed" 
          class="w-3 h-3 text-gray-400 dark:text-gray-500 flex-shrink-0" 
        />
      </div>
    </div>
    
    <!-- 播放按钮 (悬停时显示) -->
    <button
      v-if="!collapsed"
      @click.stop="playPlaylist"
      class="opacity-0 group-hover:opacity-100 p-1 rounded-full bg-primary-500 hover:bg-primary-600 text-white transition-all duration-200 flex-shrink-0"
      title="播放歌单"
    >
      <Icon name="play" class="w-3 h-3" />
    </button>
  </button>
</template>

<script setup lang="ts">
import type { Playlist } from '~/types'

interface Props {
  playlist: Playlist
  collapsed: boolean
}

interface Emits {
  (e: 'click'): void
}

const props = defineProps<Props>()
defineEmits<Emits>()

// 当前路由
const route = useRoute()

// 播放器状态
const playerStore = usePlayerStore()

// 判断是否为当前激活的歌单
const isActive = computed(() => {
  return route.path === `/playlists/${props.playlist.id}`
})

// 播放歌单
const playPlaylist = () => {
  if (props.playlist.songs?.length) {
    playerStore.setQueue(props.playlist.songs, 0)
  }
}
</script>
