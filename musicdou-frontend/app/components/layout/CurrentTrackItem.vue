<template>
  <div class="flex items-center gap-3">
    <!-- 封面 -->
    <div class="w-12 h-12 rounded-lg overflow-hidden bg-gray-200 dark:bg-gray-700 flex-shrink-0">
      <img
        v-if="track.coverUrl"
        :src="track.coverUrl"
        :alt="track.title"
        class="w-full h-full object-cover"
      />
      <div v-else class="w-full h-full flex items-center justify-center">
        <Icon name="musical-note" class="w-6 h-6 text-gray-400" />
      </div>
    </div>
    
    <!-- 歌曲信息 -->
    <div class="flex-1 min-w-0">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate">
        {{ track.title }}
      </h4>
      <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
        {{ track.artist }}
      </p>
      <p v-if="track.album" class="text-xs text-gray-400 dark:text-gray-500 truncate">
        {{ track.album }}
      </p>
    </div>
    
    <!-- 播放状态指示器 -->
    <div class="flex-shrink-0">
      <div class="w-4 h-4 flex items-center justify-center">
        <div v-if="isPlaying" class="flex items-center gap-0.5">
          <div class="w-0.5 h-3 bg-primary-500 rounded-full animate-pulse"></div>
          <div class="w-0.5 h-2 bg-primary-500 rounded-full animate-pulse" style="animation-delay: 0.1s"></div>
          <div class="w-0.5 h-4 bg-primary-500 rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
        </div>
        <Icon v-else name="pause" class="w-4 h-4 text-gray-400" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Music } from '~/types'

interface Props {
  track: Music
  isPlaying?: boolean
}

withDefaults(defineProps<Props>(), {
  isPlaying: false
})
</script>
