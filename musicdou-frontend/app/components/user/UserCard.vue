<template>
  <UiCard 
    :variant="variant"
    :clickable="clickable"
    class="user-card"
    @click="handleCardClick"
  >
    <div class="flex items-center space-x-4 p-4">
      <!-- 用户头像 -->
      <div class="flex-shrink-0">
        <div 
          class="w-12 h-12 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white font-semibold text-lg overflow-hidden"
          :class="{ 'w-16 h-16 text-xl': size === 'lg', 'w-10 h-10 text-base': size === 'sm' }"
        >
          <img 
            v-if="user.avatar" 
            :src="user.avatar" 
            :alt="user.username"
            class="w-full h-full object-cover"
            @error="handleImageError"
          />
          <span v-else>
            {{ user.username.charAt(0).toUpperCase() }}
          </span>
        </div>
      </div>

      <!-- 用户信息 -->
      <div class="flex-1 min-w-0">
        <div class="flex items-center space-x-2">
          <h3 
            class="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate"
            :class="{ 'text-base': size === 'lg' }"
          >
            {{ user.username }}
          </h3>
          
          <!-- VIP 标识 -->
          <span 
            v-if="user.userGroup === 'vip'"
            class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
          >
            VIP
          </span>
          
          <!-- 管理员标识 -->
          <span 
            v-if="user.userGroup === 'admin'"
            class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
          >
            管理员
          </span>
        </div>

        <!-- 用户简介 -->
        <p 
          v-if="user.bio && showBio"
          class="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2"
        >
          {{ user.bio }}
        </p>

        <!-- 统计信息 -->
        <div v-if="showStats" class="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span class="flex items-center space-x-1">
            <UiIcon name="users" size="sm" />
            <span>{{ formatNumber(user.followersCount) }} 粉丝</span>
          </span>
          <span class="flex items-center space-x-1">
            <UiIcon name="heart" size="sm" />
            <span>{{ formatNumber(user.followingCount) }} 关注</span>
          </span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div v-if="showActions" class="flex-shrink-0">
        <SocialFollowButton
          :user-id="user.id"
          :size="size === 'lg' ? 'md' : 'sm'"
          :show-text="size !== 'sm'"
          @followed="handleFollowed"
          @unfollowed="handleUnfollowed"
        />
      </div>
    </div>

    <!-- 额外内容插槽 -->
    <div v-if="$slots.extra" class="px-4 pb-4">
      <slot name="extra" :user="user" />
    </div>
  </UiCard>
</template>

<script setup lang="ts">
import type { User } from '~/types'

interface Props {
  user: User
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'bordered' | 'shadow' | 'ghost'
  clickable?: boolean
  showBio?: boolean
  showStats?: boolean
  showActions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  variant: 'default',
  clickable: true,
  showBio: true,
  showStats: true,
  showActions: true
})

const emit = defineEmits<{
  click: [user: User]
  followed: [userId: string]
  unfollowed: [userId: string]
}>()

const { formatNumber } = useUtils()

// 处理卡片点击
const handleCardClick = () => {
  if (props.clickable) {
    emit('click', props.user)
    navigateTo(`/users/${props.user.id}`)
  }
}

// 处理头像加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

// 处理关注事件
const handleFollowed = (userId: string) => {
  emit('followed', userId)
}

const handleUnfollowed = (userId: string) => {
  emit('unfollowed', userId)
}
</script>

<style scoped>
.user-card {
  @apply transition-all duration-200;
}

.user-card:hover {
  @apply transform translate-y-[-2px] shadow-lg;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
