<template>
  <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
    <!-- 自动完成建议 -->
    <div v-if="suggestions.length > 0" class="py-2">
      <div class="px-3 py-1 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
        搜索建议
      </div>
      <button
        v-for="(suggestion, index) in suggestions"
        :key="`suggestion-${suggestion.id}`"
        @click="selectSuggestion(suggestion.text)"
        :class="[
          'w-full px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150',
          { 'bg-gray-50 dark:bg-gray-700': selectedIndex === index }
        ]"
        class="flex items-center justify-between group"
      >
        <div class="flex items-center space-x-3">
          <Icon 
            :name="getSuggestionIcon(suggestion.type)" 
            class="h-4 w-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300" 
          />
          <div class="flex-1">
            <span class="text-gray-900 dark:text-gray-100">{{ suggestion.text }}</span>
            <span 
              v-if="suggestion.type !== 'query'" 
              class="ml-2 text-xs text-gray-500 dark:text-gray-400 capitalize"
            >
              {{ getSuggestionTypeLabel(suggestion.type) }}
            </span>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <span 
            v-if="suggestion.count" 
            class="text-xs text-gray-500 dark:text-gray-400"
          >
            {{ formatCount(suggestion.count) }}
          </span>
          <Icon 
            name="ArrowTopRightOnSquareIcon" 
            class="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-150" 
          />
        </div>
      </button>
    </div>

    <!-- 搜索历史 -->
    <div v-if="history.length > 0 && showHistory" class="py-2 border-t border-gray-200 dark:border-gray-700">
      <div class="px-3 py-1 flex items-center justify-between">
        <span class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
          搜索历史
        </span>
        <button
          @click="clearHistory"
          class="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300
                 transition-colors duration-150"
        >
          清除
        </button>
      </div>
      <button
        v-for="(item, index) in displayHistory"
        :key="`history-${item.id}`"
        @click="selectSuggestion(item.query)"
        :class="[
          'w-full px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150',
          { 'bg-gray-50 dark:bg-gray-700': selectedIndex === suggestions.length + index }
        ]"
        class="flex items-center justify-between group"
      >
        <div class="flex items-center space-x-3">
          <Icon name="ClockIcon" class="h-4 w-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300" />
          <div class="flex-1">
            <span class="text-gray-900 dark:text-gray-100">{{ item.query }}</span>
            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {{ formatTime(item.timestamp) }} · {{ formatNumber(item.resultCount) }} 个结果
            </div>
          </div>
        </div>
        <button
          @click.stop="removeHistoryItem(item.id)"
          class="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 
                 transition-all duration-150 rounded"
        >
          <Icon name="XMarkIcon" class="h-3 w-3" />
        </button>
      </button>
      
      <!-- 显示更多历史记录 -->
      <div v-if="history.length > maxHistoryDisplay" class="px-3 py-2">
        <button
          @click="showAllHistory = !showAllHistory"
          class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300
                 transition-colors duration-150"
        >
          {{ showAllHistory ? '收起' : `查看全部 ${history.length} 条历史记录` }}
        </button>
      </div>
    </div>

    <!-- 热门搜索 -->
    <div v-if="hotSearches.length > 0 && showHotSearches" class="py-2 border-t border-gray-200 dark:border-gray-700">
      <div class="px-3 py-1 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
        热门搜索
      </div>
      <div class="px-3 py-2">
        <div class="flex flex-wrap gap-2">
          <button
            v-for="(hotSearch, index) in hotSearches.slice(0, maxHotSearches)"
            :key="`hot-${index}`"
            @click="selectSuggestion(hotSearch)"
            :class="[
              'px-3 py-1 text-sm rounded-full border transition-all duration-200 hover:scale-105',
              index < 3 
                ? 'bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900 dark:to-red-900 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-700'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
            ]"
          >
            <Icon 
              v-if="index < 3" 
              name="FireIcon" 
              class="w-3 h-3 inline mr-1" 
            />
            {{ hotSearch }}
          </button>
        </div>
      </div>
    </div>

    <!-- 相关搜索 -->
    <div v-if="relatedSearches.length > 0" class="py-2 border-t border-gray-200 dark:border-gray-700">
      <div class="px-3 py-1 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
        相关搜索
      </div>
      <button
        v-for="related in relatedSearches"
        :key="`related-${related}`"
        @click="selectSuggestion(related)"
        class="w-full px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 
               transition-colors duration-150 flex items-center space-x-3 group"
      >
        <Icon name="ArrowTrendingUpIcon" class="h-4 w-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300" />
        <span class="text-gray-900 dark:text-gray-100">{{ related }}</span>
      </button>
    </div>

    <!-- 空状态 -->
    <div v-if="isEmpty" class="py-8 text-center">
      <Icon name="MagnifyingGlassIcon" class="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
      <p class="text-sm text-gray-500 dark:text-gray-400">
        暂无搜索建议
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { SearchSuggestion, SearchHistory } from '~/types'

interface Props {
  suggestions?: SearchSuggestion[]
  history?: SearchHistory[]
  hotSearches?: string[]
  relatedSearches?: string[]
  selectedIndex?: number
  showHistory?: boolean
  showHotSearches?: boolean
  maxHistoryDisplay?: number
  maxHotSearches?: number
}

interface Emits {
  (e: 'select', suggestion: string): void
  (e: 'clear-history'): void
  (e: 'remove-history', id: string): void
}

const props = withDefaults(defineProps<Props>(), {
  suggestions: () => [],
  history: () => [],
  hotSearches: () => [],
  relatedSearches: () => [],
  selectedIndex: -1,
  showHistory: true,
  showHotSearches: true,
  maxHistoryDisplay: 5,
  maxHotSearches: 8
})

const emit = defineEmits<Emits>()

// 响应式数据
const showAllHistory = ref(false)

// 计算属性
const displayHistory = computed(() => {
  if (showAllHistory.value) {
    return props.history
  }
  return props.history.slice(0, props.maxHistoryDisplay)
})

const isEmpty = computed(() => {
  return props.suggestions.length === 0 && 
         props.history.length === 0 && 
         props.hotSearches.length === 0 && 
         props.relatedSearches.length === 0
})

// 方法
const selectSuggestion = (suggestion: string) => {
  emit('select', suggestion)
}

const clearHistory = () => {
  emit('clear-history')
}

const removeHistoryItem = (id: string) => {
  emit('remove-history', id)
}

const getSuggestionIcon = (type: string) => {
  switch (type) {
    case 'artist': return 'UserIcon'
    case 'album': return 'MusicalNoteIcon'
    case 'genre': return 'TagIcon'
    default: return 'MagnifyingGlassIcon'
  }
}

const getSuggestionTypeLabel = (type: string) => {
  switch (type) {
    case 'artist': return '艺术家'
    case 'album': return '专辑'
    case 'genre': return '风格'
    default: return '搜索'
  }
}

const formatCount = (count: number) => {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`
  }
  return count.toString()
}

const formatNumber = (num: number) => {
  return formatCount(num)
}

const formatTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`
  return date.toLocaleDateString()
}
</script>

<style scoped>
/* 热门搜索渐变效果 */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

/* 悬停缩放效果 */
.hover\:scale-105:hover {
  transform: scale(1.05);
}

/* 平滑过渡 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-700 rounded-full;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* 组标签动画 */
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:text-gray-600 {
  color: rgb(75 85 99);
}

.dark .group:hover .group-hover\:text-gray-300 {
  color: rgb(209 213 219);
}
</style>
