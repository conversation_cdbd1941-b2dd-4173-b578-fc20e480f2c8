<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 
              hover:shadow-md dark:hover:shadow-gray-900/20 transition-all duration-200 p-4">
    <!-- 音乐结果 -->
    <div v-if="type === 'music'" class="flex items-center space-x-4">
      <!-- 封面图片 -->
      <div class="relative flex-shrink-0">
        <img
          :src="item.coverUrl || '/default-music-cover.jpg'"
          :alt="item.title"
          class="w-16 h-16 rounded-lg object-cover"
          @error="handleImageError"
        />
        <button
          @click="$emit('play', item)"
          class="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center
                 opacity-0 hover:opacity-100 transition-opacity duration-200"
        >
          <Icon name="PlayIcon" class="w-6 h-6 text-white" />
        </button>
      </div>
      
      <!-- 音乐信息 -->
      <div class="flex-1 min-w-0">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 truncate">
          <span v-html="highlightText(item.title, query)"></span>
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 truncate">
          <span v-html="highlightText(item.artist, query)"></span>
          <span v-if="item.album"> · {{ item.album }}</span>
        </p>
        <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span>{{ formatDuration(item.duration) }}</span>
          <span>{{ formatNumber(item.playCount) }} 播放</span>
          <span v-if="item.genre" class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full">
            {{ item.genre }}
          </span>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="flex items-center space-x-2">
        <button
          @click="$emit('like', item)"
          :class="[
            'p-2 rounded-full transition-colors duration-200',
            item.isLiked
              ? 'text-red-500 hover:text-red-600'
              : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
          ]"
        >
          <Icon :name="item.isLiked ? 'HeartIconSolid' : 'HeartIcon'" class="w-5 h-5" />
        </button>
        <button
          @click="$emit('add-to-playlist', item)"
          class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 
                 rounded-full transition-colors duration-200"
        >
          <Icon name="PlusIcon" class="w-5 h-5" />
        </button>
      </div>
    </div>

    <!-- 歌单结果 -->
    <div v-else-if="type === 'playlist'" class="flex items-center space-x-4">
      <!-- 歌单封面 -->
      <div class="relative flex-shrink-0">
        <img
          :src="item.coverUrl || '/default-playlist-cover.jpg'"
          :alt="item.name"
          class="w-16 h-16 rounded-lg object-cover"
          @error="handleImageError"
        />
        <div class="absolute inset-0 bg-black bg-opacity-30 rounded-lg flex items-center justify-center">
          <Icon name="QueueListIcon" class="w-6 h-6 text-white" />
        </div>
      </div>
      
      <!-- 歌单信息 -->
      <div class="flex-1 min-w-0">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 truncate">
          <span v-html="highlightText(item.name, query)"></span>
        </h3>
        <p v-if="item.description" class="text-sm text-gray-600 dark:text-gray-400 truncate">
          <span v-html="highlightText(item.description, query)"></span>
        </p>
        <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span>{{ item.songCount }} 首歌曲</span>
          <span>{{ formatDuration(item.duration) }}</span>
          <span>{{ formatNumber(item.playCount) }} 播放</span>
          <span v-if="item.user">by {{ item.user.username }}</span>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="flex items-center space-x-2">
        <button
          @click="$emit('like', item)"
          :class="[
            'p-2 rounded-full transition-colors duration-200',
            item.isLiked
              ? 'text-red-500 hover:text-red-600'
              : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
          ]"
        >
          <Icon :name="item.isLiked ? 'HeartIconSolid' : 'HeartIcon'" class="w-5 h-5" />
        </button>
        <button
          @click="$emit('play', item)"
          class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 
                 rounded-full transition-colors duration-200"
        >
          <Icon name="PlayIcon" class="w-5 h-5" />
        </button>
      </div>
    </div>

    <!-- 用户结果 -->
    <div v-else-if="type === 'user'" class="flex items-center space-x-4">
      <!-- 用户头像 -->
      <div class="flex-shrink-0">
        <img
          :src="item.avatar || '/default-avatar.jpg'"
          :alt="item.username"
          class="w-16 h-16 rounded-full object-cover"
          @error="handleImageError"
        />
      </div>
      
      <!-- 用户信息 -->
      <div class="flex-1 min-w-0">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 truncate">
          <span v-html="highlightText(item.username, query)"></span>
        </h3>
        <p v-if="item.bio" class="text-sm text-gray-600 dark:text-gray-400 truncate">
          <span v-html="highlightText(item.bio, query)"></span>
        </p>
        <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span>{{ formatNumber(item.followersCount) }} 粉丝</span>
          <span>{{ formatNumber(item.followingCount) }} 关注</span>
          <span v-if="item.userGroup !== 'normal'" 
                :class="[
                  'px-2 py-1 rounded-full text-xs font-medium',
                  item.userGroup === 'vip' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                  item.userGroup === 'admin' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' : ''
                ]">
            {{ item.userGroup === 'vip' ? 'VIP' : '管理员' }}
          </span>
        </div>
      </div>
      
      <!-- 关注按钮 -->
      <div class="flex-shrink-0">
        <FollowButton :user="item" @follow="$emit('follow', item)" @unfollow="$emit('unfollow', item)" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Music, Playlist, User } from '~/types'

interface Props {
  item: Music | Playlist | User
  type: 'music' | 'playlist' | 'user'
  query?: string
}

interface Emits {
  (e: 'play', item: Music | Playlist): void
  (e: 'like', item: Music | Playlist): void
  (e: 'add-to-playlist', item: Music): void
  (e: 'follow', item: User): void
  (e: 'unfollow', item: User): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 高亮搜索关键词
const highlightText = (text: string, query?: string) => {
  if (!query || !text) return text
  
  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>')
}

// 格式化时长
const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  if (props.type === 'music') {
    img.src = '/default-music-cover.jpg'
  } else if (props.type === 'playlist') {
    img.src = '/default-playlist-cover.jpg'
  } else if (props.type === 'user') {
    img.src = '/default-avatar.jpg'
  }
}
</script>

<style scoped>
/* 高亮标记样式 */
:deep(mark) {
  @apply bg-yellow-200 dark:bg-yellow-800 px-1 rounded;
}

/* 悬停效果 */
.hover\:shadow-md:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dark .hover\:shadow-md:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

/* 播放按钮悬停效果 */
.absolute button {
  backdrop-filter: blur(4px);
}

/* 用户组标签动画 */
.px-2.py-1 {
  transition: all 0.2s ease-in-out;
}

.px-2.py-1:hover {
  transform: scale(1.05);
}
</style>
