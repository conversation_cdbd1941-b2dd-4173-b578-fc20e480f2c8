<template>
  <div class="space-y-6">
    <!-- 搜索结果头部 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
          搜索结果
        </h2>
        <span v-if="totalResults > 0" class="text-sm text-gray-500 dark:text-gray-400">
          找到 {{ formatNumber(totalResults) }} 个结果
        </span>
      </div>
      
      <!-- 排序选择器 -->
      <div class="flex items-center space-x-2">
        <label class="text-sm text-gray-600 dark:text-gray-400">排序:</label>
        <select
          v-model="currentSortBy"
          @change="handleSortChange"
          class="text-sm border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1
                 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        >
          <option value="relevance">相关性</option>
          <option value="date">时间</option>
          <option value="popularity">热度</option>
          <option value="rating">评分</option>
        </select>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading && !results" class="flex justify-center py-12">
      <Loading size="lg" />
    </div>

    <!-- 搜索结果内容 -->
    <div v-else-if="results && hasResults">
      <!-- 结果类型标签 -->
      <div class="flex space-x-1 mb-6 border-b border-gray-200 dark:border-gray-700">
        <button
          v-for="tab in resultTabs"
          :key="tab.type"
          @click="activeTab = tab.type"
          :class="[
            'px-4 py-2 text-sm font-medium border-b-2 transition-colors duration-200',
            activeTab === tab.type
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
          ]"
        >
          {{ tab.label }}
          <span v-if="tab.count > 0" class="ml-1 text-xs">
            ({{ formatNumber(tab.count) }})
          </span>
        </button>
      </div>

      <!-- 音乐结果 -->
      <div v-if="activeTab === 'music' && results.music.length > 0" class="space-y-4">
        <MusicCard
          v-for="music in results.music"
          :key="music.id"
          :music="music"
          :show-actions="true"
          @play="handlePlayMusic"
          @like="handleLikeMusic"
          @add-to-playlist="handleAddToPlaylist"
        />
      </div>

      <!-- 歌单结果 -->
      <div v-if="activeTab === 'playlists' && results.playlists.length > 0">
        <PlaylistGrid :playlists="results.playlists" />
      </div>

      <!-- 用户结果 -->
      <div v-if="activeTab === 'users' && results.users.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <UserCard
          v-for="user in results.users"
          :key="user.id"
          :user="user"
          :show-follow-button="true"
        />
      </div>

      <!-- 全部结果 -->
      <div v-if="activeTab === 'all'" class="space-y-8">
        <!-- 音乐部分 -->
        <div v-if="results.music.length > 0">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">音乐</h3>
            <button
              v-if="results.music.length > 3"
              @click="activeTab = 'music'"
              class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
            >
              查看全部 {{ formatNumber(results.music.length) }} 首
            </button>
          </div>
          <div class="space-y-3">
            <MusicCard
              v-for="music in results.music.slice(0, 3)"
              :key="music.id"
              :music="music"
              :show-actions="true"
              @play="handlePlayMusic"
              @like="handleLikeMusic"
              @add-to-playlist="handleAddToPlaylist"
            />
          </div>
        </div>

        <!-- 歌单部分 -->
        <div v-if="results.playlists.length > 0">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">歌单</h3>
            <button
              v-if="results.playlists.length > 6"
              @click="activeTab = 'playlists'"
              class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
            >
              查看全部 {{ formatNumber(results.playlists.length) }} 个
            </button>
          </div>
          <PlaylistGrid :playlists="results.playlists.slice(0, 6)" />
        </div>

        <!-- 用户部分 -->
        <div v-if="results.users.length > 0">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">用户</h3>
            <button
              v-if="results.users.length > 6"
              @click="activeTab = 'users'"
              class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
            >
              查看全部 {{ formatNumber(results.users.length) }} 个
            </button>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <UserCard
              v-for="user in results.users.slice(0, 6)"
              :key="user.id"
              :user="user"
              :show-follow-button="true"
            />
          </div>
        </div>
      </div>

      <!-- 加载更多按钮 -->
      <div v-if="hasMore && activeTab !== 'all'" class="flex justify-center mt-8">
        <Button
          @click="loadMore"
          :loading="isLoadingMore"
          variant="outline"
          size="lg"
        >
          <Icon name="ArrowDownIcon" class="w-4 h-4 mr-2" />
          加载更多
        </Button>
      </div>

      <!-- 无限滚动加载指示器 -->
      <div
        v-if="isLoadingMore"
        class="flex justify-center py-8"
      >
        <Loading size="md" />
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!isLoading" class="text-center py-12">
      <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
        <Icon name="MagnifyingGlassIcon" class="w-12 h-12 text-gray-400" />
      </div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        {{ query ? '未找到相关结果' : '开始搜索' }}
      </h3>
      <p class="text-gray-500 dark:text-gray-400 mb-6">
        {{ query 
          ? `没有找到与 "${query}" 相关的内容，请尝试其他关键词` 
          : '输入关键词搜索音乐、歌单或用户'
        }}
      </p>
      
      <!-- 搜索建议 -->
      <div v-if="query && suggestions.length > 0" class="max-w-md mx-auto">
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">您可能想搜索:</p>
        <div class="flex flex-wrap gap-2 justify-center">
          <button
            v-for="suggestion in suggestions.slice(0, 5)"
            :key="suggestion"
            @click="handleSuggestionClick(suggestion)"
            class="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 
                   rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200"
          >
            {{ suggestion }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { SearchResult, Music, Playlist, User } from '~/types'

interface Props {
  results?: SearchResult | null
  query?: string
  isLoading?: boolean
  hasMore?: boolean
  totalResults?: number
  sortBy?: 'relevance' | 'date' | 'popularity' | 'rating'
}

interface Emits {
  (e: 'load-more'): void
  (e: 'sort-change', sortBy: string): void
  (e: 'suggestion-click', suggestion: string): void
  (e: 'play-music', music: Music): void
  (e: 'like-music', music: Music): void
  (e: 'add-to-playlist', music: Music): void
}

const props = withDefaults(defineProps<Props>(), {
  sortBy: 'relevance'
})

const emit = defineEmits<Emits>()

// 响应式数据
const activeTab = ref<'all' | 'music' | 'playlists' | 'users'>('all')
const currentSortBy = ref(props.sortBy)
const isLoadingMore = ref(false)
const observer = ref<IntersectionObserver>()

// 搜索建议（空状态时显示）
const suggestions = ref(['流行音乐', '经典老歌', '轻音乐', '摇滚', '民谣'])

// 计算属性
const hasResults = computed(() => {
  if (!props.results) return false
  const { music, playlists, users } = props.results
  return music.length > 0 || playlists.length > 0 || users.length > 0
})

const resultTabs = computed(() => {
  if (!props.results) return []
  
  const tabs = [
    { type: 'all', label: '全部', count: props.totalResults || 0 }
  ]
  
  if (props.results.music.length > 0) {
    tabs.push({ type: 'music', label: '音乐', count: props.results.music.length })
  }
  
  if (props.results.playlists.length > 0) {
    tabs.push({ type: 'playlists', label: '歌单', count: props.results.playlists.length })
  }
  
  if (props.results.users.length > 0) {
    tabs.push({ type: 'users', label: '用户', count: props.results.users.length })
  }
  
  return tabs
})

// 监听排序变化
watch(() => props.sortBy, (newValue) => {
  currentSortBy.value = newValue
})

// 处理排序变化
const handleSortChange = () => {
  emit('sort-change', currentSortBy.value)
}

// 加载更多
const loadMore = () => {
  if (!props.hasMore || isLoadingMore.value) return
  isLoadingMore.value = true
  emit('load-more')
}

// 处理建议点击
const handleSuggestionClick = (suggestion: string) => {
  emit('suggestion-click', suggestion)
}

// 处理音乐播放
const handlePlayMusic = (music: Music) => {
  emit('play-music', music)
}

// 处理音乐点赞
const handleLikeMusic = (music: Music) => {
  emit('like-music', music)
}

// 处理添加到歌单
const handleAddToPlaylist = (music: Music) => {
  emit('add-to-playlist', music)
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// 无限滚动
const setupInfiniteScroll = () => {
  if (!process.client) return
  
  const loadMoreTrigger = document.createElement('div')
  loadMoreTrigger.style.height = '1px'
  
  observer.value = new IntersectionObserver(
    (entries) => {
      if (entries[0].isIntersecting && props.hasMore && !isLoadingMore.value) {
        loadMore()
      }
    },
    { threshold: 0.1 }
  )
  
  // 在结果容器底部添加触发器
  nextTick(() => {
    const container = document.querySelector('.search-results-container')
    if (container) {
      container.appendChild(loadMoreTrigger)
      observer.value?.observe(loadMoreTrigger)
    }
  })
}

// 监听加载更多状态变化
watch(() => props.hasMore, () => {
  isLoadingMore.value = false
})

// 组件挂载时设置无限滚动
onMounted(() => {
  setupInfiniteScroll()
})

// 组件卸载时清理
onUnmounted(() => {
  observer.value?.disconnect()
})

// 重置标签页当结果变化时
watch(() => props.results, () => {
  activeTab.value = 'all'
}, { deep: true })
</script>

<style scoped>
.search-results-container {
  position: relative;
}

/* 平滑滚动 */
.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

/* 标签页过渡效果 */
.border-b-2 {
  transition: border-color 0.2s ease-in-out;
}

/* 加载状态动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-4 > *,
.space-y-3 > * {
  animation: fadeIn 0.3s ease-out;
}
</style>
