<template>
  <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
        高级搜索
      </h3>
      <div class="flex items-center space-x-2">
        <button
          @click="resetFilters"
          class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300
                 transition-colors duration-200"
        >
          重置
        </button>
        <button
          @click="$emit('close')"
          class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 
                 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
        >
          <Icon name="XMarkIcon" class="w-5 h-5" />
        </button>
      </div>
    </div>

    <div class="space-y-6">
      <!-- 智能查询构建器 -->
      <div>
        <label class="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
          智能查询构建
        </label>
        <div class="space-y-2">
          <div
            v-for="(condition, index) in queryConditions"
            :key="index"
            class="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <select
              v-model="condition.field"
              class="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md
                     bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                     focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="title">标题</option>
              <option value="artist">艺术家</option>
              <option value="album">专辑</option>
              <option value="genre">风格</option>
              <option value="lyrics">歌词</option>
              <option value="tags">标签</option>
            </select>
            
            <select
              v-model="condition.operator"
              class="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md
                     bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                     focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="contains">包含</option>
              <option value="equals">等于</option>
              <option value="starts_with">开头是</option>
              <option value="ends_with">结尾是</option>
              <option value="not_contains">不包含</option>
            </select>
            
            <input
              v-model="condition.value"
              type="text"
              placeholder="输入值"
              class="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md
                     bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400
                     focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
            
            <button
              @click="removeCondition(index)"
              class="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900
                     rounded-full transition-colors duration-200"
            >
              <Icon name="TrashIcon" class="w-4 h-4" />
            </button>
          </div>
          
          <button
            @click="addCondition"
            class="flex items-center space-x-2 px-3 py-2 text-sm text-primary-600 dark:text-primary-400
                   hover:text-primary-700 dark:hover:text-primary-300 hover:bg-primary-50 dark:hover:bg-primary-900
                   rounded-lg transition-colors duration-200"
          >
            <Icon name="PlusIcon" class="w-4 h-4" />
            <span>添加条件</span>
          </button>
        </div>
      </div>

      <!-- 音频特征过滤 -->
      <div>
        <label class="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
          音频特征
        </label>
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <!-- BPM范围 -->
          <div>
            <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
              BPM (节拍)
            </label>
            <div class="flex items-center space-x-2">
              <input
                v-model.number="audioFeatures.bpmMin"
                type="number"
                min="60"
                max="200"
                placeholder="最小"
                class="w-20 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded
                       bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              />
              <span class="text-gray-500 dark:text-gray-400">-</span>
              <input
                v-model.number="audioFeatures.bpmMax"
                type="number"
                min="60"
                max="200"
                placeholder="最大"
                class="w-20 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded
                       bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              />
            </div>
          </div>

          <!-- 能量级别 -->
          <div>
            <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
              能量级别: {{ audioFeatures.energy }}%
            </label>
            <input
              v-model.number="audioFeatures.energy"
              type="range"
              min="0"
              max="100"
              class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer
                     slider-thumb"
            />
          </div>

          <!-- 舞蹈性 -->
          <div>
            <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
              舞蹈性: {{ audioFeatures.danceability }}%
            </label>
            <input
              v-model.number="audioFeatures.danceability"
              type="range"
              min="0"
              max="100"
              class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer
                     slider-thumb"
            />
          </div>

          <!-- 情感倾向 -->
          <div>
            <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
              情感倾向: {{ audioFeatures.valence }}%
            </label>
            <input
              v-model.number="audioFeatures.valence"
              type="range"
              min="0"
              max="100"
              class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer
                     slider-thumb"
            />
          </div>
        </div>
      </div>

      <!-- 发布时间 -->
      <div>
        <label class="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
          发布时间
        </label>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              开始日期
            </label>
            <input
              v-model="dateRange.start"
              type="date"
              class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md
                     bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                     focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
          <div>
            <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              结束日期
            </label>
            <input
              v-model="dateRange.end"
              type="date"
              class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md
                     bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                     focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
        </div>
      </div>

      <!-- 播放统计 -->
      <div>
        <label class="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
          播放统计
        </label>
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
              最小播放次数
            </label>
            <input
              v-model.number="playStats.minPlays"
              type="number"
              min="0"
              placeholder="0"
              class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md
                     bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                     focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
          <div>
            <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
              最小点赞数
            </label>
            <input
              v-model.number="playStats.minLikes"
              type="number"
              min="0"
              placeholder="0"
              class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md
                     bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                     focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
        </div>
      </div>

      <!-- 语言和地区 -->
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            语言
          </label>
          <select
            v-model="language"
            class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md
                   bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                   focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="">所有语言</option>
            <option value="zh">中文</option>
            <option value="en">英语</option>
            <option value="ja">日语</option>
            <option value="ko">韩语</option>
            <option value="fr">法语</option>
            <option value="es">西班牙语</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            地区
          </label>
          <select
            v-model="region"
            class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md
                   bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                   focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="">所有地区</option>
            <option value="CN">中国</option>
            <option value="US">美国</option>
            <option value="JP">日本</option>
            <option value="KR">韩国</option>
            <option value="UK">英国</option>
            <option value="FR">法国</option>
          </select>
        </div>
      </div>

      <!-- 应用按钮 -->
      <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          {{ activeFiltersCount }} 个活跃过滤器
        </div>
        <div class="flex space-x-3">
          <Button
            @click="$emit('close')"
            variant="outline"
            size="sm"
          >
            取消
          </Button>
          <Button
            @click="applyFilters"
            size="sm"
          >
            应用过滤器
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface QueryCondition {
  field: string
  operator: string
  value: string
}

interface AudioFeatures {
  bpmMin: number | null
  bpmMax: number | null
  energy: number
  danceability: number
  valence: number
}

interface DateRange {
  start: string
  end: string
}

interface PlayStats {
  minPlays: number | null
  minLikes: number | null
}

interface Emits {
  (e: 'close'): void
  (e: 'apply', filters: any): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const queryConditions = ref<QueryCondition[]>([
  { field: 'title', operator: 'contains', value: '' }
])

const audioFeatures = ref<AudioFeatures>({
  bpmMin: null,
  bpmMax: null,
  energy: 50,
  danceability: 50,
  valence: 50
})

const dateRange = ref<DateRange>({
  start: '',
  end: ''
})

const playStats = ref<PlayStats>({
  minPlays: null,
  minLikes: null
})

const language = ref('')
const region = ref('')

// 计算属性
const activeFiltersCount = computed(() => {
  let count = 0
  
  // 查询条件
  count += queryConditions.value.filter(c => c.value.trim()).length
  
  // 音频特征
  if (audioFeatures.value.bpmMin !== null || audioFeatures.value.bpmMax !== null) count++
  if (audioFeatures.value.energy !== 50) count++
  if (audioFeatures.value.danceability !== 50) count++
  if (audioFeatures.value.valence !== 50) count++
  
  // 日期范围
  if (dateRange.value.start || dateRange.value.end) count++
  
  // 播放统计
  if (playStats.value.minPlays !== null) count++
  if (playStats.value.minLikes !== null) count++
  
  // 语言和地区
  if (language.value) count++
  if (region.value) count++
  
  return count
})

// 方法
const addCondition = () => {
  queryConditions.value.push({
    field: 'title',
    operator: 'contains',
    value: ''
  })
}

const removeCondition = (index: number) => {
  if (queryConditions.value.length > 1) {
    queryConditions.value.splice(index, 1)
  }
}

const resetFilters = () => {
  queryConditions.value = [{ field: 'title', operator: 'contains', value: '' }]
  audioFeatures.value = {
    bpmMin: null,
    bpmMax: null,
    energy: 50,
    danceability: 50,
    valence: 50
  }
  dateRange.value = { start: '', end: '' }
  playStats.value = { minPlays: null, minLikes: null }
  language.value = ''
  region.value = ''
}

const applyFilters = () => {
  const filters = {
    queryConditions: queryConditions.value.filter(c => c.value.trim()),
    audioFeatures: audioFeatures.value,
    dateRange: dateRange.value,
    playStats: playStats.value,
    language: language.value,
    region: region.value
  }
  
  emit('apply', filters)
}
</script>

<style scoped>
/* 滑块样式 */
.slider-thumb::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: rgb(37 99 235);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-thumb::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: rgb(37 99 235);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark .slider-thumb::-webkit-slider-thumb {
  background: rgb(96 165 250);
}

.dark .slider-thumb::-moz-range-thumb {
  background: rgb(96 165 250);
}

/* 输入框聚焦效果 */
input:focus, select:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.dark input:focus, .dark select:focus {
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

/* 过渡动画 */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 响应式调整 */
@media (max-width: 640px) {
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .sm\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}
</style>
