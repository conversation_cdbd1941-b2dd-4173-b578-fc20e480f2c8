<template>
  <div class="relative w-full max-w-2xl mx-auto">
    <!-- 搜索输入框 -->
    <div class="relative">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Icon name="MagnifyingGlassIcon" class="h-5 w-5 text-gray-400" />
      </div>
      
      <input
        ref="searchInput"
        v-model="localQuery"
        type="text"
        :placeholder="placeholder"
        class="block w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg 
               bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
               placeholder-gray-500 dark:placeholder-gray-400
               focus:ring-2 focus:ring-primary-500 focus:border-primary-500
               transition-colors duration-200"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
        autocomplete="off"
        spellcheck="false"
      />
      
      <!-- 清除按钮 -->
      <div class="absolute inset-y-0 right-0 flex items-center">
        <button
          v-if="localQuery"
          @click="clearSearch"
          class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 
                 transition-colors duration-200"
          type="button"
        >
          <Icon name="XMarkIcon" class="h-4 w-4" />
        </button>
        
        <!-- 语音搜索按钮 -->
        <button
          v-if="enableVoiceSearch && !localQuery"
          @click="startVoiceSearch"
          :disabled="isListening"
          class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 
                 disabled:opacity-50 disabled:cursor-not-allowed
                 transition-colors duration-200"
          type="button"
          :title="isListening ? '正在听取...' : '语音搜索'"
        >
          <Icon 
            :name="isListening ? 'StopIcon' : 'MicrophoneIcon'" 
            :class="['h-4 w-4', { 'text-red-500 animate-pulse': isListening }]" 
          />
        </button>
      </div>
    </div>

    <!-- 搜索建议下拉框 -->
    <Transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="opacity-0 translate-y-1"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 translate-y-1"
    >
      <div
        v-if="showSuggestions && (suggestions.length > 0 || history.length > 0 || hotSearches.length > 0)"
        class="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 
               border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg
               max-h-96 overflow-y-auto"
      >
        <!-- 搜索建议 -->
        <div v-if="suggestions.length > 0" class="py-2">
          <div class="px-3 py-1 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
            搜索建议
          </div>
          <button
            v-for="(suggestion, index) in suggestions"
            :key="`suggestion-${suggestion.id}`"
            @click="selectSuggestion(suggestion.text)"
            :class="[
              'w-full px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150',
              { 'bg-gray-50 dark:bg-gray-700': selectedIndex === index }
            ]"
            class="flex items-center justify-between"
          >
            <div class="flex items-center space-x-2">
              <Icon 
                :name="getSuggestionIcon(suggestion.type)" 
                class="h-4 w-4 text-gray-400" 
              />
              <span class="text-gray-900 dark:text-gray-100">{{ suggestion.text }}</span>
              <span 
                v-if="suggestion.type !== 'query'" 
                class="text-xs text-gray-500 dark:text-gray-400 capitalize"
              >
                {{ suggestion.type }}
              </span>
            </div>
            <span 
              v-if="suggestion.count" 
              class="text-xs text-gray-500 dark:text-gray-400"
            >
              {{ formatCount(suggestion.count) }}
            </span>
          </button>
        </div>

        <!-- 搜索历史 -->
        <div v-if="history.length > 0 && !localQuery" class="py-2 border-t border-gray-200 dark:border-gray-700">
          <div class="px-3 py-1 flex items-center justify-between">
            <span class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
              搜索历史
            </span>
            <button
              @click="clearHistory"
              class="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
            >
              清除
            </button>
          </div>
          <button
            v-for="(item, index) in history.slice(0, 5)"
            :key="`history-${item.id}`"
            @click="selectSuggestion(item.query)"
            :class="[
              'w-full px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150',
              { 'bg-gray-50 dark:bg-gray-700': selectedIndex === suggestions.length + index }
            ]"
            class="flex items-center justify-between group"
          >
            <div class="flex items-center space-x-2">
              <Icon name="ClockIcon" class="h-4 w-4 text-gray-400" />
              <span class="text-gray-900 dark:text-gray-100">{{ item.query }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-xs text-gray-500 dark:text-gray-400">
                {{ formatTime(item.timestamp) }}
              </span>
              <button
                @click.stop="removeHistoryItem(item.id)"
                class="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-all duration-150"
              >
                <Icon name="XMarkIcon" class="h-3 w-3" />
              </button>
            </div>
          </button>
        </div>

        <!-- 热门搜索 -->
        <div v-if="hotSearches.length > 0 && !localQuery && !history.length" class="py-2 border-t border-gray-200 dark:border-gray-700">
          <div class="px-3 py-1 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
            热门搜索
          </div>
          <button
            v-for="(hotSearch, index) in hotSearches.slice(0, 8)"
            :key="`hot-${index}`"
            @click="selectSuggestion(hotSearch)"
            :class="[
              'w-full px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150',
              { 'bg-gray-50 dark:bg-gray-700': selectedIndex === suggestions.length + history.length + index }
            ]"
            class="flex items-center space-x-2"
          >
            <Icon name="FireIcon" class="h-4 w-4 text-orange-500" />
            <span class="text-gray-900 dark:text-gray-100">{{ hotSearch }}</span>
          </button>
        </div>
      </div>
    </Transition>

    <!-- 语音搜索状态 -->
    <Transition
      enter-active-class="transition ease-out duration-300"
      enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100"
      leave-active-class="transition ease-in duration-200"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-95"
    >
      <div
        v-if="isListening"
        class="absolute inset-0 bg-white dark:bg-gray-800 rounded-lg border-2 border-primary-500 
               flex items-center justify-center z-40"
      >
        <div class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 bg-primary-100 dark:bg-primary-900 rounded-full 
                      flex items-center justify-center">
            <Icon name="MicrophoneIcon" class="h-8 w-8 text-primary-600 dark:text-primary-400 animate-pulse" />
          </div>
          <p class="text-gray-900 dark:text-gray-100 font-medium">正在听取...</p>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">请说出您要搜索的内容</p>
          <button
            @click="stopVoiceSearch"
            class="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200"
          >
            停止
          </button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import type { SearchSuggestion, SearchHistory } from '~/types'

interface Props {
  modelValue?: string
  placeholder?: string
  enableVoiceSearch?: boolean
  showSuggestions?: boolean
  autoFocus?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'search', query: string): void
  (e: 'suggestion-select', suggestion: string): void
  (e: 'focus'): void
  (e: 'blur'): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '搜索音乐、歌单、用户...',
  enableVoiceSearch: true,
  showSuggestions: true,
  autoFocus: false
})

const emit = defineEmits<Emits>()

// 搜索store
const searchStore = useSearchStore()
const { getSuggestions } = useSearchApi()

// 响应式数据
const searchInput = ref<HTMLInputElement>()
const localQuery = ref(props.modelValue || '')
const showSuggestions = ref(false)
const selectedIndex = ref(-1)
const isListening = ref(false)
const debounceTimer = ref<NodeJS.Timeout>()

// 计算属性
const suggestions = computed(() => searchStore.suggestions)
const history = computed(() => searchStore.history)
const hotSearches = computed(() => searchStore.hotSearches)

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  localQuery.value = newValue || ''
})

// 监听本地查询变化
watch(localQuery, (newValue) => {
  emit('update:modelValue', newValue)
})

// 处理输入
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  localQuery.value = target.value
  
  // 防抖获取搜索建议
  if (debounceTimer.value) {
    clearTimeout(debounceTimer.value)
  }
  
  debounceTimer.value = setTimeout(async () => {
    if (localQuery.value.trim()) {
      try {
        await getSuggestions(localQuery.value)
      } catch (error) {
        console.error('获取搜索建议失败:', error)
      }
    }
  }, 300)
  
  selectedIndex.value = -1
}

// 处理焦点
const handleFocus = () => {
  showSuggestions.value = true
  emit('focus')
}

// 处理失焦
const handleBlur = () => {
  // 延迟隐藏建议，允许点击建议项
  setTimeout(() => {
    showSuggestions.value = false
  }, 200)
  emit('blur')
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  const totalItems = suggestions.value.length + 
    (localQuery.value ? 0 : history.value.slice(0, 5).length) +
    (localQuery.value || history.value.length ? 0 : hotSearches.value.slice(0, 8).length)

  switch (event.key) {
    case 'Enter':
      event.preventDefault()
      if (selectedIndex.value >= 0) {
        // 选择建议项
        const allItems = [
          ...suggestions.value.map(s => s.text),
          ...(localQuery.value ? [] : history.value.slice(0, 5).map(h => h.query)),
          ...(localQuery.value || history.value.length ? [] : hotSearches.value.slice(0, 8))
        ]
        selectSuggestion(allItems[selectedIndex.value])
      } else if (localQuery.value.trim()) {
        // 直接搜索
        performSearch()
      }
      break
    
    case 'ArrowDown':
      event.preventDefault()
      selectedIndex.value = Math.min(selectedIndex.value + 1, totalItems - 1)
      break
    
    case 'ArrowUp':
      event.preventDefault()
      selectedIndex.value = Math.max(selectedIndex.value - 1, -1)
      break
    
    case 'Escape':
      showSuggestions.value = false
      selectedIndex.value = -1
      searchInput.value?.blur()
      break
  }
}

// 选择建议
const selectSuggestion = (suggestion: string) => {
  localQuery.value = suggestion
  showSuggestions.value = false
  selectedIndex.value = -1
  emit('suggestion-select', suggestion)
  performSearch()
}

// 执行搜索
const performSearch = () => {
  if (localQuery.value.trim()) {
    emit('search', localQuery.value.trim())
    showSuggestions.value = false
  }
}

// 清除搜索
const clearSearch = () => {
  localQuery.value = ''
  searchInput.value?.focus()
}

// 清除历史
const clearHistory = () => {
  searchStore.clearHistory()
}

// 删除历史项
const removeHistoryItem = (id: string) => {
  searchStore.removeHistoryItem(id)
}

// 语音搜索
const startVoiceSearch = () => {
  if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
    alert('您的浏览器不支持语音识别功能')
    return
  }

  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
  const recognition = new SpeechRecognition()
  
  recognition.lang = 'zh-CN'
  recognition.continuous = false
  recognition.interimResults = false

  recognition.onstart = () => {
    isListening.value = true
  }

  recognition.onresult = (event) => {
    const result = event.results[0][0].transcript
    localQuery.value = result
    performSearch()
  }

  recognition.onerror = (event) => {
    console.error('语音识别错误:', event.error)
    isListening.value = false
  }

  recognition.onend = () => {
    isListening.value = false
  }

  recognition.start()
}

// 停止语音搜索
const stopVoiceSearch = () => {
  isListening.value = false
}

// 获取建议图标
const getSuggestionIcon = (type: string) => {
  switch (type) {
    case 'artist': return 'UserIcon'
    case 'album': return 'MusicalNoteIcon'
    case 'genre': return 'TagIcon'
    default: return 'MagnifyingGlassIcon'
  }
}

// 格式化数量
const formatCount = (count: number) => {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`
  }
  return count.toString()
}

// 格式化时间
const formatTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

// 组件挂载时的处理
onMounted(() => {
  if (props.autoFocus) {
    nextTick(() => {
      searchInput.value?.focus()
    })
  }
  
  // 初始化热门搜索
  searchStore.getHotSearches()
})

// 组件卸载时清理
onUnmounted(() => {
  if (debounceTimer.value) {
    clearTimeout(debounceTimer.value)
  }
})

// 键盘快捷键支持
onMounted(() => {
  const handleGlobalKeydown = (event: KeyboardEvent) => {
    // Ctrl/Cmd + K 聚焦搜索框
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
      event.preventDefault()
      searchInput.value?.focus()
    }
  }
  
  document.addEventListener('keydown', handleGlobalKeydown)
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleGlobalKeydown)
  })
})
</script>

<style scoped>
/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-700 rounded-full;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}
</style>
