<template>
  <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-96 overflow-y-auto">
    <!-- AI智能建议 -->
    <div v-if="aiSuggestions.length > 0" class="py-2">
      <div class="px-3 py-1 flex items-center space-x-2">
        <Icon name="SparklesIcon" class="w-4 h-4 text-purple-500" />
        <span class="text-xs font-medium text-purple-600 dark:text-purple-400 uppercase tracking-wide">
          AI智能建议
        </span>
      </div>
      <div
        v-for="(suggestion, index) in aiSuggestions"
        :key="`ai-${suggestion.id}`"
        @click="selectSuggestion(suggestion.text)"
        :class="[
          'px-3 py-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150',
          { 'bg-gray-50 dark:bg-gray-700': selectedIndex === index }
        ]"
        class="group"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center">
              <Icon name="SparklesIcon" class="w-4 h-4 text-white" />
            </div>
            <div>
              <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ suggestion.text }}
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                {{ suggestion.reason }}
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <span class="text-xs text-purple-600 dark:text-purple-400 font-medium">
              {{ Math.round(suggestion.confidence * 100) }}%
            </span>
            <Icon 
              name="ArrowTopRightOnSquareIcon" 
              class="w-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-150" 
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 语义搜索建议 -->
    <div v-if="semanticSuggestions.length > 0" class="py-2 border-t border-gray-200 dark:border-gray-700">
      <div class="px-3 py-1 flex items-center space-x-2">
        <Icon name="MagnifyingGlassIcon" class="w-4 h-4 text-blue-500" />
        <span class="text-xs font-medium text-blue-600 dark:text-blue-400 uppercase tracking-wide">
          语义搜索
        </span>
      </div>
      <div
        v-for="(suggestion, index) in semanticSuggestions"
        :key="`semantic-${suggestion.id}`"
        @click="selectSuggestion(suggestion.text)"
        :class="[
          'px-3 py-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150',
          { 'bg-gray-50 dark:bg-gray-700': selectedIndex === aiSuggestions.length + index }
        ]"
        class="group"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <Icon name="LightBulbIcon" class="w-4 h-4 text-blue-500" />
            <div>
              <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ suggestion.text }}
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                {{ suggestion.description }}
              </div>
            </div>
          </div>
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ formatNumber(suggestion.resultCount) }} 结果
          </span>
        </div>
      </div>
    </div>

    <!-- 趋势搜索 -->
    <div v-if="trendingSuggestions.length > 0" class="py-2 border-t border-gray-200 dark:border-gray-700">
      <div class="px-3 py-1 flex items-center space-x-2">
        <Icon name="TrendingUpIcon" class="w-4 h-4 text-green-500" />
        <span class="text-xs font-medium text-green-600 dark:text-green-400 uppercase tracking-wide">
          趋势搜索
        </span>
      </div>
      <div class="px-3 py-2">
        <div class="flex flex-wrap gap-2">
          <button
            v-for="(trend, index) in trendingSuggestions"
            :key="`trend-${index}`"
            @click="selectSuggestion(trend.text)"
            :class="[
              'px-3 py-1 text-sm rounded-full border transition-all duration-200 hover:scale-105',
              getTrendBadgeClass(trend.rank)
            ]"
          >
            <div class="flex items-center space-x-1">
              <span class="text-xs font-bold">#{{ trend.rank }}</span>
              <span>{{ trend.text }}</span>
              <Icon 
                name="ArrowTrendingUpIcon" 
                :class="['w-3 h-3', getTrendIconClass(trend.changeRate)]" 
              />
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- 个性化建议 -->
    <div v-if="personalizedSuggestions.length > 0" class="py-2 border-t border-gray-200 dark:border-gray-700">
      <div class="px-3 py-1 flex items-center space-x-2">
        <Icon name="UserIcon" class="w-4 h-4 text-orange-500" />
        <span class="text-xs font-medium text-orange-600 dark:text-orange-400 uppercase tracking-wide">
          为你推荐
        </span>
      </div>
      <div
        v-for="(suggestion, index) in personalizedSuggestions"
        :key="`personal-${suggestion.id}`"
        @click="selectSuggestion(suggestion.text)"
        :class="[
          'px-3 py-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150',
          { 'bg-gray-50 dark:bg-gray-700': selectedIndex === getPersonalizedIndex(index) }
        ]"
        class="group"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="w-6 h-6 bg-gradient-to-r from-orange-400 to-red-400 rounded-full flex items-center justify-center">
              <Icon name="HeartIcon" class="w-3 h-3 text-white" />
            </div>
            <div>
              <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ suggestion.text }}
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                基于你的{{ suggestion.basedOn }}
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-1">
            <div class="flex">
              <Icon
                v-for="i in 5"
                :key="i"
                name="StarIcon"
                :class="[
                  'w-3 h-3',
                  i <= suggestion.relevanceScore 
                    ? 'text-yellow-400' 
                    : 'text-gray-300 dark:text-gray-600'
                ]"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能纠错 -->
    <div v-if="correctionSuggestion" class="py-2 border-t border-gray-200 dark:border-gray-700">
      <div class="px-3 py-1 flex items-center space-x-2">
        <Icon name="ExclamationTriangleIcon" class="w-4 h-4 text-yellow-500" />
        <span class="text-xs font-medium text-yellow-600 dark:text-yellow-400 uppercase tracking-wide">
          你是否想搜索
        </span>
      </div>
      <div
        @click="selectSuggestion(correctionSuggestion.text)"
        class="px-3 py-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150 group"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <Icon name="ArrowPathIcon" class="w-4 h-4 text-yellow-500" />
            <div>
              <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ correctionSuggestion.text }}
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                自动纠错建议
              </div>
            </div>
          </div>
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ formatNumber(correctionSuggestion.resultCount) }} 结果
          </span>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="isEmpty" class="py-8 text-center">
      <Icon name="MagnifyingGlassIcon" class="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
      <p class="text-sm text-gray-500 dark:text-gray-400">
        暂无智能建议
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface AISuggestion {
  id: string
  text: string
  reason: string
  confidence: number
}

interface SemanticSuggestion {
  id: string
  text: string
  description: string
  resultCount: number
}

interface TrendingSuggestion {
  text: string
  rank: number
  changeRate: number
}

interface PersonalizedSuggestion {
  id: string
  text: string
  basedOn: string
  relevanceScore: number
}

interface CorrectionSuggestion {
  text: string
  resultCount: number
}

interface Props {
  query: string
  aiSuggestions?: AISuggestion[]
  semanticSuggestions?: SemanticSuggestion[]
  trendingSuggestions?: TrendingSuggestion[]
  personalizedSuggestions?: PersonalizedSuggestion[]
  correctionSuggestion?: CorrectionSuggestion | null
  selectedIndex?: number
}

interface Emits {
  (e: 'select', suggestion: string): void
}

const props = withDefaults(defineProps<Props>(), {
  aiSuggestions: () => [],
  semanticSuggestions: () => [],
  trendingSuggestions: () => [],
  personalizedSuggestions: () => [],
  correctionSuggestion: null,
  selectedIndex: -1
})

const emit = defineEmits<Emits>()

// 计算属性
const isEmpty = computed(() => {
  return props.aiSuggestions.length === 0 &&
         props.semanticSuggestions.length === 0 &&
         props.trendingSuggestions.length === 0 &&
         props.personalizedSuggestions.length === 0 &&
         !props.correctionSuggestion
})

// 方法
const selectSuggestion = (suggestion: string) => {
  emit('select', suggestion)
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

const getTrendBadgeClass = (rank: number) => {
  if (rank <= 3) {
    return 'bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900 dark:to-orange-900 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-700'
  } else if (rank <= 10) {
    return 'bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900 dark:to-blue-900 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700'
  } else {
    return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-600'
  }
}

const getTrendIconClass = (changeRate: number) => {
  if (changeRate > 0.5) {
    return 'text-red-500'
  } else if (changeRate > 0.2) {
    return 'text-orange-500'
  } else {
    return 'text-green-500'
  }
}

const getPersonalizedIndex = (index: number) => {
  return props.aiSuggestions.length + props.semanticSuggestions.length + index
}
</script>

<style scoped>
/* 渐变背景 */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

/* 悬停缩放效果 */
.hover\:scale-105:hover {
  transform: scale(1.05);
}

/* 星星评分样式 */
.text-yellow-400 {
  fill: currentColor;
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-700 rounded-full;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* 过渡动画 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 组悬停效果 */
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}
</style>
