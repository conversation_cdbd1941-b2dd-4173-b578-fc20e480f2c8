<template>
  <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
    <!-- 筛选器头部 -->
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
        筛选条件
      </h3>
      <div class="flex items-center space-x-2">
        <button
          @click="resetFilters"
          class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 
                 transition-colors duration-200"
        >
          重置
        </button>
        <button
          @click="toggleCollapse"
          class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"
        >
          <Icon 
            :name="isCollapsed ? 'ChevronDownIcon' : 'ChevronUpIcon'" 
            class="w-4 h-4" 
          />
        </button>
      </div>
    </div>

    <!-- 筛选器内容 -->
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 max-h-0"
      enter-to-class="opacity-100 max-h-96"
      leave-active-class="transition-all duration-300 ease-in"
      leave-from-class="opacity-100 max-h-96"
      leave-to-class="opacity-0 max-h-0"
    >
      <div v-if="!isCollapsed" class="space-y-6 overflow-hidden">
        <!-- 内容类型筛选 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            内容类型
          </label>
          <div class="flex flex-wrap gap-2">
            <button
              v-for="type in contentTypes"
              :key="type.value"
              @click="updateFilter('type', type.value)"
              :class="[
                'px-3 py-1 text-sm rounded-full border transition-all duration-200',
                localFilters.type === type.value
                  ? 'bg-primary-500 text-white border-primary-500'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
              ]"
            >
              {{ type.label }}
            </button>
          </div>
        </div>

        <!-- 音乐风格筛选 -->
        <div v-if="localFilters.type === 'all' || localFilters.type === 'music'">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            音乐风格
          </label>
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
            <button
              v-for="genre in genres"
              :key="genre"
              @click="updateFilter('genre', genre === localFilters.genre ? undefined : genre)"
              :class="[
                'px-3 py-2 text-sm rounded-md border text-left transition-all duration-200',
                localFilters.genre === genre
                  ? 'bg-primary-50 dark:bg-primary-900 text-primary-700 dark:text-primary-300 border-primary-200 dark:border-primary-700'
                  : 'bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600'
              ]"
            >
              {{ genre }}
            </button>
          </div>
        </div>

        <!-- 时间范围筛选 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            发布时间
          </label>
          <div class="flex flex-wrap gap-2">
            <button
              v-for="range in dateRanges"
              :key="range.value"
              @click="updateFilter('dateRange', range.value)"
              :class="[
                'px-3 py-1 text-sm rounded-full border transition-all duration-200',
                localFilters.dateRange === range.value
                  ? 'bg-primary-500 text-white border-primary-500'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
              ]"
            >
              {{ range.label }}
            </button>
          </div>
        </div>

        <!-- 音质筛选 -->
        <div v-if="localFilters.type === 'all' || localFilters.type === 'music'">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            音质
          </label>
          <div class="flex flex-wrap gap-2">
            <button
              v-for="quality in qualities"
              :key="quality.value"
              @click="updateFilter('quality', quality.value)"
              :class="[
                'px-3 py-1 text-sm rounded-full border transition-all duration-200',
                localFilters.quality === quality.value
                  ? 'bg-primary-500 text-white border-primary-500'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
              ]"
            >
              {{ quality.label }}
            </button>
          </div>
        </div>

        <!-- 语言筛选 -->
        <div v-if="localFilters.type === 'all' || localFilters.type === 'music'">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            语言
          </label>
          <select
            :value="localFilters.language || ''"
            @change="updateFilter('language', ($event.target as HTMLSelectElement).value || undefined)"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
                   bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                   focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="">所有语言</option>
            <option v-for="lang in languages" :key="lang" :value="lang">
              {{ lang }}
            </option>
          </select>
        </div>

        <!-- 时长筛选 -->
        <div v-if="localFilters.type === 'all' || localFilters.type === 'music'">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            时长 (分钟)
          </label>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">最短</label>
              <input
                type="number"
                :value="localFilters.duration?.min || ''"
                @input="updateDuration('min', ($event.target as HTMLInputElement).value)"
                placeholder="0"
                min="0"
                max="60"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
                       bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                       focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">最长</label>
              <input
                type="number"
                :value="localFilters.duration?.max || ''"
                @input="updateDuration('max', ($event.target as HTMLInputElement).value)"
                placeholder="60"
                min="0"
                max="60"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
                       bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                       focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>
        </div>

        <!-- 排序方式 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            排序方式
          </label>
          <select
            :value="localFilters.sortBy"
            @change="updateFilter('sortBy', ($event.target as HTMLSelectElement).value as any)"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
                   bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                   focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="relevance">相关性</option>
            <option value="date">发布时间</option>
            <option value="popularity">热度</option>
          </select>
        </div>

        <!-- 应用筛选按钮 -->
        <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
          <div class="text-sm text-gray-500 dark:text-gray-400">
            {{ activeFiltersCount }} 个筛选条件已应用
          </div>
          <div class="flex space-x-2">
            <Button
              @click="resetFilters"
              variant="outline"
              size="sm"
            >
              重置
            </Button>
            <Button
              @click="applyFilters"
              size="sm"
              :disabled="!hasChanges"
            >
              应用筛选
            </Button>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import type { SearchFilters } from '~/types'

interface Props {
  filters: SearchFilters
  collapsed?: boolean
}

interface Emits {
  (e: 'update:filters', filters: SearchFilters): void
  (e: 'apply', filters: SearchFilters): void
  (e: 'reset'): void
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const isCollapsed = ref(props.collapsed)
const localFilters = ref<SearchFilters>({ ...props.filters })

// 筛选选项数据
const contentTypes = [
  { value: 'all', label: '全部' },
  { value: 'music', label: '音乐' },
  { value: 'playlists', label: '歌单' },
  { value: 'users', label: '用户' }
]

const genres = [
  '流行', '摇滚', '民谣', '电子', '古典', '爵士',
  '说唱', '乡村', '蓝调', '雷鬼', '朋克', '金属',
  '新世纪', '世界音乐', '实验', '环境音乐'
]

const dateRanges = [
  { value: 'all', label: '全部时间' },
  { value: 'today', label: '今天' },
  { value: 'week', label: '本周' },
  { value: 'month', label: '本月' },
  { value: 'year', label: '今年' }
]

const qualities = [
  { value: 'all', label: '全部音质' },
  { value: 'low', label: '标准' },
  { value: 'medium', label: '高品质' },
  { value: 'high', label: '超高品质' },
  { value: 'lossless', label: '无损' }
]

const languages = [
  '中文', '英文', '日文', '韩文', '法文', '德文', '西班牙文', '意大利文', '俄文', '其他'
]

// 计算属性
const activeFiltersCount = computed(() => {
  let count = 0
  if (localFilters.value.type !== 'all') count++
  if (localFilters.value.genre) count++
  if (localFilters.value.dateRange !== 'all') count++
  if (localFilters.value.quality !== 'all') count++
  if (localFilters.value.language) count++
  if (localFilters.value.duration?.min || localFilters.value.duration?.max) count++
  if (localFilters.value.sortBy !== 'relevance') count++
  return count
})

const hasChanges = computed(() => {
  return JSON.stringify(localFilters.value) !== JSON.stringify(props.filters)
})

// 监听props变化
watch(() => props.filters, (newFilters) => {
  localFilters.value = { ...newFilters }
}, { deep: true })

watch(() => props.collapsed, (newValue) => {
  isCollapsed.value = newValue
})

// 方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

const updateFilter = (key: keyof SearchFilters, value: any) => {
  localFilters.value = {
    ...localFilters.value,
    [key]: value
  }
  
  // 实时更新
  emit('update:filters', localFilters.value)
}

const updateDuration = (type: 'min' | 'max', value: string) => {
  const numValue = value ? parseInt(value) : undefined
  
  localFilters.value = {
    ...localFilters.value,
    duration: {
      ...localFilters.value.duration,
      [type]: numValue
    }
  }
  
  // 清理空的duration对象
  if (!localFilters.value.duration?.min && !localFilters.value.duration?.max) {
    const { duration, ...rest } = localFilters.value
    localFilters.value = rest
  }
  
  emit('update:filters', localFilters.value)
}

const applyFilters = () => {
  emit('apply', localFilters.value)
}

const resetFilters = () => {
  localFilters.value = {
    type: 'all',
    sortBy: 'relevance',
    dateRange: 'all',
    quality: 'all'
  }
  
  emit('update:filters', localFilters.value)
  emit('reset')
}

// 键盘快捷键
onMounted(() => {
  const handleKeydown = (event: KeyboardEvent) => {
    // Ctrl/Cmd + R 重置筛选
    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
      event.preventDefault()
      resetFilters()
    }
    
    // Enter 应用筛选
    if (event.key === 'Enter' && hasChanges.value) {
      applyFilters()
    }
  }
  
  document.addEventListener('keydown', handleKeydown)
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
})
</script>

<style scoped>
/* 过渡动画 */
.transition-all {
  transition-property: all;
}

/* 筛选按钮悬停效果 */
button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}

/* 输入框聚焦效果 */
input:focus,
select:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 网格布局响应式调整 */
@media (max-width: 640px) {
  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

@media (max-width: 768px) {
  .md\:grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* 滚动条样式 */
.overflow-hidden::-webkit-scrollbar {
  width: 4px;
}

.overflow-hidden::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-700;
}

.overflow-hidden::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}
</style>
