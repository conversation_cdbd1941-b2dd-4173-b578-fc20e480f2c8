<template>
  <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 overflow-hidden">
    <!-- 队列头部 -->
    <div class="p-4 border-b border-gray-200 dark:border-slate-700">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            播放队列
          </h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ playerStore.state.queue.length }} 首歌曲
          </p>
        </div>
        
        <div class="flex items-center space-x-2">
          <!-- 播放模式 -->
          <div class="flex items-center bg-gray-100 dark:bg-slate-700 rounded-lg p-1">
            <Button
              variant="ghost"
              size="sm"
              :class="{ 'bg-white dark:bg-slate-600 shadow-sm': !playerStore.state.shuffle }"
              @click="playerStore.toggleShuffle()"
              class="p-2"
            >
              <Icon name="bars-3" class="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              :class="{ 'bg-white dark:bg-slate-600 shadow-sm': playerStore.state.shuffle }"
              @click="playerStore.toggleShuffle()"
              class="p-2"
            >
              <Icon name="arrows-right-left" class="w-4 h-4" />
            </Button>
          </div>
          
          <!-- 循环模式 -->
          <Button
            variant="ghost"
            size="sm"
            :class="{ 'text-primary-500': playerStore.state.repeat !== 'none' }"
            @click="toggleRepeat"
            class="p-2"
          >
            <Icon :name="repeatIcon" class="w-4 h-4" />
          </Button>
          
          <!-- 清空队列 -->
          <Button
            variant="ghost"
            size="sm"
            @click="clearQueue"
            class="p-2 text-red-500 hover:text-red-600"
          >
            <Icon name="trash" class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>

    <!-- 当前播放 -->
    <div 
      v-if="playerStore.state.currentTrack"
      class="p-4 bg-primary-50 dark:bg-primary-900/20 border-b border-gray-200 dark:border-slate-700"
    >
      <div class="flex items-center space-x-3">
        <div class="w-12 h-12 bg-gray-200 dark:bg-slate-700 rounded-lg overflow-hidden flex-shrink-0">
          <img 
            v-if="playerStore.state.currentTrack.coverUrl" 
            :src="playerStore.state.currentTrack.coverUrl" 
            :alt="playerStore.state.currentTrack.title"
            class="w-full h-full object-cover"
          >
          <div 
            v-else 
            class="w-full h-full flex items-center justify-center"
          >
            <Icon name="musical-note" class="w-6 h-6 text-gray-400" />
          </div>
        </div>
        
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <Icon 
              :name="playerStore.state.isPlaying ? 'speaker-wave' : 'pause'" 
              class="w-4 h-4 text-primary-500" 
            />
            <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
              {{ playerStore.state.currentTrack.title }}
            </p>
          </div>
          <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
            {{ playerStore.state.currentTrack.artist }}
          </p>
        </div>
        
        <div class="text-xs text-gray-500 dark:text-gray-400">
          正在播放
        </div>
      </div>
    </div>

    <!-- 队列列表 -->
    <div class="max-h-96 overflow-y-auto">
      <div v-if="playerStore.state.queue.length === 0" class="p-8 text-center">
        <Icon name="queue-list" class="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
        <p class="text-gray-500 dark:text-gray-400">播放队列为空</p>
        <p class="text-sm text-gray-400 dark:text-gray-500 mt-1">
          添加一些歌曲开始播放吧
        </p>
      </div>
      
      <draggable
        v-else
        v-model="queueList"
        item-key="id"
        @end="onDragEnd"
        class="divide-y divide-gray-200 dark:divide-slate-700"
      >
        <template #item="{ element: track, index }">
          <div
            :class="[
              'flex items-center space-x-3 p-3 hover:bg-gray-50 dark:hover:bg-slate-700 cursor-pointer group',
              { 'bg-primary-50 dark:bg-primary-900/20': index === playerStore.state.currentIndex }
            ]"
            @click="playTrack(index)"
            @dblclick="playTrack(index)"
          >
            <!-- 拖拽手柄 -->
            <div class="drag-handle opacity-0 group-hover:opacity-100 transition-opacity cursor-move">
              <Icon name="bars-3" class="w-4 h-4 text-gray-400" />
            </div>
            
            <!-- 序号/播放状态 -->
            <div class="w-6 text-center">
              <div v-if="index === playerStore.state.currentIndex && playerStore.state.isPlaying" class="text-primary-500">
                <Icon name="speaker-wave" class="w-4 h-4" />
              </div>
              <div v-else-if="index === playerStore.state.currentIndex" class="text-primary-500">
                <Icon name="pause" class="w-4 h-4" />
              </div>
              <span v-else class="text-sm text-gray-500 dark:text-gray-400">
                {{ index + 1 }}
              </span>
            </div>
            
            <!-- 歌曲信息 -->
            <div class="w-10 h-10 bg-gray-200 dark:bg-slate-700 rounded overflow-hidden flex-shrink-0">
              <img 
                v-if="track.coverUrl" 
                :src="track.coverUrl" 
                :alt="track.title"
                class="w-full h-full object-cover"
              >
              <div 
                v-else 
                class="w-full h-full flex items-center justify-center"
              >
                <Icon name="musical-note" class="w-4 h-4 text-gray-400" />
              </div>
            </div>
            
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                {{ track.title }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
                {{ track.artist }}
              </p>
            </div>
            
            <!-- 时长 -->
            <div class="text-xs text-gray-500 dark:text-gray-400">
              {{ formatDuration(track.duration) }}
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                variant="ghost"
                size="sm"
                @click.stop="toggleLike(track)"
                :class="{ 'text-red-500': track.isLiked }"
                class="p-1"
              >
                <Icon :name="track.isLiked ? 'heart-solid' : 'heart'" class="w-3 h-3" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                @click.stop="removeFromQueue(index)"
                class="p-1 text-red-500 hover:text-red-600"
              >
                <Icon name="x-mark" class="w-3 h-3" />
              </Button>
            </div>
          </div>
        </template>
      </draggable>
    </div>

    <!-- 队列操作 -->
    <div v-if="playerStore.state.queue.length > 0" class="p-4 border-t border-gray-200 dark:border-slate-700">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          总时长：{{ formatTotalDuration() }}
        </div>
        
        <div class="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            @click="saveAsPlaylist"
            class="flex items-center space-x-1"
          >
            <Icon name="bookmark" class="w-3 h-3" />
            <span>保存为歌单</span>
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            @click="shareQueue"
            class="flex items-center space-x-1"
          >
            <Icon name="share" class="w-3 h-3" />
            <span>分享</span>
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import draggable from 'vuedraggable'
import type { Music } from '~/types'

const playerStore = usePlayerStore()
const audioPlayer = useAudioPlayer()

// 响应式数据
const queueList = computed({
  get: () => playerStore.state.queue,
  set: (value) => playerStore.setQueue(value)
})

// 计算属性
const repeatIcon = computed(() => {
  switch (playerStore.state.repeat) {
    case 'one':
      return 'arrow-path'
    case 'all':
      return 'arrow-path'
    default:
      return 'arrow-path'
  }
})

// 方法
const formatDuration = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0:00'
  
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const formatTotalDuration = (): string => {
  const totalSeconds = playerStore.state.queue.reduce((total, track) => total + track.duration, 0)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  }
  return `${minutes}分钟`
}

const toggleRepeat = () => {
  const modes: Array<'none' | 'one' | 'all'> = ['none', 'one', 'all']
  const currentIndex = modes.indexOf(playerStore.state.repeat)
  const nextIndex = (currentIndex + 1) % modes.length
  playerStore.setRepeat(modes[nextIndex])
}

const playTrack = (index: number) => {
  playerStore.setCurrentIndex(index)
}

const removeFromQueue = (index: number) => {
  playerStore.removeFromQueue(index)
}

const clearQueue = () => {
  if (confirm('确定要清空播放队列吗？')) {
    playerStore.clearQueue()
  }
}

const toggleLike = (track: Music) => {
  // 这里应该调用API来切换喜欢状态
  track.isLiked = !track.isLiked
  if (track.isLiked) {
    track.likeCount++
  } else {
    track.likeCount--
  }
}

const onDragEnd = (event: any) => {
  // 处理拖拽结束事件
  const { oldIndex, newIndex } = event
  
  if (oldIndex !== newIndex) {
    // 如果移动的是当前播放的歌曲
    if (oldIndex === playerStore.state.currentIndex) {
      playerStore.state.currentIndex = newIndex
    } else if (oldIndex < playerStore.state.currentIndex && newIndex >= playerStore.state.currentIndex) {
      // 从当前播放歌曲前面移动到后面
      playerStore.state.currentIndex--
    } else if (oldIndex > playerStore.state.currentIndex && newIndex <= playerStore.state.currentIndex) {
      // 从当前播放歌曲后面移动到前面
      playerStore.state.currentIndex++
    }
  }
}

const saveAsPlaylist = () => {
  // 保存队列为歌单
  console.log('保存为歌单')
}

const shareQueue = () => {
  // 分享播放队列
  console.log('分享播放队列')
}
</script>

<style scoped>
.drag-handle {
  cursor: move;
}

/* 拖拽时的样式 */
.sortable-ghost {
  opacity: 0.5;
}

.sortable-chosen {
  background-color: rgba(59, 130, 246, 0.1);
}
</style>
