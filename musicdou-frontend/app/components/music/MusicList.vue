<template>
  <div class="space-y-4">
    <!-- 列表头部 -->
    <div v-if="showHeader" class="flex items-center justify-between">
      <div>
        <h2 v-if="title" class="text-xl font-semibold text-gray-900 dark:text-white">
          {{ title }}
        </h2>
        <p v-if="description" class="text-sm text-gray-500 dark:text-gray-400 mt-1">
          {{ description }}
        </p>
      </div>
      
      <div class="flex items-center space-x-2">
        <!-- 播放全部按钮 -->
        <Button
          v-if="musicList.length > 0"
          variant="primary"
          @click="playAll"
          class="flex items-center space-x-2"
        >
          <Icon name="play" class="w-4 h-4" />
          <span>播放全部</span>
        </Button>
        
        <!-- 视图切换 -->
        <div class="flex items-center bg-gray-100 dark:bg-slate-700 rounded-lg p-1">
          <Button
            variant="ghost"
            size="sm"
            :class="{ 'bg-white dark:bg-slate-600 shadow-sm': viewMode === 'grid' }"
            @click="viewMode = 'grid'"
          >
            <Icon name="squares-2x2" class="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            :class="{ 'bg-white dark:bg-slate-600 shadow-sm': viewMode === 'list' }"
            @click="viewMode = 'list'"
          >
            <Icon name="bars-3" class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center py-8">
      <Loading type="spinner" size="md" text="加载中..." />
    </div>
    
    <!-- 空状态 -->
    <div v-else-if="musicList.length === 0" class="text-center py-12">
      <Icon name="musical-note" class="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
        {{ emptyTitle || '暂无音乐' }}
      </h3>
      <p class="text-gray-500 dark:text-gray-400">
        {{ emptyDescription || '这里还没有任何音乐，快去发现一些好听的歌曲吧！' }}
      </p>
    </div>
    
    <!-- 网格视图 -->
    <div 
      v-else-if="viewMode === 'grid'"
      :class="[
        'grid gap-4',
        gridCols
      ]"
    >
      <MusicCard
        v-for="music in musicList"
        :key="music.id"
        :music="music"
        :show-actions="showCardActions"
        @click="onMusicClick"
        @play="onMusicPlay"
        @like="onMusicLike"
        @add-to-queue="onAddToQueue"
        @add-to-playlist="onAddToPlaylist"
        @share="onMusicShare"
      />
    </div>
    
    <!-- 列表视图 -->
    <div v-else class="bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-slate-700 overflow-hidden">
      <!-- 表头 -->
      <div class="grid grid-cols-12 gap-4 px-4 py-3 bg-gray-50 dark:bg-slate-700 text-sm font-medium text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-slate-600">
        <div class="col-span-1 text-center">#</div>
        <div class="col-span-5">歌曲</div>
        <div class="col-span-2">艺术家</div>
        <div class="col-span-2">专辑</div>
        <div class="col-span-1 text-center">时长</div>
        <div class="col-span-1 text-center">操作</div>
      </div>
      
      <!-- 音乐列表 -->
      <div class="divide-y divide-gray-200 dark:divide-slate-700">
        <div
          v-for="(music, index) in musicList"
          :key="music.id"
          :class="[
            'grid grid-cols-12 gap-4 px-4 py-3 hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors cursor-pointer group',
            { 'bg-primary-50 dark:bg-primary-900/20': isCurrentTrack(music) }
          ]"
          @click="onMusicClick(music)"
          @dblclick="onMusicPlay(music)"
        >
          <!-- 序号/播放状态 -->
          <div class="col-span-1 flex items-center justify-center">
            <div v-if="isCurrentTrack(music) && playerStore.state.isPlaying" class="text-primary-500">
              <Icon name="speaker-wave" class="w-4 h-4" />
            </div>
            <div v-else-if="isCurrentTrack(music)" class="text-primary-500">
              <Icon name="pause" class="w-4 h-4" />
            </div>
            <span v-else class="text-sm text-gray-500 dark:text-gray-400 group-hover:hidden">
              {{ index + 1 }}
            </span>
            <Button
              v-if="!isCurrentTrack(music)"
              variant="ghost"
              size="sm"
              class="hidden group-hover:flex p-1"
              @click.stop="onMusicPlay(music)"
            >
              <Icon name="play" class="w-4 h-4" />
            </Button>
          </div>
          
          <!-- 歌曲信息 -->
          <div class="col-span-5 flex items-center space-x-3 min-w-0">
            <div class="w-10 h-10 bg-gray-200 dark:bg-slate-700 rounded overflow-hidden flex-shrink-0">
              <img 
                v-if="music.coverUrl" 
                :src="music.coverUrl" 
                :alt="music.title"
                class="w-full h-full object-cover"
              >
              <div 
                v-else 
                class="w-full h-full flex items-center justify-center"
              >
                <Icon name="musical-note" class="w-4 h-4 text-gray-400" />
              </div>
            </div>
            
            <div class="min-w-0 flex-1">
              <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                {{ music.title }}
              </p>
              <div class="flex items-center space-x-2 mt-1">
                <span v-if="music.isLiked" class="text-red-500">
                  <Icon name="heart-solid" class="w-3 h-3" />
                </span>
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  {{ formatNumber(music.playCount) }} 播放
                </span>
              </div>
            </div>
          </div>
          
          <!-- 艺术家 -->
          <div class="col-span-2 flex items-center">
            <p class="text-sm text-gray-600 dark:text-gray-300 truncate">
              {{ music.artist }}
            </p>
          </div>
          
          <!-- 专辑 -->
          <div class="col-span-2 flex items-center">
            <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
              {{ music.album || '-' }}
            </p>
          </div>
          
          <!-- 时长 -->
          <div class="col-span-1 flex items-center justify-center">
            <span class="text-sm text-gray-500 dark:text-gray-400">
              {{ formatDuration(music.duration) }}
            </span>
          </div>
          
          <!-- 操作按钮 -->
          <div class="col-span-1 flex items-center justify-center">
            <div class="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                variant="ghost"
                size="sm"
                @click.stop="toggleLike(music)"
                :class="{ 'text-red-500': music.isLiked }"
              >
                <Icon :name="music.isLiked ? 'heart-solid' : 'heart'" class="w-4 h-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                @click.stop="onAddToQueue(music)"
              >
                <Icon name="plus" class="w-4 h-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                @click.stop="showMusicOptions(music)"
              >
                <Icon name="ellipsis-horizontal" class="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分页 -->
    <div v-if="showPagination && totalPages > 1" class="flex justify-center mt-6">
      <nav class="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          :disabled="currentPage === 1"
          @click="$emit('page-change', currentPage - 1)"
        >
          <Icon name="chevron-left" class="w-4 h-4" />
        </Button>
        
        <div class="flex items-center space-x-1">
          <Button
            v-for="page in visiblePages"
            :key="page"
            :variant="page === currentPage ? 'primary' : 'ghost'"
            size="sm"
            @click="$emit('page-change', page)"
          >
            {{ page }}
          </Button>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          :disabled="currentPage === totalPages"
          @click="$emit('page-change', currentPage + 1)"
        >
          <Icon name="chevron-right" class="w-4 h-4" />
        </Button>
      </nav>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Music } from '~/types'

interface Props {
  musicList: Music[]
  loading?: boolean
  title?: string
  description?: string
  showHeader?: boolean
  showCardActions?: boolean
  showPagination?: boolean
  currentPage?: number
  totalPages?: number
  emptyTitle?: string
  emptyDescription?: string
  defaultViewMode?: 'grid' | 'list'
  gridColumns?: 2 | 3 | 4 | 5 | 6
}

interface Emits {
  (e: 'music-click', music: Music): void
  (e: 'music-play', music: Music): void
  (e: 'music-like', music: Music): void
  (e: 'add-to-queue', music: Music): void
  (e: 'add-to-playlist', music: Music): void
  (e: 'music-share', music: Music): void
  (e: 'page-change', page: number): void
  (e: 'play-all'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showHeader: true,
  showCardActions: false,
  showPagination: false,
  currentPage: 1,
  totalPages: 1,
  defaultViewMode: 'grid',
  gridColumns: 4
})

const emit = defineEmits<Emits>()

const playerStore = usePlayerStore()
const audioPlayer = useAudioPlayer()

// 响应式数据
const viewMode = ref<'grid' | 'list'>(props.defaultViewMode)

// 计算属性
const gridCols = computed(() => {
  const colsMap = {
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
    5: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5',
    6: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 3xl:grid-cols-6'
  }
  return colsMap[props.gridColumns]
})

const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, props.currentPage - 2)
  const end = Math.min(props.totalPages, props.currentPage + 2)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// 方法
const formatDuration = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0:00'
  
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const isCurrentTrack = (music: Music): boolean => {
  return playerStore.state.currentTrack?.id === music.id
}

const onMusicClick = (music: Music) => {
  emit('music-click', music)
}

const onMusicPlay = (music: Music) => {
  audioPlayer.playTrack(music, props.musicList)
  emit('music-play', music)
}

const onMusicLike = (music: Music) => {
  emit('music-like', music)
}

const onAddToQueue = (music: Music) => {
  playerStore.addToQueue(music)
  emit('add-to-queue', music)
}

const onAddToPlaylist = (music: Music) => {
  emit('add-to-playlist', music)
}

const onMusicShare = (music: Music) => {
  emit('music-share', music)
}

const toggleLike = (music: Music) => {
  emit('music-like', music)
}

const showMusicOptions = (music: Music) => {
  // 显示音乐选项菜单
  console.log('显示音乐选项', music)
}

const playAll = () => {
  if (props.musicList.length > 0) {
    audioPlayer.playTrack(props.musicList[0], props.musicList)
    emit('play-all')
  }
}
</script>
