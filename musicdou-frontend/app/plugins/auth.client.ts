export default defineNuxtPlugin(async () => {
  // 暂时禁用自动初始化，避免与useAuth composable冲突
  // const authStore = useAuthStore()

  // // 初始化认证状态
  // await authStore.initAuth()

  // // 设置token自动刷新
  // const token = useCookie('auth-token')
  // if (token.value) {
  //   // 每30分钟尝试刷新token
  //   setInterval(async () => {
  //     try {
  //       await authStore.refreshToken()
  //     } catch (error) {
  //       console.warn('Token刷新失败:', error)
  //     }
  //   }, 30 * 60 * 1000) // 30分钟
  // }
})
