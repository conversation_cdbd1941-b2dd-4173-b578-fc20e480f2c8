export default defineNuxtRouteMiddleware(async (to) => {
  // 检查token是否存在
  const token = useCookie('auth-token')

  // 如果没有token，重定向到登录页面
  if (!token.value) {
    return navigateTo({
      path: '/login',
      query: { redirect: to.fullPath }
    })
  }

  // 仅在客户端进行token验证
  if (import.meta.client) {
    try {
      // 获取auth store实例
      const { useAuthStore } = await import('~/stores/auth')
      const authStore = useAuthStore()

      // 如果用户信息已存在且不超过5分钟，跳过验证
      const lastCheck = sessionStorage.getItem('auth-last-check')
      const now = Date.now()
      const fiveMinutes = 5 * 60 * 1000

      if (authStore.user && lastCheck && (now - parseInt(lastCheck)) < fiveMinutes) {
        return // 用户信息有效，跳过验证
      }

      // 验证token有效性
      const config = useRuntimeConfig()
      const response = await $fetch<{ success: boolean }>(`${config.public.apiBase}/auth/test`, {
        headers: {
          'Authorization': `Bearer ${token.value}`
        }
      })

      if (response.success) {
        // 更新最后检查时间
        sessionStorage.setItem('auth-last-check', now.toString())

        // 如果用户信息不存在，初始化认证状态
        if (!authStore.user) {
          await authStore.initAuth()
        }
        return
      }
    } catch (error: any) {
      console.warn('认证中间件验证失败:', error)

      // 如果token无效，清除认证状态并重定向
      if (error.status === 401) {
        // 使用auth store的resetAuth方法清除所有认证数据
        try {
          const { useAuthStore } = await import('~/stores/auth')
          const authStore = useAuthStore()
          authStore.resetAuth()
        } catch (storeError) {
          console.warn('清除auth store失败:', storeError)
          // 手动清除基本数据
          token.value = null
          if (import.meta.client) {
            sessionStorage.removeItem('auth-last-check')
          }
        }

        return navigateTo({
          path: '/login',
          query: { redirect: to.fullPath }
        })
      }

      // 其他错误，允许继续但记录警告
      console.warn('认证验证出现非致命错误，允许继续访问')
    }
  }
})
