import { defineStore } from 'pinia'
import type { User, LoginForm, RegisterForm } from '~/types'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const isLoggedIn = computed(() => !!user.value)
  const isLoading = ref(false)

  // API实例
  const {
    login: apiLogin,
    register: apiRegister,
    logout: apiLogout,
    getCurrentUser,
    updateProfile: apiUpdateProfile,
    refreshToken: apiRefreshToken
  } = useAuthApi()

  // 登录
  const login = async (credentials: LoginForm) => {
    try {
      isLoading.value = true
      const response = await apiLogin(credentials)

      if (response.success) {
        // 保存token
        const token = useCookie('auth-token', {
          default: () => null,
          maxAge: 60 * 60 * 24 * 7, // 7天
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax'
        })
        token.value = response.data.token

        // 保存用户信息
        user.value = response.data.user

        return response
      }

      throw new Error(response.message || '登录失败')
    } catch (error) {
      console.error('登录错误:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (userData: RegisterForm) => {
    try {
      isLoading.value = true
      const response = await apiRegister(userData)

      if (response.success) {
        return response
      }

      throw new Error(response.message || '注册失败')
    } catch (error) {
      console.error('注册错误:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      await apiLogout()
    } catch (error) {
      console.error('登出错误:', error)
    } finally {
      // 清除本地数据
      const token = useCookie('auth-token')
      token.value = null
      user.value = null

      // 跳转到首页
      await navigateTo('/')
    }
  }

  // 获取当前用户信息
  const fetchUser = async () => {
    try {
      const token = useCookie('auth-token')
      if (!token.value) return

      const response = await getCurrentUser()
      if (response.success) {
        user.value = response.data.user
      }
    } catch (error) {
      console.error('获取用户信息错误:', error)
      // 如果token无效，清除本地数据
      const token = useCookie('auth-token')
      token.value = null
      user.value = null
    }
  }

  // 更新用户信息
  const updateProfile = async (profileData: Partial<User>) => {
    try {
      isLoading.value = true
      const response = await apiUpdateProfile(profileData)

      if (response.success) {
        user.value = { ...user.value, ...response.data }
        return response
      }

      throw new Error(response.message || '更新失败')
    } catch (error) {
      console.error('更新用户信息错误:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // Token刷新
  const refreshToken = async () => {
    try {
      const response = await apiRefreshToken()
      if (response.success) {
        const token = useCookie('auth-token')
        token.value = response.data.token
        return true
      }
      return false
    } catch (error) {
      console.error('Token刷新失败:', error)
      // 刷新失败，清除认证状态
      const token = useCookie('auth-token')
      token.value = null
      user.value = null
      return false
    }
  }

  // 初始化认证状态
  const initAuth = async () => {
    const token = useCookie('auth-token')
    if (token.value && !user.value) {
      await fetchUser()
    }
  }

  // 重置认证状态
  const resetAuth = () => {
    user.value = null
    isLoading.value = false
    const token = useCookie('auth-token')
    token.value = null
    // 清除会话存储
    if (import.meta.client) {
      sessionStorage.removeItem('auth-last-check')
    }
  }

  return {
    // 状态
    user: readonly(user),
    isLoggedIn,
    isLoading: readonly(isLoading),

    // 方法
    login,
    register,
    logout,
    fetchUser,
    updateProfile,
    refreshToken,
    initAuth,
    resetAuth
  }
})
