import { defineStore } from 'pinia'
import type { User, Comment } from '~/types'

interface SocialState {
  // 关注系统状态
  followingUsers: User[]
  followers: User[]
  followingCount: number
  followersCount: number
  followStatus: Record<string, boolean>
  
  // 评论系统状态
  comments: Record<string, Comment[]>
  commentCounts: Record<string, number>
  
  // 点赞系统状态
  likedItems: Record<string, boolean>
  likeCounts: Record<string, number>
  
  // 动态系统状态
  activityFeed: any[]
  userActivities: Record<string, any[]>
  
  // 加载状态
  loading: {
    following: boolean
    followers: boolean
    comments: boolean
    activities: boolean
  }
}

export const useSocialStore = defineStore('social', {
  state: (): SocialState => ({
    followingUsers: [],
    followers: [],
    followingCount: 0,
    followersCount: 0,
    followStatus: {},
    
    comments: {},
    commentCounts: {},
    
    likedItems: {},
    likeCounts: {},
    
    activityFeed: [],
    userActivities: {},
    
    loading: {
      following: false,
      followers: false,
      comments: false,
      activities: false
    }
  }),

  getters: {
    // 检查是否关注某用户
    isFollowing: (state) => (userId: string) => {
      return state.followStatus[userId] || false
    },
    
    // 检查是否点赞某项目
    isLiked: (state) => (targetType: string, targetId: string) => {
      const key = `${targetType}:${targetId}`
      return state.likedItems[key] || false
    },
    
    // 获取点赞数
    getLikeCount: (state) => (targetType: string, targetId: string) => {
      const key = `${targetType}:${targetId}`
      return state.likeCounts[key] || 0
    },
    
    // 获取评论数
    getCommentCount: (state) => (targetType: string, targetId: string) => {
      const key = `${targetType}:${targetId}`
      return state.commentCounts[key] || 0
    },
    
    // 获取评论列表
    getComments: (state) => (targetType: string, targetId: string) => {
      const key = `${targetType}:${targetId}`
      return state.comments[key] || []
    }
  },

  actions: {
    // 关注系统操作
    async followUser(userId: string, source?: string) {
      const { followUser } = useSocialApi()
      
      try {
        this.loading.following = true
        const response = await followUser(userId, source)
        
        if (response.success) {
          this.followStatus[userId] = true
          this.followingCount++
          
          // 添加到关注列表
          const userStore = useAuthStore()
          if (userStore.user) {
            // 这里可以添加用户信息到关注列表
          }
        }
        
        return response
      } catch (error) {
        console.error('Follow user error:', error)
        throw error
      } finally {
        this.loading.following = false
      }
    },

    async unfollowUser(userId: string) {
      const { unfollowUser } = useSocialApi()
      
      try {
        this.loading.following = true
        const response = await unfollowUser(userId)
        
        if (response.success) {
          this.followStatus[userId] = false
          this.followingCount = Math.max(0, this.followingCount - 1)
          
          // 从关注列表中移除
          this.followingUsers = this.followingUsers.filter(user => user.id !== userId)
        }
        
        return response
      } catch (error) {
        console.error('Unfollow user error:', error)
        throw error
      } finally {
        this.loading.following = false
      }
    },

    async loadFollowStatus(userId: string) {
      const { checkFollowStatus } = useSocialApi()
      
      try {
        const response = await checkFollowStatus(userId)
        if (response.success) {
          this.followStatus[userId] = response.data.isFollowing
        }
        return response
      } catch (error) {
        console.error('Load follow status error:', error)
        throw error
      }
    },

    // 点赞系统操作
    async toggleLike(targetType: 'music' | 'comment' | 'playlist', targetId: string) {
      const { likeTarget, unlikeTarget } = useSocialApi()
      const key = `${targetType}:${targetId}`
      const isCurrentlyLiked = this.likedItems[key]
      
      try {
        let response
        if (isCurrentlyLiked) {
          response = await unlikeTarget(targetType, targetId)
          if (response.success) {
            this.likedItems[key] = false
            this.likeCounts[key] = Math.max(0, (this.likeCounts[key] || 0) - 1)
          }
        } else {
          response = await likeTarget(targetType, targetId)
          if (response.success) {
            this.likedItems[key] = true
            this.likeCounts[key] = (this.likeCounts[key] || 0) + 1
          }
        }
        
        return response
      } catch (error) {
        console.error('Toggle like error:', error)
        throw error
      }
    },

    async loadLikeStatus(targetType: string, targetId: string) {
      const { checkLikeStatus } = useSocialApi()
      const key = `${targetType}:${targetId}`
      
      try {
        const response = await checkLikeStatus(targetType, targetId)
        if (response.success) {
          this.likedItems[key] = response.data.isLiked
          this.likeCounts[key] = response.data.likeCount
        }
        return response
      } catch (error) {
        console.error('Load like status error:', error)
        throw error
      }
    },

    // 评论系统操作
    async loadComments(targetType: string, targetId: string, params?: any) {
      const { getComments } = useSocialApi()
      const key = `${targetType}:${targetId}`
      
      try {
        this.loading.comments = true
        const response = await getComments(targetType, targetId, params)
        
        if (response.success) {
          this.comments[key] = response.data.data
          this.commentCounts[key] = response.data.pagination.total
        }
        
        return response
      } catch (error) {
        console.error('Load comments error:', error)
        throw error
      } finally {
        this.loading.comments = false
      }
    },

    async addComment(targetType: 'music' | 'playlist', targetId: string, content: string, parentId?: string) {
      const { createComment } = useSocialApi()
      const key = `${targetType}:${targetId}`
      
      try {
        const response = await createComment({
          targetType,
          targetId,
          content,
          parentId
        })
        
        if (response.success) {
          // 添加到评论列表
          if (!this.comments[key]) {
            this.comments[key] = []
          }
          this.comments[key].unshift(response.data)
          this.commentCounts[key] = (this.commentCounts[key] || 0) + 1
        }
        
        return response
      } catch (error) {
        console.error('Add comment error:', error)
        throw error
      }
    },

    async deleteComment(commentId: string, targetType: string, targetId: string) {
      const { deleteComment } = useSocialApi()
      const key = `${targetType}:${targetId}`
      
      try {
        const response = await deleteComment(commentId)
        
        if (response.success) {
          // 从评论列表中移除
          if (this.comments[key]) {
            this.comments[key] = this.comments[key].filter(comment => comment.id !== commentId)
            this.commentCounts[key] = Math.max(0, (this.commentCounts[key] || 0) - 1)
          }
        }
        
        return response
      } catch (error) {
        console.error('Delete comment error:', error)
        throw error
      }
    },

    // 动态系统操作
    async loadActivityFeed(params?: any) {
      const { getActivityFeed } = useSocialApi()
      
      try {
        this.loading.activities = true
        const response = await getActivityFeed(params)
        
        if (response.success) {
          if (params?.page && params.page > 1) {
            // 分页加载，追加数据
            this.activityFeed.push(...response.data.data)
          } else {
            // 首次加载或刷新
            this.activityFeed = response.data.data
          }
        }
        
        return response
      } catch (error) {
        console.error('Load activity feed error:', error)
        throw error
      } finally {
        this.loading.activities = false
      }
    },

    // 清除状态
    clearSocialData() {
      this.followingUsers = []
      this.followers = []
      this.followStatus = {}
      this.comments = {}
      this.commentCounts = {}
      this.likedItems = {}
      this.likeCounts = {}
      this.activityFeed = []
      this.userActivities = {}
    }
  }
})
