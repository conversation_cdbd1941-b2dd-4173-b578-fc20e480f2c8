import { defineStore } from 'pinia'
import type { Playlist, Music } from '~/types'

interface PlaylistState {
  // 歌单列表
  playlists: Playlist[]
  currentPlaylist: Playlist | null
  
  // 用户相关
  userPlaylists: Playlist[]
  likedPlaylists: Playlist[]
  
  // 分页和筛选
  currentPage: number
  totalPages: number
  totalCount: number
  pageSize: number
  
  // 搜索和筛选
  searchQuery: string
  sortBy: string
  sortOrder: 'asc' | 'desc'
  filterPublic: boolean | null
  
  // 加载状态
  loading: boolean
  loadingMore: boolean
  
  // 缓存
  playlistCache: Map<string, Playlist>
  lastFetchTime: number
}

export const usePlaylistStore = defineStore('playlist', {
  state: (): PlaylistState => ({
    playlists: [],
    currentPlaylist: null,
    userPlaylists: [],
    likedPlaylists: [],
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 12,
    searchQuery: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
    filterPublic: null,
    loading: false,
    loadingMore: false,
    playlistCache: new Map(),
    lastFetchTime: 0
  }),

  getters: {
    // 获取歌单总数
    playlistCount: (state) => state.playlists.length,
    
    // 获取用户歌单数量
    userPlaylistCount: (state) => state.userPlaylists.length,
    
    // 获取收藏歌单数量
    likedPlaylistCount: (state) => state.likedPlaylists.length,
    
    // 检查是否有更多数据
    hasMore: (state) => state.currentPage < state.totalPages,
    
    // 获取筛选后的歌单
    filteredPlaylists: (state) => {
      let filtered = [...state.playlists]
      
      if (state.searchQuery) {
        const query = state.searchQuery.toLowerCase()
        filtered = filtered.filter(playlist => 
          playlist.name.toLowerCase().includes(query) ||
          playlist.description?.toLowerCase().includes(query) ||
          playlist.user?.username.toLowerCase().includes(query)
        )
      }
      
      if (state.filterPublic !== null) {
        filtered = filtered.filter(playlist => playlist.isPublic === state.filterPublic)
      }
      
      return filtered
    },
    
    // 检查歌单是否被收藏
    isPlaylistLiked: (state) => (playlistId: string) => {
      return state.likedPlaylists.some(playlist => playlist.id === playlistId)
    },
    
    // 获取歌单统计信息
    playlistStats: (state) => {
      const totalSongs = state.userPlaylists.reduce((sum, playlist) => sum + playlist.songCount, 0)
      const totalDuration = state.userPlaylists.reduce((sum, playlist) => sum + playlist.duration, 0)
      const totalPlays = state.userPlaylists.reduce((sum, playlist) => sum + playlist.playCount, 0)
      const totalLikes = state.userPlaylists.reduce((sum, playlist) => sum + playlist.likeCount, 0)
      
      return {
        totalPlaylists: state.userPlaylists.length,
        totalSongs,
        totalDuration,
        totalPlays,
        totalLikes
      }
    }
  },

  actions: {
    // 获取歌单列表
    async fetchPlaylists(params?: {
      page?: number
      limit?: number
      search?: string
      sortBy?: string
      sortOrder?: 'asc' | 'desc'
      isPublic?: boolean
      userId?: string
    }) {
      const { api } = useApi()
      
      try {
        this.loading = params?.page === 1 || !params?.page
        this.loadingMore = (params?.page || 1) > 1
        
        const queryParams = {
          page: params?.page || this.currentPage,
          limit: params?.limit || this.pageSize,
          search: params?.search || this.searchQuery,
          sortBy: params?.sortBy || this.sortBy,
          sortOrder: params?.sortOrder || this.sortOrder,
          ...(params?.isPublic !== undefined && { isPublic: params.isPublic }),
          ...(params?.userId && { userId: params.userId })
        }
        
        const response = await api('/playlists', {
          method: 'GET',
          query: queryParams
        })
        
        // 更新状态
        if (queryParams.page === 1) {
          this.playlists = response.data
        } else {
          this.playlists.push(...response.data)
        }
        
        this.currentPage = queryParams.page
        this.totalPages = Math.ceil(response.total / queryParams.limit)
        this.totalCount = response.total
        this.lastFetchTime = Date.now()
        
        // 更新缓存
        response.data.forEach((playlist: Playlist) => {
          this.playlistCache.set(playlist.id, playlist)
        })
        
        return response
      } catch (error) {
        throw error
      } finally {
        this.loading = false
        this.loadingMore = false
      }
    },

    // 获取用户歌单
    async fetchUserPlaylists(userId?: string) {
      const { api } = useApi()
      
      try {
        this.loading = true
        
        const response = await api('/playlists/user', {
          method: 'GET',
          query: userId ? { userId } : {}
        })
        
        this.userPlaylists = response.data
        
        // 更新缓存
        response.data.forEach((playlist: Playlist) => {
          this.playlistCache.set(playlist.id, playlist)
        })
        
        return response
      } catch (error) {
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取收藏的歌单
    async fetchLikedPlaylists() {
      const { api } = useApi()
      
      try {
        this.loading = true
        
        const response = await api('/playlists/liked', {
          method: 'GET'
        })
        
        this.likedPlaylists = response.data
        
        // 更新缓存
        response.data.forEach((playlist: Playlist) => {
          this.playlistCache.set(playlist.id, playlist)
        })
        
        return response
      } catch (error) {
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取歌单详情
    async fetchPlaylistDetail(id: string, forceRefresh = false) {
      const { api } = useApi()
      
      // 检查缓存
      if (!forceRefresh && this.playlistCache.has(id)) {
        const cached = this.playlistCache.get(id)!
        this.currentPlaylist = cached
        return cached
      }
      
      try {
        this.loading = true
        
        const response = await api(`/playlists/${id}`)
        
        this.currentPlaylist = response
        this.playlistCache.set(id, response)
        
        return response
      } catch (error) {
        throw error
      } finally {
        this.loading = false
      }
    },

    // 创建歌单
    async createPlaylist(data: {
      name: string
      description?: string
      isPublic: boolean
      coverUrl?: string
    }) {
      const { api } = useApi()
      
      try {
        const response = await api('/playlists', {
          method: 'POST',
          body: data
        })
        
        // 添加到用户歌单列表
        this.userPlaylists.unshift(response)
        this.playlistCache.set(response.id, response)
        
        return response
      } catch (error) {
        throw error
      }
    },

    // 更新歌单
    async updatePlaylist(id: string, data: {
      name?: string
      description?: string
      isPublic?: boolean
      coverUrl?: string
    }) {
      const { api } = useApi()
      
      try {
        const response = await api(`/playlists/${id}`, {
          method: 'PUT',
          body: data
        })
        
        // 更新本地数据
        this.updatePlaylistInLists(id, response)
        this.playlistCache.set(id, response)
        
        if (this.currentPlaylist?.id === id) {
          this.currentPlaylist = response
        }
        
        return response
      } catch (error) {
        throw error
      }
    },

    // 删除歌单
    async deletePlaylist(id: string) {
      const { api } = useApi()
      
      try {
        await api(`/playlists/${id}`, {
          method: 'DELETE'
        })
        
        // 从本地数据中移除
        this.removePlaylistFromLists(id)
        this.playlistCache.delete(id)
        
        if (this.currentPlaylist?.id === id) {
          this.currentPlaylist = null
        }
      } catch (error) {
        throw error
      }
    },

    // 收藏/取消收藏歌单
    async togglePlaylistLike(id: string) {
      const { api } = useApi()
      
      try {
        const response = await api(`/playlists/${id}/like`, {
          method: 'POST'
        })
        
        // 更新本地数据
        this.updatePlaylistLikeStatus(id, response.isLiked, response.likeCount)
        
        return response
      } catch (error) {
        throw error
      }
    },

    // 添加歌曲到歌单
    async addSongsToPlaylist(playlistId: string, songIds: string[]) {
      const { api } = useApi()
      
      try {
        const response = await api(`/playlists/${playlistId}/songs`, {
          method: 'POST',
          body: { songIds }
        })
        
        // 更新歌单信息
        if (this.currentPlaylist?.id === playlistId) {
          this.currentPlaylist.songs = response.songs
          this.currentPlaylist.songCount = response.songCount
          this.currentPlaylist.duration = response.duration
        }
        
        // 更新缓存
        const cachedPlaylist = this.playlistCache.get(playlistId)
        if (cachedPlaylist) {
          cachedPlaylist.songs = response.songs
          cachedPlaylist.songCount = response.songCount
          cachedPlaylist.duration = response.duration
        }
        
        return response
      } catch (error) {
        throw error
      }
    },

    // 从歌单移除歌曲
    async removeSongFromPlaylist(playlistId: string, songId: string) {
      const { api } = useApi()
      
      try {
        await api(`/playlists/${playlistId}/songs/${songId}`, {
          method: 'DELETE'
        })
        
        // 更新本地数据
        if (this.currentPlaylist?.id === playlistId && this.currentPlaylist.songs) {
          const songIndex = this.currentPlaylist.songs.findIndex(song => song.id === songId)
          if (songIndex > -1) {
            const removedSong = this.currentPlaylist.songs[songIndex]
            this.currentPlaylist.songs.splice(songIndex, 1)
            this.currentPlaylist.songCount--
            this.currentPlaylist.duration -= removedSong.duration
          }
        }
        
        // 更新缓存
        const cachedPlaylist = this.playlistCache.get(playlistId)
        if (cachedPlaylist?.songs) {
          const songIndex = cachedPlaylist.songs.findIndex(song => song.id === songId)
          if (songIndex > -1) {
            const removedSong = cachedPlaylist.songs[songIndex]
            cachedPlaylist.songs.splice(songIndex, 1)
            cachedPlaylist.songCount--
            cachedPlaylist.duration -= removedSong.duration
          }
        }
      } catch (error) {
        throw error
      }
    },

    // 重排歌单中的歌曲
    async reorderPlaylistSongs(playlistId: string, songIds: string[]) {
      const { api } = useApi()
      
      try {
        const response = await api(`/playlists/${playlistId}/songs/reorder`, {
          method: 'PUT',
          body: { songIds }
        })
        
        // 更新本地数据
        if (this.currentPlaylist?.id === playlistId) {
          this.currentPlaylist.songs = response.songs
        }
        
        // 更新缓存
        const cachedPlaylist = this.playlistCache.get(playlistId)
        if (cachedPlaylist) {
          cachedPlaylist.songs = response.songs
        }
        
        return response
      } catch (error) {
        throw error
      }
    },

    // 设置搜索查询
    setSearchQuery(query: string) {
      this.searchQuery = query
    },

    // 设置排序
    setSorting(sortBy: string, sortOrder: 'asc' | 'desc' = 'desc') {
      this.sortBy = sortBy
      this.sortOrder = sortOrder
    },

    // 设置筛选
    setFilter(isPublic: boolean | null) {
      this.filterPublic = isPublic
    },

    // 重置筛选
    resetFilters() {
      this.searchQuery = ''
      this.sortBy = 'createdAt'
      this.sortOrder = 'desc'
      this.filterPublic = null
      this.currentPage = 1
    },

    // 清空数据
    clearData() {
      this.playlists = []
      this.userPlaylists = []
      this.likedPlaylists = []
      this.currentPlaylist = null
      this.playlistCache.clear()
      this.currentPage = 1
      this.totalPages = 1
      this.totalCount = 0
    },

    // 辅助方法：更新歌单在各个列表中的数据
    updatePlaylistInLists(id: string, updatedPlaylist: Playlist) {
      // 更新主列表
      const mainIndex = this.playlists.findIndex(p => p.id === id)
      if (mainIndex > -1) {
        this.playlists[mainIndex] = updatedPlaylist
      }
      
      // 更新用户歌单列表
      const userIndex = this.userPlaylists.findIndex(p => p.id === id)
      if (userIndex > -1) {
        this.userPlaylists[userIndex] = updatedPlaylist
      }
      
      // 更新收藏列表
      const likedIndex = this.likedPlaylists.findIndex(p => p.id === id)
      if (likedIndex > -1) {
        this.likedPlaylists[likedIndex] = updatedPlaylist
      }
    },

    // 辅助方法：从各个列表中移除歌单
    removePlaylistFromLists(id: string) {
      this.playlists = this.playlists.filter(p => p.id !== id)
      this.userPlaylists = this.userPlaylists.filter(p => p.id !== id)
      this.likedPlaylists = this.likedPlaylists.filter(p => p.id !== id)
    },

    // 辅助方法：更新歌单点赞状态
    updatePlaylistLikeStatus(id: string, isLiked: boolean, likeCount: number) {
      const updatePlaylist = (playlist: Playlist) => {
        playlist.isLiked = isLiked
        playlist.likeCount = likeCount
      }
      
      // 更新各个列表中的歌单
      this.playlists.forEach(playlist => {
        if (playlist.id === id) updatePlaylist(playlist)
      })
      
      this.userPlaylists.forEach(playlist => {
        if (playlist.id === id) updatePlaylist(playlist)
      })
      
      this.likedPlaylists.forEach(playlist => {
        if (playlist.id === id) updatePlaylist(playlist)
      })
      
      // 更新当前歌单
      if (this.currentPlaylist?.id === id) {
        updatePlaylist(this.currentPlaylist)
      }
      
      // 更新缓存
      const cachedPlaylist = this.playlistCache.get(id)
      if (cachedPlaylist) {
        updatePlaylist(cachedPlaylist)
      }
      
      // 如果取消收藏，从收藏列表中移除
      if (!isLiked) {
        this.likedPlaylists = this.likedPlaylists.filter(p => p.id !== id)
      }
    }
  }
})
