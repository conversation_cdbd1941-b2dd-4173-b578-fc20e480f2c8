import { defineStore } from 'pinia'
import type { 
  SearchResult, 
  SearchFilters, 
  SearchQuery, 
  SearchSuggestion, 
  SearchHistory,
  SearchState
} from '~/types'

export const useSearchStore = defineStore('search', () => {
  // 状态
  const query = ref('')
  const results = ref<SearchResult | null>(null)
  const suggestions = ref<SearchSuggestion[]>([])
  const history = ref<SearchHistory[]>([])
  const hotSearches = ref<string[]>([])
  const isLoading = ref(false)
  const hasMore = ref(false)
  const currentPage = ref(1)
  const totalResults = ref(0)
  
  // 筛选条件
  const filters = ref<SearchFilters>({
    type: 'all',
    sortBy: 'relevance',
    dateRange: 'all',
    quality: 'all'
  })

  // 缓存 - 使用Map存储搜索结果缓存
  const cache = ref(new Map<string, { data: SearchResult; timestamp: number }>())
  const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  // API实例
  const { get, post } = useApi()

  // 计算属性
  const hasResults = computed(() => {
    if (!results.value) return false
    const { music, playlists, users } = results.value
    return music.length > 0 || playlists.length > 0 || users.length > 0
  })

  const totalResultsCount = computed(() => {
    if (!results.value) return 0
    const { music, playlists, users } = results.value
    return music.length + playlists.length + users.length
  })

  // 生成缓存键
  const getCacheKey = (searchQuery: string, searchFilters: SearchFilters, page: number) => {
    return `${searchQuery}-${JSON.stringify(searchFilters)}-${page}`
  }

  // 检查缓存是否有效
  const isCacheValid = (timestamp: number) => {
    return Date.now() - timestamp < CACHE_DURATION
  }

  // 从缓存获取结果
  const getFromCache = (key: string) => {
    const cached = cache.value.get(key)
    if (cached && isCacheValid(cached.timestamp)) {
      return cached.data
    }
    return null
  }

  // 保存到缓存
  const saveToCache = (key: string, data: SearchResult) => {
    cache.value.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  // 清理过期缓存
  const cleanExpiredCache = () => {
    const now = Date.now()
    for (const [key, value] of cache.value.entries()) {
      if (!isCacheValid(value.timestamp)) {
        cache.value.delete(key)
      }
    }
  }

  // 执行搜索
  const search = async (searchQuery?: string, resetPage = true) => {
    try {
      const queryToSearch = searchQuery || query.value
      if (!queryToSearch.trim()) return

      if (resetPage) {
        currentPage.value = 1
      }

      isLoading.value = true

      // 检查缓存
      const cacheKey = getCacheKey(queryToSearch, filters.value, currentPage.value)
      const cachedResult = getFromCache(cacheKey)
      
      if (cachedResult) {
        if (resetPage) {
          results.value = cachedResult
        } else {
          // 追加结果（分页加载）
          if (results.value) {
            results.value.music.push(...cachedResult.music)
            results.value.playlists.push(...cachedResult.playlists)
            results.value.users.push(...cachedResult.users)
          } else {
            results.value = cachedResult
          }
        }
        return
      }

      // 构建搜索参数
      const searchParams: SearchQuery = {
        query: queryToSearch,
        filters: filters.value,
        page: currentPage.value,
        limit: 20
      }

      const response = await post('/search', searchParams)
      
      if (response.success) {
        const searchResult = response.data as SearchResult
        
        // 保存到缓存
        saveToCache(cacheKey, searchResult)
        
        if (resetPage) {
          results.value = searchResult
          // 添加到搜索历史
          addToHistory(queryToSearch, totalResultsCount.value)
        } else {
          // 追加结果（分页加载）
          if (results.value) {
            results.value.music.push(...searchResult.music)
            results.value.playlists.push(...searchResult.playlists)
            results.value.users.push(...searchResult.users)
          } else {
            results.value = searchResult
          }
        }

        // 更新分页信息
        hasMore.value = response.pagination?.hasMore || false
        totalResults.value = response.pagination?.total || 0
        
        // 更新查询状态
        query.value = queryToSearch
      }
    } catch (error) {
      console.error('搜索错误:', error)
      throw error
    } finally {
      isLoading.value = false
      // 清理过期缓存
      cleanExpiredCache()
    }
  }

  // 加载更多结果
  const loadMore = async () => {
    if (!hasMore.value || isLoading.value) return
    
    currentPage.value += 1
    await search(query.value, false)
  }

  // 获取搜索建议
  const getSuggestions = async (searchQuery: string) => {
    try {
      if (!searchQuery.trim()) {
        suggestions.value = []
        return
      }

      const response = await get('/search/suggestions', {
        query: searchQuery,
        limit: 10
      })
      
      if (response.success) {
        suggestions.value = response.data
      }
    } catch (error) {
      console.error('获取搜索建议错误:', error)
      suggestions.value = []
    }
  }

  // 获取热门搜索
  const getHotSearches = async () => {
    try {
      const response = await get('/search/hot')
      
      if (response.success) {
        hotSearches.value = response.data
      }
    } catch (error) {
      console.error('获取热门搜索错误:', error)
    }
  }

  // 添加到搜索历史
  const addToHistory = (searchQuery: string, resultCount: number) => {
    const existingIndex = history.value.findIndex(item => item.query === searchQuery)
    
    const historyItem: SearchHistory = {
      id: Date.now().toString(),
      query: searchQuery,
      timestamp: new Date().toISOString(),
      resultCount
    }

    if (existingIndex >= 0) {
      // 更新现有记录并移到最前面
      history.value.splice(existingIndex, 1)
    }
    
    history.value.unshift(historyItem)
    
    // 限制历史记录数量
    if (history.value.length > 20) {
      history.value = history.value.slice(0, 20)
    }

    // 保存到localStorage
    saveHistoryToStorage()
  }

  // 从localStorage加载搜索历史
  const loadHistoryFromStorage = () => {
    if (process.client) {
      try {
        const stored = localStorage.getItem('search-history')
        if (stored) {
          history.value = JSON.parse(stored)
        }
      } catch (error) {
        console.error('加载搜索历史错误:', error)
      }
    }
  }

  // 保存搜索历史到localStorage
  const saveHistoryToStorage = () => {
    if (process.client) {
      try {
        localStorage.setItem('search-history', JSON.stringify(history.value))
      } catch (error) {
        console.error('保存搜索历史错误:', error)
      }
    }
  }

  // 清除搜索历史
  const clearHistory = () => {
    history.value = []
    if (process.client) {
      localStorage.removeItem('search-history')
    }
  }

  // 删除单个历史记录
  const removeHistoryItem = (id: string) => {
    history.value = history.value.filter(item => item.id !== id)
    saveHistoryToStorage()
  }

  // 更新筛选条件
  const updateFilters = (newFilters: Partial<SearchFilters>) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  // 重置筛选条件
  const resetFilters = () => {
    filters.value = {
      type: 'all',
      sortBy: 'relevance',
      dateRange: 'all',
      quality: 'all'
    }
  }

  // 清除搜索结果
  const clearResults = () => {
    results.value = null
    query.value = ''
    currentPage.value = 1
    hasMore.value = false
    totalResults.value = 0
  }

  // 清除缓存
  const clearCache = () => {
    cache.value.clear()
  }

  // 初始化
  const init = () => {
    loadHistoryFromStorage()
    getHotSearches()
  }

  return {
    // 状态
    query: readonly(query),
    results: readonly(results),
    suggestions: readonly(suggestions),
    history: readonly(history),
    hotSearches: readonly(hotSearches),
    filters: readonly(filters),
    isLoading: readonly(isLoading),
    hasMore: readonly(hasMore),
    currentPage: readonly(currentPage),
    totalResults: readonly(totalResults),
    
    // 计算属性
    hasResults,
    totalResultsCount,
    
    // 方法
    search,
    loadMore,
    getSuggestions,
    getHotSearches,
    addToHistory,
    clearHistory,
    removeHistoryItem,
    updateFilters,
    resetFilters,
    clearResults,
    clearCache,
    init
  }
})
