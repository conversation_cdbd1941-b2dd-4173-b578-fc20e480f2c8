import { defineStore } from 'pinia'
import type { 
  Music, 
  Playlist, 
  User, 
  RecommendationCategory,
  RecommendationSettings,
  UserPreferences 
} from '~/types'

interface RecommendationState {
  // 推荐内容
  forYou: Music[]
  newReleases: Music[]
  trending: Music[]
  similarArtists: User[]
  recommendedPlaylists: Playlist[]
  
  // 分类推荐
  categories: RecommendationCategory[]
  categoryContent: Record<string, Music[]>
  
  // 用户偏好
  preferences: UserPreferences | null
  settings: RecommendationSettings
  
  // 状态管理
  isLoading: boolean
  lastUpdated: string | null
  refreshInterval: number
  
  // 缓存管理
  cache: Map<string, { data: any; timestamp: number }>
}

export const useRecommendationStore = defineStore('recommendation', () => {
  // 状态
  const state = reactive<RecommendationState>({
    forYou: [],
    newReleases: [],
    trending: [],
    similarArtists: [],
    recommendedPlaylists: [],
    categories: [],
    categoryContent: {},
    preferences: null,
    settings: {
      enablePersonalization: true,
      includeExplicit: false,
      preferredGenres: [],
      excludedGenres: [],
      discoveryLevel: 'balanced', // conservative, balanced, adventurous
      refreshFrequency: 'daily' // hourly, daily, weekly
    },
    isLoading: false,
    lastUpdated: null,
    refreshInterval: 24 * 60 * 60 * 1000, // 24小时
    cache: new Map()
  })

  // Getters
  const hasRecommendations = computed(() => {
    return state.forYou.length > 0 || 
           state.newReleases.length > 0 || 
           state.trending.length > 0
  })

  const needsRefresh = computed(() => {
    if (!state.lastUpdated) return true
    const lastUpdate = new Date(state.lastUpdated).getTime()
    return Date.now() - lastUpdate > state.refreshInterval
  })

  const personalizedCategories = computed(() => {
    return state.categories.filter(cat => cat.isPersonalized)
  })

  const generalCategories = computed(() => {
    return state.categories.filter(cat => !cat.isPersonalized)
  })

  // 缓存管理
  const CACHE_DURATION = 30 * 60 * 1000 // 30分钟缓存

  const getCachedData = (key: string) => {
    const cached = state.cache.get(key)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data
    }
    return null
  }

  const setCachedData = (key: string, data: any) => {
    state.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  // Actions
  const initializeRecommendations = async () => {
    if (state.isLoading) return
    
    state.isLoading = true
    try {
      // 并行加载基础推荐数据
      await Promise.all([
        loadForYouRecommendations(),
        loadNewReleases(),
        loadTrendingMusic(),
        loadRecommendationCategories()
      ])
      
      state.lastUpdated = new Date().toISOString()
    } catch (error) {
      console.error('初始化推荐失败:', error)
      throw error
    } finally {
      state.isLoading = false
    }
  }

  const loadForYouRecommendations = async () => {
    const cacheKey = 'for-you-recommendations'
    const cached = getCachedData(cacheKey)
    if (cached) {
      state.forYou = cached
      return
    }

    try {
      const { getPersonalizedRecommendations } = useRecommendationApi()
      const response = await getPersonalizedRecommendations({
        limit: 20,
        includeExplicit: state.settings.includeExplicit,
        genres: state.settings.preferredGenres,
        excludeGenres: state.settings.excludedGenres
      })
      
      if (response.success) {
        state.forYou = response.data
        setCachedData(cacheKey, response.data)
      }
    } catch (error) {
      console.error('加载个性化推荐失败:', error)
    }
  }

  const loadNewReleases = async () => {
    const cacheKey = 'new-releases'
    const cached = getCachedData(cacheKey)
    if (cached) {
      state.newReleases = cached
      return
    }

    try {
      const { getNewReleases } = useRecommendationApi()
      const response = await getNewReleases({
        limit: 20,
        genres: state.settings.preferredGenres.length > 0 ? state.settings.preferredGenres : undefined
      })
      
      if (response.success) {
        state.newReleases = response.data
        setCachedData(cacheKey, response.data)
      }
    } catch (error) {
      console.error('加载新发布音乐失败:', error)
    }
  }

  const loadTrendingMusic = async () => {
    const cacheKey = 'trending-music'
    const cached = getCachedData(cacheKey)
    if (cached) {
      state.trending = cached
      return
    }

    try {
      const { getTrendingMusic } = useRecommendationApi()
      const response = await getTrendingMusic({
        limit: 20,
        timeRange: 'week' // day, week, month
      })
      
      if (response.success) {
        state.trending = response.data
        setCachedData(cacheKey, response.data)
      }
    } catch (error) {
      console.error('加载热门音乐失败:', error)
    }
  }

  const loadRecommendationCategories = async () => {
    const cacheKey = 'recommendation-categories'
    const cached = getCachedData(cacheKey)
    if (cached) {
      state.categories = cached
      return
    }

    try {
      const { getRecommendationCategories } = useRecommendationApi()
      const response = await getRecommendationCategories()
      
      if (response.success) {
        state.categories = response.data
        setCachedData(cacheKey, response.data)
      }
    } catch (error) {
      console.error('加载推荐分类失败:', error)
    }
  }

  const loadCategoryContent = async (categoryId: string, refresh = false) => {
    const cacheKey = `category-${categoryId}`
    
    if (!refresh) {
      const cached = getCachedData(cacheKey)
      if (cached) {
        state.categoryContent[categoryId] = cached
        return
      }
    }

    try {
      const { getCategoryRecommendations } = useRecommendationApi()
      const response = await getCategoryRecommendations(categoryId, {
        limit: 20,
        personalized: state.settings.enablePersonalization
      })
      
      if (response.success) {
        state.categoryContent[categoryId] = response.data
        setCachedData(cacheKey, response.data)
      }
    } catch (error) {
      console.error(`加载分类 ${categoryId} 内容失败:`, error)
    }
  }

  const loadSimilarArtists = async (artistId?: string) => {
    const cacheKey = `similar-artists-${artistId || 'general'}`
    const cached = getCachedData(cacheKey)
    if (cached) {
      state.similarArtists = cached
      return
    }

    try {
      const { getSimilarArtists } = useRecommendationApi()
      const response = await getSimilarArtists(artistId, { limit: 10 })
      
      if (response.success) {
        state.similarArtists = response.data
        setCachedData(cacheKey, response.data)
      }
    } catch (error) {
      console.error('加载相似艺术家失败:', error)
    }
  }

  const loadRecommendedPlaylists = async () => {
    const cacheKey = 'recommended-playlists'
    const cached = getCachedData(cacheKey)
    if (cached) {
      state.recommendedPlaylists = cached
      return
    }

    try {
      const { getRecommendedPlaylists } = useRecommendationApi()
      const response = await getRecommendedPlaylists({
        limit: 15,
        personalized: state.settings.enablePersonalization
      })
      
      if (response.success) {
        state.recommendedPlaylists = response.data
        setCachedData(cacheKey, response.data)
      }
    } catch (error) {
      console.error('加载推荐歌单失败:', error)
    }
  }

  const updatePreferences = async (preferences: Partial<UserPreferences>) => {
    try {
      const { updateUserPreferences } = useRecommendationApi()
      const response = await updateUserPreferences(preferences)
      
      if (response.success) {
        state.preferences = { ...state.preferences, ...preferences }
        // 清除相关缓存，触发重新加载
        clearRecommendationCache()
        await initializeRecommendations()
      }
    } catch (error) {
      console.error('更新用户偏好失败:', error)
      throw error
    }
  }

  const updateSettings = async (settings: Partial<RecommendationSettings>) => {
    state.settings = { ...state.settings, ...settings }
    
    // 保存到本地存储
    if (process.client) {
      localStorage.setItem('recommendation-settings', JSON.stringify(state.settings))
    }
    
    // 如果是重要设置变更，重新加载推荐
    const importantKeys = ['enablePersonalization', 'preferredGenres', 'excludedGenres', 'includeExplicit']
    const hasImportantChanges = Object.keys(settings).some(key => importantKeys.includes(key))
    
    if (hasImportantChanges) {
      clearRecommendationCache()
      await initializeRecommendations()
    }
  }

  const refreshRecommendations = async (force = false) => {
    if (!force && !needsRefresh.value) return
    
    clearRecommendationCache()
    await initializeRecommendations()
  }

  const clearRecommendationCache = () => {
    state.cache.clear()
  }

  const addToPreferredGenres = (genre: string) => {
    if (!state.settings.preferredGenres.includes(genre)) {
      state.settings.preferredGenres.push(genre)
      updateSettings({ preferredGenres: state.settings.preferredGenres })
    }
  }

  const removeFromPreferredGenres = (genre: string) => {
    const index = state.settings.preferredGenres.indexOf(genre)
    if (index > -1) {
      state.settings.preferredGenres.splice(index, 1)
      updateSettings({ preferredGenres: state.settings.preferredGenres })
    }
  }

  const addToExcludedGenres = (genre: string) => {
    if (!state.settings.excludedGenres.includes(genre)) {
      state.settings.excludedGenres.push(genre)
      updateSettings({ excludedGenres: state.settings.excludedGenres })
    }
  }

  const removeFromExcludedGenres = (genre: string) => {
    const index = state.settings.excludedGenres.indexOf(genre)
    if (index > -1) {
      state.settings.excludedGenres.splice(index, 1)
      updateSettings({ excludedGenres: state.settings.excludedGenres })
    }
  }

  // 初始化设置
  const initializeSettings = () => {
    if (process.client) {
      const saved = localStorage.getItem('recommendation-settings')
      if (saved) {
        try {
          const settings = JSON.parse(saved)
          state.settings = { ...state.settings, ...settings }
        } catch (error) {
          console.error('解析推荐设置失败:', error)
        }
      }
    }
  }

  // 自动刷新
  let refreshTimer: NodeJS.Timeout | null = null

  const startAutoRefresh = () => {
    if (refreshTimer) clearInterval(refreshTimer)
    
    const interval = state.settings.refreshFrequency === 'hourly' ? 60 * 60 * 1000 :
                    state.settings.refreshFrequency === 'daily' ? 24 * 60 * 60 * 1000 :
                    7 * 24 * 60 * 60 * 1000 // weekly
    
    refreshTimer = setInterval(() => {
      refreshRecommendations()
    }, interval)
  }

  const stopAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }

  // 导出状态和方法
  return {
    // 状态
    ...toRefs(state),
    
    // Getters
    hasRecommendations,
    needsRefresh,
    personalizedCategories,
    generalCategories,
    
    // Actions
    initializeRecommendations,
    loadForYouRecommendations,
    loadNewReleases,
    loadTrendingMusic,
    loadRecommendationCategories,
    loadCategoryContent,
    loadSimilarArtists,
    loadRecommendedPlaylists,
    updatePreferences,
    updateSettings,
    refreshRecommendations,
    clearRecommendationCache,
    addToPreferredGenres,
    removeFromPreferredGenres,
    addToExcludedGenres,
    removeFromExcludedGenres,
    initializeSettings,
    startAutoRefresh,
    stopAutoRefresh
  }
})
