<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="@localhost" uuid="ff41a1cf-da99-44ae-a2e8-4a5c96c0fa55">
      <driver-ref>mongo.4</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.dbschema.MongoJdbcDriver</jdbc-driver>
      <jdbc-url>mongodb://localhost:27017</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="0@localhost" uuid="c299a49a-658c-4894-ac0d-560dd6a5a9dc">
      <driver-ref>redis</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>jdbc.RedisDriver</jdbc-driver>
      <jdbc-url>*****************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>