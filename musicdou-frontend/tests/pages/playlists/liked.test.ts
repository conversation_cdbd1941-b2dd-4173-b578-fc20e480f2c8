import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'

// Mock composables
const mockSocialApi = {
  getUserLikes: vi.fn(),
  unlikeTarget: vi.fn()
}

const mockNotification = {
  success: vi.fn(),
  error: vi.fn(),
  info: vi.fn()
}

const mockPlayerStore = {
  playTrack: vi.fn(),
  toggleShuffle: vi.fn()
}

const mockAuthStore = {
  user: {
    id: 'user123',
    username: 'testuser'
  }
}

// Mock global functions
global.useSocialApi = vi.fn(() => mockSocialApi)
global.useNotification = vi.fn(() => mockNotification)
global.usePlayerStore = vi.fn(() => mockPlayerStore)
global.useAuthStore = vi.fn(() => mockAuthStore)
global.navigateTo = vi.fn()
global.useHead = vi.fn()
global.definePageMeta = vi.fn()
global.onMounted = vi.fn()

// Mock components
const MockIcon = {
  name: 'Icon',
  template: '<span>{{ name }}</span>',
  props: ['name']
}

const MockButton = {
  name: 'Button',
  template: '<button><slot /></button>',
  props: ['size', 'variant', 'disabled']
}

const MockCard = {
  name: 'Card',
  template: '<div><slot /></div>',
  props: ['class']
}

describe('Liked Music Page', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('应该正确渲染页面标题', async () => {
    // Mock successful API response
    mockSocialApi.getUserLikes.mockResolvedValue({
      success: true,
      data: {
        likes: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          pages: 0
        }
      }
    })

    const LikedPage = await import('../../../app/pages/playlists/liked.vue')
    
    const wrapper = mount(LikedPage.default, {
      global: {
        components: {
          Icon: MockIcon,
          Button: MockButton,
          Card: MockCard
        }
      }
    })

    expect(wrapper.find('h1').text()).toBe('我喜欢的音乐')
  })

  it('应该在加载时显示加载状态', async () => {
    // Mock pending API response
    mockSocialApi.getUserLikes.mockImplementation(() => new Promise(() => {}))

    const LikedPage = await import('../../../app/pages/playlists/liked.vue')
    
    const wrapper = mount(LikedPage.default, {
      global: {
        components: {
          Icon: MockIcon,
          Button: MockButton,
          Card: MockCard
        }
      }
    })

    // 应该显示加载状态
    expect(wrapper.find('.animate-spin').exists()).toBe(true)
  })

  it('应该在没有喜欢的音乐时显示空状态', async () => {
    mockSocialApi.getUserLikes.mockResolvedValue({
      success: true,
      data: {
        likes: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          pages: 0
        }
      }
    })

    const LikedPage = await import('../../../app/pages/playlists/liked.vue')
    
    const wrapper = mount(LikedPage.default, {
      global: {
        components: {
          Icon: MockIcon,
          Button: MockButton,
          Card: MockCard
        }
      }
    })

    // 等待组件更新
    await wrapper.vm.$nextTick()
    
    expect(wrapper.text()).toContain('还没有喜欢的音乐')
  })

  it('应该正确显示喜欢的音乐列表', async () => {
    const mockLikes = [
      {
        id: 'like1',
        target: {
          id: 'music1',
          title: '测试歌曲1',
          artist: '测试艺术家1',
          album: '测试专辑1',
          duration: 180,
          coverUrl: 'http://example.com/cover1.jpg'
        },
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 'like2',
        target: {
          id: 'music2',
          title: '测试歌曲2',
          artist: '测试艺术家2',
          album: '测试专辑2',
          duration: 240,
          coverUrl: 'http://example.com/cover2.jpg'
        },
        createdAt: '2024-01-02T00:00:00Z'
      }
    ]

    mockSocialApi.getUserLikes.mockResolvedValue({
      success: true,
      data: {
        likes: mockLikes,
        pagination: {
          page: 1,
          limit: 20,
          total: 2,
          pages: 1
        }
      }
    })

    const LikedPage = await import('../../../app/pages/playlists/liked.vue')
    
    const wrapper = mount(LikedPage.default, {
      global: {
        components: {
          Icon: MockIcon,
          Button: MockButton,
          Card: MockCard
        }
      }
    })

    // 等待组件更新
    await wrapper.vm.$nextTick()
    
    expect(wrapper.text()).toContain('测试歌曲1')
    expect(wrapper.text()).toContain('测试艺术家1')
    expect(wrapper.text()).toContain('2 首歌曲')
  })

  it('应该在API错误时显示错误状态', async () => {
    mockSocialApi.getUserLikes.mockRejectedValue(new Error('API错误'))

    const LikedPage = await import('../../../app/pages/playlists/liked.vue')
    
    const wrapper = mount(LikedPage.default, {
      global: {
        components: {
          Icon: MockIcon,
          Button: MockButton,
          Card: MockCard
        }
      }
    })

    // 等待组件更新
    await wrapper.vm.$nextTick()
    
    expect(wrapper.text()).toContain('加载失败')
    expect(wrapper.text()).toContain('API错误')
  })
})
