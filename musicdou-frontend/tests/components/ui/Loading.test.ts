import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import Loading from '~/components/ui/Loading.vue'

describe('Loading', () => {
  describe('基础渲染', () => {
    it('应该正确渲染默认加载组件', () => {
      const wrapper = mount(Loading)

      expect(wrapper.find('div').exists()).toBe(true)
      expect(wrapper.find('svg').exists()).toBe(true) // 默认是spinner类型
    })

    it('应该渲染加载文本', () => {
      const wrapper = mount(Loading, {
        props: {
          text: 'Loading...'
        }
      })

      expect(wrapper.find('p').text()).toBe('Loading...')
    })

    it('应该在没有文本时不渲染文本元素', () => {
      const wrapper = mount(Loading)

      expect(wrapper.find('p').exists()).toBe(false)
    })
  })

  describe('加载类型', () => {
    it('应该渲染spinner类型', () => {
      const wrapper = mount(Loading, {
        props: { type: 'spinner' }
      })

      expect(wrapper.find('svg').exists()).toBe(true)
      expect(wrapper.find('svg').classes()).toContain('animate-spin')
    })

    it('应该渲染dots类型', () => {
      const wrapper = mount(Loading, {
        props: { type: 'dots' }
      })

      expect(wrapper.find('svg').exists()).toBe(false)
      const dots = wrapper.findAll('div div div')
      expect(dots.length).toBe(3) // 3个点
    })

    it('应该渲染bars类型', () => {
      const wrapper = mount(Loading, {
        props: { type: 'bars' }
      })

      expect(wrapper.find('svg').exists()).toBe(false)
      const bars = wrapper.findAll('div div div')
      expect(bars.length).toBe(4) // 4个条
    })

    it('应该渲染pulse类型', () => {
      const wrapper = mount(Loading, {
        props: { type: 'pulse' }
      })

      expect(wrapper.find('svg').exists()).toBe(false)
      // pulse类型有一个圆形元素
      const pulseElements = wrapper.findAll('div div div')
      expect(pulseElements.length).toBe(1)
    })
  })

  describe('尺寸变体', () => {
    const sizeTests = [
      { size: 'sm', expectedClass: 'w-4' },
      { size: 'md', expectedClass: 'w-6' },
      { size: 'lg', expectedClass: 'w-8' },
      { size: 'xl', expectedClass: 'w-12' }
    ]

    sizeTests.forEach(({ size, expectedClass }) => {
      it(`应该应用${size}尺寸样式`, () => {
        const wrapper = mount(Loading, {
          props: {
            type: 'spinner',
            size: size as any
          }
        })

        const svg = wrapper.find('svg')
        expect(svg.classes()).toContain(expectedClass)
      })
    })
  })

  describe('颜色变体', () => {
    const colorTests = [
      { color: 'primary', expectedClass: 'text-primary-500' },
      { color: 'secondary', expectedClass: 'text-secondary-500' },
      { color: 'gray', expectedClass: 'text-gray-500' },
      { color: 'white', expectedClass: 'text-white' }
    ]

    colorTests.forEach(({ color, expectedClass }) => {
      it(`应该应用${color}颜色样式`, () => {
        const wrapper = mount(Loading, {
          props: {
            type: 'spinner',
            color: color as any
          }
        })

        const spinnerDiv = wrapper.find('div[class*="text-"]')
        expect(spinnerDiv.classes()).toContain(expectedClass)
      })
    })
  })

  describe('覆盖层模式', () => {
    it('应该在overlay为true时应用覆盖层样式', () => {
      const wrapper = mount(Loading, {
        props: {
          overlay: true
        }
      })

      const container = wrapper.find('div')
      expect(container.classes()).toContain('absolute')
      expect(container.classes()).toContain('inset-0')
      expect(container.classes()).toContain('bg-white/80')
      expect(container.classes()).toContain('backdrop-blur-sm')
      expect(container.classes()).toContain('z-50')
    })

    it('应该在overlay为false时不应用覆盖层样式', () => {
      const wrapper = mount(Loading, {
        props: {
          overlay: false
        }
      })

      const container = wrapper.find('div')
      expect(container.classes()).not.toContain('absolute')
      expect(container.classes()).not.toContain('inset-0')
    })
  })

  describe('全屏模式', () => {
    it('应该在fullscreen为true时应用全屏样式', () => {
      const wrapper = mount(Loading, {
        props: {
          fullscreen: true
        }
      })

      const container = wrapper.find('div')
      expect(container.classes()).toContain('fixed')
      expect(container.classes()).toContain('inset-0')
      expect(container.classes()).toContain('bg-white/90')
      expect(container.classes()).toContain('z-50')
    })

    it('应该在fullscreen为false时不应用全屏样式', () => {
      const wrapper = mount(Loading, {
        props: {
          fullscreen: false
        }
      })

      const container = wrapper.find('div')
      expect(container.classes()).not.toContain('fixed')
    })
  })

  describe('组合模式', () => {
    it('应该同时支持overlay和fullscreen', () => {
      const wrapper = mount(Loading, {
        props: {
          overlay: true,
          fullscreen: true
        }
      })

      const container = wrapper.find('div')
      expect(container.classes()).toContain('fixed')
      expect(container.classes()).toContain('inset-0')
      expect(container.classes()).toContain('z-50')
    })

    it('应该支持自定义文本和类型组合', () => {
      const wrapper = mount(Loading, {
        props: {
          type: 'dots',
          text: 'Please wait...',
          color: 'primary',
          size: 'lg'
        }
      })

      expect(wrapper.find('p').text()).toBe('Please wait...')
      const dots = wrapper.findAll('div div div')
      expect(dots.length).toBe(3)
    })
  })

  describe('动画延迟', () => {
    it('应该为dots类型的每个点设置不同的动画延迟', () => {
      const wrapper = mount(Loading, {
        props: { type: 'dots' }
      })

      const dots = wrapper.findAll('div div div')
      expect(dots[0].attributes('style')).toContain('animation-delay: 0s')
      expect(dots[1].attributes('style')).toContain('animation-delay: 0.2s')
      expect(dots[2].attributes('style')).toContain('animation-delay: 0.4s')
    })

    it('应该为bars类型的每个条设置不同的动画延迟', () => {
      const wrapper = mount(Loading, {
        props: { type: 'bars' }
      })

      const bars = wrapper.findAll('div div div')
      expect(bars[0].attributes('style')).toContain('animation-delay: 0s')
      expect(bars[1].attributes('style')).toContain('animation-delay: 0.1s')
      expect(bars[2].attributes('style')).toContain('animation-delay: 0.2s')
      expect(bars[3].attributes('style')).toContain('animation-delay: 0.3')
    })
  })

  describe('默认属性', () => {
    it('应该使用正确的默认属性值', () => {
      const wrapper = mount(Loading)

      // 检查默认类型 (spinner)
      expect(wrapper.find('svg').exists()).toBe(true)
      
      // 检查默认尺寸 (md)
      expect(wrapper.find('svg').classes()).toContain('w-6')
      
      // 检查默认颜色 (primary)
      const spinnerDiv = wrapper.find('div[class*="text-"]')
      expect(spinnerDiv.classes()).toContain('text-primary-500')
      
      // 检查默认不是overlay模式
      const container = wrapper.find('div')
      expect(container.classes()).not.toContain('absolute')
      
      // 检查默认不是fullscreen模式
      expect(container.classes()).not.toContain('fixed')
    })
  })

  describe('文本样式', () => {
    it('应该应用正确的文本样式', () => {
      const wrapper = mount(Loading, {
        props: {
          text: 'Loading...',
          color: 'primary'
        }
      })

      const textElement = wrapper.find('p')
      expect(textElement.classes()).toContain('text-sm')
      expect(textElement.classes()).toContain('font-medium')
      expect(textElement.classes()).toContain('text-gray-600')
    })

    it('应该应用固定的文本颜色样式', () => {
      const wrapper = mount(Loading, {
        props: {
          text: 'Loading...',
          color: 'primary'
        }
      })

      const textElement = wrapper.find('p')
      // 文本颜色是固定的，不受color属性影响
      expect(textElement.classes()).toContain('text-gray-600')
      expect(textElement.classes()).toContain('dark:text-gray-400')
    })
  })
})
