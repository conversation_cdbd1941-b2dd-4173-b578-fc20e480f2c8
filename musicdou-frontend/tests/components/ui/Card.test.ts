import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import Card from '~/components/ui/Card.vue'

describe('Card', () => {
  describe('基础渲染', () => {
    it('应该正确渲染基础卡片', () => {
      const wrapper = mount(Card, {
        slots: {
          default: '<p>Card content</p>'
        }
      })

      expect(wrapper.find('div').exists()).toBe(true)
      expect(wrapper.text()).toContain('Card content')
    })

    it('应该渲染标题', () => {
      const wrapper = mount(Card, {
        props: {
          title: 'Test Title'
        },
        slots: {
          default: '<p>Content</p>'
        }
      })

      expect(wrapper.find('h3').text()).toBe('Test Title')
    })

    it('应该渲染自定义头部插槽', () => {
      const wrapper = mount(Card, {
        slots: {
          header: '<div class="custom-header">Custom Header</div>',
          default: '<p>Content</p>'
        }
      })

      expect(wrapper.find('.custom-header').exists()).toBe(true)
      expect(wrapper.text()).toContain('Custom Header')
    })

    it('应该渲染操作插槽', () => {
      const wrapper = mount(Card, {
        props: {
          title: 'Test Title'
        },
        slots: {
          actions: '<button class="action-btn">Action</button>',
          default: '<p>Content</p>'
        }
      })

      expect(wrapper.find('.action-btn').exists()).toBe(true)
      expect(wrapper.find('.action-btn').text()).toBe('Action')
    })

    it('应该渲染底部插槽', () => {
      const wrapper = mount(Card, {
        slots: {
          default: '<p>Content</p>',
          footer: '<div class="footer">Footer content</div>'
        }
      })

      expect(wrapper.find('.footer').exists()).toBe(true)
      expect(wrapper.text()).toContain('Footer content')
    })
  })

  describe('变体样式', () => {
    it('应该应用默认变体样式', () => {
      const wrapper = mount(Card, {
        props: { variant: 'default' },
        slots: { default: '<p>Content</p>' }
      })

      const cardElement = wrapper.find('div')
      expect(cardElement.classes()).toContain('bg-white')
      expect(cardElement.classes()).toContain('dark:bg-gray-800')
    })

    it('应该应用outlined变体样式', () => {
      const wrapper = mount(Card, {
        props: { variant: 'outlined' },
        slots: { default: '<p>Content</p>' }
      })

      const cardElement = wrapper.find('div')
      expect(cardElement.classes()).toContain('bg-transparent')
      expect(cardElement.classes()).toContain('border-2')
      expect(cardElement.classes()).toContain('border-gray-200')
    })

    it('应该应用elevated变体样式', () => {
      const wrapper = mount(Card, {
        props: { variant: 'elevated' },
        slots: { default: '<p>Content</p>' }
      })

      const cardElement = wrapper.find('div')
      expect(cardElement.classes()).toContain('bg-white')
      expect(cardElement.classes()).toContain('shadow-lg')
      expect(cardElement.classes()).toContain('border-0')
    })

    it('应该应用filled变体样式', () => {
      const wrapper = mount(Card, {
        props: { variant: 'filled' },
        slots: { default: '<p>Content</p>' }
      })

      const cardElement = wrapper.find('div')
      expect(cardElement.classes()).toContain('bg-gray-50')
      expect(cardElement.classes()).toContain('dark:bg-gray-900')
      expect(cardElement.classes()).toContain('border-0')
    })
  })

  describe('尺寸和样式属性', () => {
    it('应该应用不同的内边距', () => {
      const paddingTests = [
        { padding: 'none', expected: [] },
        { padding: 'sm', expected: ['p-3'] },
        { padding: 'md', expected: ['p-4'] },
        { padding: 'lg', expected: ['p-6'] },
        { padding: 'xl', expected: ['p-8'] }
      ]

      paddingTests.forEach(({ padding, expected }) => {
        const wrapper = mount(Card, {
          props: { padding: padding as any },
          slots: { default: '<p>Content</p>' }
        })

        // 查找body元素 - 包含slot内容的div
        const bodyElements = wrapper.findAll('div')
        // body元素应该是最后一个div（如果没有footer）
        const bodyElement = bodyElements[bodyElements.length - 1]

        if (expected.length > 0) {
          expected.forEach(cls => {
            expect(bodyElement.classes()).toContain(cls)
          })
        } else {
          // 对于 'none' padding，检查没有padding类
          expect(bodyElement.classes().some(cls => cls.startsWith('p-'))).toBe(false)
        }
      })
    })

    it('应该应用不同的圆角', () => {
      const roundedTests = [
        { rounded: 'none', expected: [] },
        { rounded: 'sm', expected: ['rounded-sm'] },
        { rounded: 'md', expected: ['rounded-md'] },
        { rounded: 'lg', expected: ['rounded-lg'] },
        { rounded: 'xl', expected: ['rounded-xl'] },
        { rounded: 'full', expected: ['rounded-full'] }
      ]

      roundedTests.forEach(({ rounded, expected }) => {
        const wrapper = mount(Card, {
          props: { rounded: rounded as any },
          slots: { default: '<p>Content</p>' }
        })

        const cardElement = wrapper.find('div')
        if (expected.length > 0) {
          expected.forEach(cls => {
            expect(cardElement.classes()).toContain(cls)
          })
        }
      })
    })

    it('应该应用不同的阴影', () => {
      const shadowTests = [
        { shadow: 'none', expected: [] },
        { shadow: 'sm', expected: ['shadow-sm'] },
        { shadow: 'md', expected: ['shadow-md'] },
        { shadow: 'lg', expected: ['shadow-lg'] },
        { shadow: 'xl', expected: ['shadow-xl'] }
      ]

      shadowTests.forEach(({ shadow, expected }) => {
        const wrapper = mount(Card, {
          props: { shadow: shadow as any },
          slots: { default: '<p>Content</p>' }
        })

        const cardElement = wrapper.find('div')
        if (expected.length > 0) {
          expected.forEach(cls => {
            expect(cardElement.classes()).toContain(cls)
          })
        }
      })
    })
  })

  describe('交互行为', () => {
    it('应该应用悬停效果', () => {
      const wrapper = mount(Card, {
        props: { hover: true },
        slots: { default: '<p>Content</p>' }
      })

      const cardElement = wrapper.find('div')
      expect(cardElement.classes()).toContain('hover:shadow-md')
      expect(cardElement.classes()).toContain('hover:-translate-y-1')
      expect(cardElement.classes()).toContain('hover:scale-[1.02]')
    })

    it('应该应用可点击样式', () => {
      const wrapper = mount(Card, {
        props: { clickable: true },
        slots: { default: '<p>Content</p>' }
      })

      const cardElement = wrapper.find('div')
      expect(cardElement.classes()).toContain('cursor-pointer')
      expect(cardElement.classes()).toContain('hover:shadow-lg')
      expect(cardElement.classes()).toContain('active:scale-[0.98]')
    })

    it('应该在可点击时触发点击事件', async () => {
      const wrapper = mount(Card, {
        props: { clickable: true },
        slots: { default: '<p>Content</p>' }
      })

      await wrapper.find('div').trigger('click')
      expect(wrapper.emitted('click')).toBeTruthy()
      expect(wrapper.emitted('click')).toHaveLength(1)
    })

    it('应该在不可点击时不触发点击事件', async () => {
      const wrapper = mount(Card, {
        props: { clickable: false },
        slots: { default: '<p>Content</p>' }
      })

      await wrapper.find('div').trigger('click')
      expect(wrapper.emitted('click')).toBeFalsy()
    })
  })

  describe('边框样式', () => {
    it('应该在默认变体且bordered为true时显示边框', () => {
      const wrapper = mount(Card, {
        props: { variant: 'default', bordered: true },
        slots: { default: '<p>Content</p>' }
      })

      const cardElement = wrapper.find('div')
      expect(cardElement.classes()).toContain('border')
      expect(cardElement.classes()).toContain('border-gray-200')
    })

    it('应该在bordered为false时不显示边框', () => {
      const wrapper = mount(Card, {
        props: { variant: 'default', bordered: false },
        slots: { default: '<p>Content</p>' }
      })

      const cardElement = wrapper.find('div')
      expect(cardElement.classes()).not.toContain('border')
    })

    it('应该在非默认变体时不显示边框', () => {
      const wrapper = mount(Card, {
        props: { variant: 'outlined', bordered: true },
        slots: { default: '<p>Content</p>' }
      })

      const cardElement = wrapper.find('div')
      // outlined变体有自己的border-2样式，但不应该有默认的border样式
      expect(cardElement.classes()).toContain('border-2')
      expect(cardElement.classes()).not.toContain('border')
    })
  })

  describe('默认属性', () => {
    it('应该使用正确的默认属性值', () => {
      const wrapper = mount(Card, {
        slots: { default: '<p>Content</p>' }
      })

      const cardElement = wrapper.find('div')
      
      // 检查默认变体
      expect(cardElement.classes()).toContain('bg-white')
      
      // 检查默认圆角
      expect(cardElement.classes()).toContain('rounded-lg')
      
      // 检查默认阴影
      expect(cardElement.classes()).toContain('shadow-sm')
      
      // 检查默认边框
      expect(cardElement.classes()).toContain('border')
    })
  })
})
