import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import Modal from '~/components/ui/Modal.vue'

// Mock Icon component
vi.mock('~/components/ui/Icon.vue', () => ({
  default: {
    name: 'Icon',
    props: ['name', 'class'],
    template: '<span :class="$props.class">{{ name }}</span>'
  }
}))

describe('Modal', () => {
  let originalBodyOverflow: string

  beforeEach(() => {
    // 保存原始的body overflow样式
    originalBodyOverflow = document.body.style.overflow
    // 创建body元素用于Teleport
    if (!document.body) {
      document.body = document.createElement('body')
      document.documentElement.appendChild(document.body)
    }
  })

  afterEach(() => {
    // 恢复原始的body overflow样式
    document.body.style.overflow = originalBodyOverflow
    // 清理DOM
    document.body.innerHTML = ''
  })

  describe('基础渲染', () => {
    it('应该在modelValue为true时渲染模态框', async () => {
      const wrapper = mount(Modal, {
        props: {
          modelValue: true
        },
        slots: {
          default: '<p>Modal content</p>'
        },
        attachTo: document.body
      })

      await nextTick()
      expect(document.body.querySelector('.fixed.inset-0.z-50')).toBeTruthy()
      expect(document.body.textContent).toContain('Modal content')
    })

    it('应该在modelValue为false时不渲染模态框', () => {
      const wrapper = mount(Modal, {
        props: {
          modelValue: false
        },
        slots: {
          default: '<p>Modal content</p>'
        },
        attachTo: document.body
      })

      expect(document.body.querySelector('.fixed.inset-0.z-50')).toBeFalsy()
    })

    it('应该渲染标题', async () => {
      const wrapper = mount(Modal, {
        props: {
          modelValue: true,
          title: 'Test Modal'
        },
        slots: {
          default: '<p>Content</p>'
        },
        attachTo: document.body
      })

      await nextTick()
      expect(document.body.textContent).toContain('Test Modal')
    })

    it('应该渲染自定义头部插槽', async () => {
      const wrapper = mount(Modal, {
        props: {
          modelValue: true
        },
        slots: {
          header: '<div class="custom-header">Custom Header</div>',
          default: '<p>Content</p>'
        },
        attachTo: document.body
      })

      await nextTick()
      expect(document.body.querySelector('.custom-header')).toBeTruthy()
      expect(document.body.textContent).toContain('Custom Header')
    })

    it('应该渲染底部插槽', async () => {
      const wrapper = mount(Modal, {
        props: {
          modelValue: true
        },
        slots: {
          default: '<p>Content</p>',
          footer: '<div class="footer">Footer content</div>'
        },
        attachTo: document.body
      })

      await nextTick()
      expect(document.body.querySelector('.footer')).toBeTruthy()
      expect(document.body.textContent).toContain('Footer content')
    })
  })

  describe('尺寸变体', () => {
    const sizeTests = [
      { size: 'sm', expectedClass: 'max-w-md' },
      { size: 'md', expectedClass: 'max-w-lg' },
      { size: 'lg', expectedClass: 'max-w-2xl' },
      { size: 'xl', expectedClass: 'max-w-4xl' },
      { size: 'full', expectedClass: 'max-w-7xl' }
    ]

    sizeTests.forEach(({ size, expectedClass }) => {
      it(`应该应用${size}尺寸样式`, async () => {
        const wrapper = mount(Modal, {
          props: {
            modelValue: true,
            size: size as any
          },
          slots: {
            default: '<p>Content</p>'
          },
          attachTo: document.body
        })

        await nextTick()
        const modalElement = document.body.querySelector('.relative.bg-white')
        expect(modalElement?.classList.contains(expectedClass)).toBe(true)
      })
    })
  })

  describe('关闭功能', () => {
    it('应该在closable为true时显示关闭按钮', async () => {
      const wrapper = mount(Modal, {
        props: {
          modelValue: true,
          closable: true
        },
        slots: {
          default: '<p>Content</p>'
        },
        attachTo: document.body
      })

      await nextTick()
      const closeButton = document.body.querySelector('button')
      expect(closeButton).toBeTruthy()
    })

    it('应该在closable为false时不显示关闭按钮', async () => {
      const wrapper = mount(Modal, {
        props: {
          modelValue: true,
          closable: false
        },
        slots: {
          default: '<p>Content</p>'
        },
        attachTo: document.body
      })

      await nextTick()
      const closeButton = document.body.querySelector('button')
      expect(closeButton).toBeFalsy()
    })

    it('应该在点击关闭按钮时触发关闭事件', async () => {
      const wrapper = mount(Modal, {
        props: {
          modelValue: true,
          closable: true
        },
        slots: {
          default: '<p>Content</p>'
        },
        attachTo: document.body
      })

      await nextTick()
      const closeButton = document.body.querySelector('button')
      await closeButton?.dispatchEvent(new Event('click'))
      
      expect(wrapper.emitted('update:modelValue')).toBeTruthy()
      expect(wrapper.emitted('update:modelValue')?.[0]).toEqual([false])
      expect(wrapper.emitted('close')).toBeTruthy()
    })
  })

  describe('背景点击关闭', () => {
    it('应该在closeOnBackdrop为true时点击背景关闭', async () => {
      const wrapper = mount(Modal, {
        props: {
          modelValue: true,
          closeOnBackdrop: true
        },
        slots: {
          default: '<p>Content</p>'
        },
        attachTo: document.body
      })

      await nextTick()
      const backdrop = document.body.querySelector('.fixed.inset-0.z-50')
      await backdrop?.dispatchEvent(new Event('click'))
      
      expect(wrapper.emitted('update:modelValue')).toBeTruthy()
      expect(wrapper.emitted('update:modelValue')?.[0]).toEqual([false])
    })

    it('应该在closeOnBackdrop为false时点击背景不关闭', async () => {
      const wrapper = mount(Modal, {
        props: {
          modelValue: true,
          closeOnBackdrop: false
        },
        slots: {
          default: '<p>Content</p>'
        },
        attachTo: document.body
      })

      await nextTick()
      const backdrop = document.body.querySelector('.fixed.inset-0.z-50')
      await backdrop?.dispatchEvent(new Event('click'))
      
      expect(wrapper.emitted('update:modelValue')).toBeFalsy()
    })
  })

  describe('键盘事件', () => {
    it('应该在closeOnEscape为true时按ESC键关闭', async () => {
      const wrapper = mount(Modal, {
        props: {
          modelValue: true,
          closeOnEscape: true
        },
        slots: {
          default: '<p>Content</p>'
        },
        attachTo: document.body
      })

      await nextTick()
      
      // 模拟ESC键按下
      const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' })
      document.dispatchEvent(escapeEvent)
      
      await nextTick()
      expect(wrapper.emitted('update:modelValue')).toBeTruthy()
      expect(wrapper.emitted('update:modelValue')?.[0]).toEqual([false])
    })

    it('应该在closeOnEscape为false时按ESC键不关闭', async () => {
      const wrapper = mount(Modal, {
        props: {
          modelValue: true,
          closeOnEscape: false
        },
        slots: {
          default: '<p>Content</p>'
        },
        attachTo: document.body
      })

      await nextTick()
      
      // 模拟ESC键按下
      const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' })
      document.dispatchEvent(escapeEvent)
      
      await nextTick()
      expect(wrapper.emitted('update:modelValue')).toBeFalsy()
    })
  })

  describe('持久化模式', () => {
    it('应该在persistent为true时不能关闭', async () => {
      const wrapper = mount(Modal, {
        props: {
          modelValue: true,
          persistent: true,
          closable: true
        },
        slots: {
          default: '<p>Content</p>'
        },
        attachTo: document.body
      })

      await nextTick()
      const closeButton = document.body.querySelector('button')
      await closeButton?.dispatchEvent(new Event('click'))
      
      expect(wrapper.emitted('update:modelValue')).toBeFalsy()
    })
  })

  describe('body滚动控制', () => {
    it('应该监听模态框状态变化', async () => {
      const wrapper = mount(Modal, {
        props: {
          modelValue: false
        },
        slots: {
          default: '<p>Content</p>'
        },
        attachTo: document.body
      })

      // 打开模态框
      await wrapper.setProps({ modelValue: true })
      await nextTick()

      // 验证模态框已渲染
      expect(document.body.querySelector('.fixed.inset-0.z-50')).toBeTruthy()

      // 关闭模态框
      await wrapper.setProps({ modelValue: false })
      await nextTick()

      // 验证模态框已隐藏
      expect(document.body.querySelector('.fixed.inset-0.z-50')).toBeFalsy()
    })
  })

  describe('事件发射', () => {
    it('应该在打开时发射open事件', async () => {
      const wrapper = mount(Modal, {
        props: {
          modelValue: false
        },
        slots: {
          default: '<p>Content</p>'
        },
        attachTo: document.body
      })

      await wrapper.setProps({ modelValue: true })
      await nextTick()
      
      expect(wrapper.emitted('open')).toBeTruthy()
    })
  })

  describe('默认属性', () => {
    it('应该使用正确的默认属性值', async () => {
      const wrapper = mount(Modal, {
        props: {
          modelValue: true
        },
        slots: {
          default: '<p>Content</p>'
        },
        attachTo: document.body
      })

      await nextTick()
      
      // 检查默认尺寸 (md)
      const modalElement = document.body.querySelector('.relative.bg-white')
      expect(modalElement?.classList.contains('max-w-lg')).toBe(true)
      
      // 检查默认显示关闭按钮
      const closeButton = document.body.querySelector('button')
      expect(closeButton).toBeTruthy()
    })
  })
})
