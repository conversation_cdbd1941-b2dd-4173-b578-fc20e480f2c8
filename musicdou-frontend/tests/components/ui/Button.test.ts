import { describe, it, expect, vi } from 'vitest'
import { fireEvent } from '@testing-library/vue'
import { renderWithProviders } from '../../utils/test-utils'
import Button from '../../../app/components/ui/Button.vue'

describe('Button Component', () => {
  it('renders with default props', () => {
    const { getByRole } = renderWithProviders(Button, {
      slots: {
        default: 'Click me'
      }
    })

    const button = getByRole('button')
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Click me')
    expect(button).toHaveClass('bg-primary-500') // Default variant is primary
  })

  it('renders different variants correctly', () => {
    const variants = [
      { variant: 'primary', expectedClass: 'bg-primary-500' },
      { variant: 'secondary', expectedClass: 'bg-secondary-500' },
      { variant: 'outline', expectedClass: 'bg-transparent' },
      { variant: 'ghost', expectedClass: 'bg-transparent' },
      { variant: 'danger', expectedClass: 'bg-red-500' }
    ] as const

    variants.forEach(({ variant, expectedClass }) => {
      const { container } = renderWithProviders(Button, {
        props: { variant },
        slots: { default: 'Button' }
      })

      const button = container.querySelector('button')
      expect(button).toHaveClass(expectedClass)
    })
  })

  it('renders different sizes correctly', () => {
    const sizes = [
      { size: 'sm', expectedClass: 'text-sm' },
      { size: 'md', expectedClass: 'text-sm' },
      { size: 'lg', expectedClass: 'text-base' },
      { size: 'xl', expectedClass: 'text-lg' }
    ] as const

    sizes.forEach(({ size, expectedClass }) => {
      const { container } = renderWithProviders(Button, {
        props: { size },
        slots: { default: 'Button' }
      })

      const button = container.querySelector('button')
      expect(button).toHaveClass(expectedClass)
    })
  })

  it('shows loading state correctly', () => {
    const { getByRole, container } = renderWithProviders(Button, {
      props: { loading: true },
      slots: { default: 'Loading' }
    })

    const button = getByRole('button')
    expect(button).toBeDisabled()

    // Check for loading spinner (ArrowPathIcon with animate-spin)
    const spinner = container.querySelector('.animate-spin')
    expect(spinner).toBeInTheDocument()
  })

  it('is disabled when disabled prop is true', () => {
    const { getByRole } = renderWithProviders(Button, {
      props: { disabled: true },
      slots: { default: 'Disabled' }
    })

    const button = getByRole('button')
    expect(button).toBeDisabled()
    expect(button).toHaveClass('disabled:opacity-50')
  })

  it('emits click event when clicked', async () => {
    const clickHandler = vi.fn()
    const { getByRole } = renderWithProviders(Button, {
      props: { onClick: clickHandler },
      slots: { default: 'Click me' }
    })

    const button = getByRole('button')
    await fireEvent.click(button)
    
    expect(clickHandler).toHaveBeenCalledTimes(1)
  })

  it('does not emit click when disabled', async () => {
    const clickHandler = vi.fn()
    const { getByRole } = renderWithProviders(Button, {
      props: { 
        disabled: true,
        onClick: clickHandler 
      },
      slots: { default: 'Disabled' }
    })

    const button = getByRole('button')
    await fireEvent.click(button)
    
    expect(clickHandler).not.toHaveBeenCalled()
  })

  it('does not emit click when loading', async () => {
    const clickHandler = vi.fn()
    const { getByRole } = renderWithProviders(Button, {
      props: { 
        loading: true,
        onClick: clickHandler 
      },
      slots: { default: 'Loading' }
    })

    const button = getByRole('button')
    await fireEvent.click(button)
    
    expect(clickHandler).not.toHaveBeenCalled()
  })

  it('renders with icon when provided', () => {
    const { container } = renderWithProviders(Button, {
      props: { icon: 'PlusIcon' },
      slots: { default: 'Add Item' }
    })

    // Check that the button has the expected structure with icon
    const button = container.querySelector('button')
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Add Item')

    // The icon should be rendered as a component, even if not as SVG in test
    // We can check for the presence of the Icon component by looking for its wrapper
    const buttonContent = button?.innerHTML
    expect(buttonContent).toBeTruthy()
  })

  it('renders as full width when fullWidth prop is true', () => {
    const { getByRole } = renderWithProviders(Button, {
      props: { fullWidth: true },
      slots: { default: 'Full Width' }
    })

    const button = getByRole('button')
    expect(button).toHaveClass('w-full')
  })

  it('supports keyboard navigation', async () => {
    const clickHandler = vi.fn()
    const { getByRole } = renderWithProviders(Button, {
      props: { onClick: clickHandler },
      slots: { default: 'Press Enter' }
    })

    const button = getByRole('button')
    button.focus()

    // Buttons naturally handle Enter and Space key presses
    await fireEvent.keyDown(button, { key: 'Enter' })
    await fireEvent.keyUp(button, { key: 'Enter' })

    await fireEvent.keyDown(button, { key: ' ' })
    await fireEvent.keyUp(button, { key: ' ' })

    // Since the component doesn't have custom keyboard handling,
    // we just test that the button can receive focus
    expect(button).toHaveFocus()
  })
})
