import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import Toast from '~/components/ui/Toast.vue'

// Mock Icon component
vi.mock('~/components/ui/Icon.vue', () => ({
  default: {
    name: 'Icon',
    props: ['name', 'class'],
    template: '<span :class="$props.class">{{ name }}</span>'
  }
}))

describe('Toast', () => {
  let wrapper: any

  beforeEach(() => {
    // 创建body元素用于Teleport
    if (!document.body) {
      document.body = document.createElement('body')
      document.documentElement.appendChild(document.body)
    }
    // 清理DOM
    document.body.innerHTML = ''
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
    // 清理DOM
    document.body.innerHTML = ''
  })

  describe('基础渲染', () => {
    it('应该正确渲染空的Toast容器', () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      expect(document.body.querySelector('.fixed')).toBeTruthy()
    })

    it('应该在没有toast时不渲染任何toast项', () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      expect(document.body.querySelectorAll('[data-toast]').length).toBe(0)
    })
  })

  describe('位置变体', () => {
    const positionTests = [
      { position: 'top-right', expectedClasses: ['top-0', 'right-0'] },
      { position: 'top-left', expectedClasses: ['top-0', 'left-0'] },
      { position: 'bottom-right', expectedClasses: ['bottom-0', 'right-0'] },
      { position: 'bottom-left', expectedClasses: ['bottom-0', 'left-0'] },
      { position: 'top-center', expectedClasses: ['top-0', 'left-1/2'] },
      { position: 'bottom-center', expectedClasses: ['bottom-0', 'left-1/2'] }
    ]

    positionTests.forEach(({ position, expectedClasses }) => {
      it(`应该应用${position}位置样式`, () => {
        wrapper = mount(Toast, {
          props: {
            position: position as any
          },
          attachTo: document.body
        })

        const container = document.body.querySelector('.fixed')
        expectedClasses.forEach(cls => {
          expect(container?.classList.contains(cls)).toBe(true)
        })
      })
    })
  })

  describe('Toast添加和移除', () => {
    it('应该能够添加toast', async () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      // 通过组件实例添加toast
      wrapper.vm.addToast({
        type: 'success',
        message: 'Test message'
      })

      await nextTick()
      expect(wrapper.vm.toasts.length).toBe(1)
      expect(wrapper.vm.toasts[0].message).toBe('Test message')
      expect(wrapper.vm.toasts[0].type).toBe('success')
    })

    it('应该能够移除toast', async () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      wrapper.vm.addToast({
        type: 'info',
        message: 'Test message'
      })

      await nextTick()
      expect(wrapper.vm.toasts.length).toBe(1)

      const toastId = wrapper.vm.toasts[0].id
      wrapper.vm.removeToast(toastId)

      await nextTick()
      expect(wrapper.vm.toasts.length).toBe(0)
    })

    it('应该限制最大toast数量', async () => {
      wrapper = mount(Toast, {
        props: {
          maxToasts: 2
        },
        attachTo: document.body
      })

      // 添加3个toast
      wrapper.vm.addToast({ type: 'info', message: 'Toast 1' })
      wrapper.vm.addToast({ type: 'info', message: 'Toast 2' })
      wrapper.vm.addToast({ type: 'info', message: 'Toast 3' })

      await nextTick()
      expect(wrapper.vm.toasts.length).toBe(2)
      expect(wrapper.vm.toasts[0].message).toBe('Toast 2')
      expect(wrapper.vm.toasts[1].message).toBe('Toast 3')
    })
  })

  describe('Toast类型和图标', () => {
    const typeTests = [
      { type: 'success', expectedIcon: 'CheckCircleIcon' },
      { type: 'error', expectedIcon: 'XCircleIcon' },
      { type: 'warning', expectedIcon: 'ExclamationTriangleIcon' },
      { type: 'info', expectedIcon: 'InformationCircleIcon' }
    ]

    typeTests.forEach(({ type, expectedIcon }) => {
      it(`应该为${type}类型显示正确的图标`, async () => {
        wrapper = mount(Toast, {
          attachTo: document.body
        })

        wrapper.vm.addToast({
          type: type as any,
          message: 'Test message'
        })

        await nextTick()
        expect(wrapper.vm.getIcon(type)).toBe(expectedIcon)
      })
    })
  })

  describe('Toast内容', () => {
    it('应该渲染消息内容', async () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      wrapper.vm.addToast({
        type: 'info',
        message: 'Test message content'
      })

      await nextTick()
      expect(document.body.textContent).toContain('Test message content')
    })

    it('应该渲染标题和消息', async () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      wrapper.vm.addToast({
        type: 'success',
        title: 'Success Title',
        message: 'Success message'
      })

      await nextTick()
      expect(document.body.textContent).toContain('Success Title')
      expect(document.body.textContent).toContain('Success message')
    })

    it('应该在没有标题时只渲染消息', async () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      wrapper.vm.addToast({
        type: 'info',
        message: 'Only message'
      })

      await nextTick()
      expect(document.body.textContent).toContain('Only message')
      // 检查没有标题元素
      expect(document.body.querySelector('.font-medium')).toBeFalsy()
    })
  })

  describe('关闭功能', () => {
    it('应该默认显示关闭按钮', async () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      wrapper.vm.addToast({
        type: 'info',
        message: 'Test message'
      })

      await nextTick()
      expect(document.body.querySelector('button')).toBeTruthy()
    })

    it('应该在closable为false时不显示关闭按钮', async () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      wrapper.vm.addToast({
        type: 'info',
        message: 'Test message',
        closable: false
      })

      await nextTick()
      expect(document.body.querySelector('button')).toBeFalsy()
    })

    it('应该在点击关闭按钮时移除toast', async () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      wrapper.vm.addToast({
        type: 'info',
        message: 'Test message'
      })

      await nextTick()
      expect(wrapper.vm.toasts.length).toBe(1)

      const closeButton = document.body.querySelector('button')
      await closeButton?.dispatchEvent(new Event('click'))

      await nextTick()
      expect(wrapper.vm.toasts.length).toBe(0)
    })
  })

  describe('自动关闭', () => {
    it('应该在有duration时显示进度条', async () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      wrapper.vm.addToast({
        type: 'info',
        message: 'Test message',
        duration: 3000
      })

      await nextTick()
      expect(document.body.querySelector('.absolute.bottom-0')).toBeTruthy()
    })

    it('应该在没有duration时不显示进度条', async () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      wrapper.vm.addToast({
        type: 'info',
        message: 'Test message'
      })

      await nextTick()
      expect(document.body.querySelector('.absolute.bottom-0')).toBeFalsy()
    })

    it('应该在duration为0时不显示进度条', async () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      wrapper.vm.addToast({
        type: 'info',
        message: 'Test message',
        duration: 0
      })

      await nextTick()
      expect(document.body.querySelector('.absolute.bottom-0')).toBeFalsy()
    })
  })

  describe('定时器控制', () => {
    it('应该能够暂停定时器', async () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      wrapper.vm.addToast({
        type: 'info',
        message: 'Test message',
        duration: 3000
      })

      await nextTick()
      const toastId = wrapper.vm.toasts[0].id

      wrapper.vm.pauseTimer(toastId)
      expect(wrapper.vm.toasts[0].remainingTime).toBeTruthy()
    })

    it('应该能够恢复定时器', async () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      wrapper.vm.addToast({
        type: 'info',
        message: 'Test message',
        duration: 3000
      })

      await nextTick()
      const toastId = wrapper.vm.toasts[0].id

      wrapper.vm.pauseTimer(toastId)
      const remainingTime = wrapper.vm.toasts[0].remainingTime

      wrapper.vm.resumeTimer(toastId)
      // 恢复后应该有新的开始时间
      expect(wrapper.vm.toasts[0].startTime).toBeTruthy()
    })
  })

  describe('默认属性', () => {
    it('应该使用正确的默认属性值', () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      // 检查默认位置 (top-right)
      const container = document.body.querySelector('.fixed')
      expect(container?.classList.contains('top-0')).toBe(true)
      expect(container?.classList.contains('right-0')).toBe(true)

      // 检查默认最大数量 (5)
      expect(wrapper.vm.maxToasts).toBe(5)
    })
  })

  describe('工具方法', () => {
    it('应该能够清除所有toast', async () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      wrapper.vm.addToast({ type: 'info', message: 'Toast 1' })
      wrapper.vm.addToast({ type: 'success', message: 'Toast 2' })

      await nextTick()
      expect(wrapper.vm.toasts.length).toBe(2)

      wrapper.vm.clearToasts()

      await nextTick()
      expect(wrapper.vm.toasts.length).toBe(0)
    })

    it('应该正确计算进度百分比', async () => {
      wrapper = mount(Toast, {
        attachTo: document.body
      })

      const toast = {
        id: 'test',
        type: 'info' as const,
        message: 'Test',
        duration: 1000,
        startTime: Date.now() - 500,
        remainingTime: 500
      }

      wrapper.vm.toasts.push(toast)
      await nextTick()

      const progress = wrapper.vm.getProgress(toast)
      expect(progress).toBeGreaterThanOrEqual(0)
      expect(progress).toBeLessThanOrEqual(100)
    })
  })
})
