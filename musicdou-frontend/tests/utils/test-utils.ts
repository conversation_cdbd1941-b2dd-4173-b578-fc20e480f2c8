import { render, RenderOptions } from '@testing-library/vue'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { vi } from 'vitest'
import type { Component } from 'vue'

// Create a custom render function with common providers
export function renderWithProviders(
  component: Component,
  options: RenderOptions = {}
) {
  const pinia = createPinia()
  setActivePinia(pinia)

  return render(component, {
    global: {
      plugins: [pinia],
      stubs: {
        // Stub Nuxt components
        NuxtLink: {
          template: '<a><slot /></a>',
          props: ['to']
        },
        NuxtPage: {
          template: '<div><slot /></div>'
        },
        ClientOnly: {
          template: '<div><slot /></div>'
        },
        // Stub transition components
        Transition: {
          template: '<div><slot /></div>'
        },
        TransitionGroup: {
          template: '<div><slot /></div>'
        }
      },
      mocks: {
        $t: (key: string) => key,
        $router: {
          push: vi.fn(),
          replace: vi.fn(),
          go: vi.fn(),
          back: vi.fn(),
          forward: vi.fn()
        },
        $route: {
          path: '/',
          params: {},
          query: {},
          hash: '',
          name: 'index'
        }
      },
      ...options.global
    },
    ...options
  })
}

// Mock API responses
export const mockApiResponse = <T>(data: T, delay = 0) => {
  return new Promise<T>((resolve) => {
    setTimeout(() => resolve(data), delay)
  })
}

export const mockApiError = (message = 'API Error', status = 500, delay = 0) => {
  return new Promise((_, reject) => {
    setTimeout(() => {
      const error = new Error(message)
      ;(error as any).status = status
      reject(error)
    }, delay)
  })
}

// Test data generators
export const createMockUser = (overrides = {}) => ({
  id: '1',
  username: 'testuser',
  email: '<EMAIL>',
  displayName: 'Test User',
  avatar: 'https://example.com/avatar.jpg',
  bio: 'Test user bio',
  followersCount: 10,
  followingCount: 5,
  isFollowing: false,
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  ...overrides
})

export const createMockMusic = (overrides = {}) => ({
  id: '1',
  title: 'Test Song',
  artist: 'Test Artist',
  album: 'Test Album',
  duration: 180,
  url: 'https://example.com/song.mp3',
  coverUrl: 'https://example.com/cover.jpg',
  genre: 'Pop',
  releaseDate: '2023-01-01',
  playCount: 1000,
  likeCount: 50,
  isLiked: false,
  uploadedBy: createMockUser(),
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  ...overrides
})

export const createMockPlaylist = (overrides = {}) => ({
  id: '1',
  name: 'Test Playlist',
  description: 'Test playlist description',
  coverUrl: 'https://example.com/playlist-cover.jpg',
  isPublic: true,
  trackCount: 5,
  totalDuration: 900,
  likeCount: 25,
  isLiked: false,
  createdBy: createMockUser(),
  tracks: [createMockMusic()],
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  ...overrides
})

export const createMockComment = (overrides = {}) => ({
  id: '1',
  content: 'Test comment',
  author: createMockUser(),
  targetType: 'music' as const,
  targetId: '1',
  likeCount: 5,
  isLiked: false,
  replies: [],
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  ...overrides
})

export const createMockNotification = (overrides = {}) => ({
  id: '1',
  type: 'like' as const,
  title: 'Test Notification',
  message: 'Someone liked your music',
  isRead: false,
  data: {},
  createdAt: '2023-01-01T00:00:00Z',
  ...overrides
})

// Event helpers
export const createMockEvent = (type: string, properties = {}) => {
  const event = new Event(type, { bubbles: true, cancelable: true })
  Object.assign(event, properties)
  return event
}

export const createMockKeyboardEvent = (key: string, properties = {}) => {
  return new KeyboardEvent('keydown', {
    key,
    bubbles: true,
    cancelable: true,
    ...properties
  })
}

export const createMockMouseEvent = (type: string, properties = {}) => {
  return new MouseEvent(type, {
    bubbles: true,
    cancelable: true,
    ...properties
  })
}

// Wait helpers
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export const waitForNextTick = () => new Promise(resolve => process.nextTick(resolve))

// DOM helpers
export const getByTestId = (container: HTMLElement, testId: string) => {
  return container.querySelector(`[data-testid="${testId}"]`)
}

export const getAllByTestId = (container: HTMLElement, testId: string) => {
  return container.querySelectorAll(`[data-testid="${testId}"]`)
}

// Mock store helpers
export const createMockStore = (initialState = {}) => {
  return {
    state: reactive(initialState),
    getters: {},
    actions: {},
    mutations: {}
  }
}

// Audio mock helpers
export const createMockAudio = () => ({
  play: vi.fn().mockResolvedValue(undefined),
  pause: vi.fn(),
  load: vi.fn(),
  currentTime: 0,
  duration: 180,
  volume: 1,
  muted: false,
  paused: true,
  ended: false,
  readyState: 4,
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn()
})

// File mock helpers
export const createMockFile = (name = 'test.mp3', type = 'audio/mpeg', size = 1024) => {
  const file = new File([''], name, { type })
  Object.defineProperty(file, 'size', { value: size })
  return file
}

// Form helpers
export const fillForm = async (container: HTMLElement, data: Record<string, string>) => {
  for (const [name, value] of Object.entries(data)) {
    const input = container.querySelector(`[name="${name}"]`) as HTMLInputElement
    if (input) {
      input.value = value
      input.dispatchEvent(new Event('input', { bubbles: true }))
    }
  }
}
