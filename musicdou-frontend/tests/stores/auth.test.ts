import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'

// Mock dependencies
const mockAuthApi = {
  login: vi.fn(),
  register: vi.fn(),
  logout: vi.fn(),
  getCurrentUser: vi.fn(),
  updateProfile: vi.fn(),
  refreshToken: vi.fn()
}

const mockCookie = {
  value: null
}

global.useAuthApi = vi.fn(() => mockAuthApi)
global.useCookie = vi.fn(() => mockCookie)
global.ref = vi.fn((value) => ({ value }))
global.computed = vi.fn((fn) => ({ value: fn() }))
global.process = { env: { NODE_ENV: 'test' } }

import { useAuthStore } from '../../app/stores/auth'

describe('Auth Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    mockCookie.value = null
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      const authStore = useAuthStore()
      
      expect(authStore.user).toBeNull()
      expect(authStore.isLoggedIn).toBe(false)
      expect(authStore.isLoading).toBe(false)
    })
  })

  describe('登录功能', () => {
    it('应该正确处理成功登录', async () => {
      const authStore = useAuthStore()
      const credentials = { email: '<EMAIL>', password: 'password123' }
      const mockResponse = {
        success: true,
        data: {
          user: { id: 1, email: '<EMAIL>', username: 'testuser' },
          token: 'mock-token-123'
        }
      }
      
      mockAuthApi.login.mockResolvedValue(mockResponse)

      const result = await authStore.login(credentials)

      expect(mockAuthApi.login).toHaveBeenCalledWith(credentials)
      expect(authStore.user).toEqual(mockResponse.data.user)
      expect(mockCookie.value).toBe('mock-token-123')
      expect(result).toEqual(mockResponse)
    })

    it('应该正确处理登录失败', async () => {
      const authStore = useAuthStore()
      const credentials = { email: '<EMAIL>', password: 'wrongpassword' }
      const mockError = new Error('登录失败')
      
      mockAuthApi.login.mockRejectedValue(mockError)

      await expect(authStore.login(credentials)).rejects.toThrow('登录失败')
      expect(authStore.user).toBeNull()
      expect(mockCookie.value).toBeNull()
    })

    it('应该在登录过程中设置loading状态', async () => {
      const authStore = useAuthStore()
      const credentials = { email: '<EMAIL>', password: 'password123' }
      
      // 模拟异步操作
      let resolveLogin: (value: any) => void
      const loginPromise = new Promise(resolve => {
        resolveLogin = resolve
      })
      mockAuthApi.login.mockReturnValue(loginPromise)

      const loginCall = authStore.login(credentials)
      
      // 检查loading状态
      expect(authStore.isLoading).toBe(true)
      
      // 完成登录
      resolveLogin({ success: true, data: { user: {}, token: 'token' } })
      await loginCall
      
      expect(authStore.isLoading).toBe(false)
    })
  })

  describe('注册功能', () => {
    it('应该正确处理成功注册', async () => {
      const authStore = useAuthStore()
      const userData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123'
      }
      const mockResponse = {
        success: true,
        data: {
          user: { id: 1, username: 'newuser', email: '<EMAIL>' },
          token: 'new-token-123'
        }
      }
      
      mockAuthApi.register.mockResolvedValue(mockResponse)

      const result = await authStore.register(userData)

      expect(mockAuthApi.register).toHaveBeenCalledWith(userData)
      expect(authStore.user).toEqual(mockResponse.data.user)
      expect(mockCookie.value).toBe('new-token-123')
      expect(result).toEqual(mockResponse)
    })

    it('应该正确处理注册失败', async () => {
      const authStore = useAuthStore()
      const userData = {
        username: 'existinguser',
        email: '<EMAIL>',
        password: 'password123'
      }
      const mockError = new Error('用户已存在')
      
      mockAuthApi.register.mockRejectedValue(mockError)

      await expect(authStore.register(userData)).rejects.toThrow('用户已存在')
      expect(authStore.user).toBeNull()
    })
  })

  describe('登出功能', () => {
    it('应该正确处理登出', async () => {
      const authStore = useAuthStore()
      
      // 先设置用户已登录状态
      authStore.user = { id: 1, username: 'testuser' }
      mockCookie.value = 'existing-token'
      
      const mockResponse = { success: true, data: { message: '登出成功' } }
      mockAuthApi.logout.mockResolvedValue(mockResponse)

      await authStore.logout()

      expect(mockAuthApi.logout).toHaveBeenCalled()
      expect(authStore.user).toBeNull()
      expect(mockCookie.value).toBeNull()
    })

    it('应该在API调用失败时仍然清除本地状态', async () => {
      const authStore = useAuthStore()
      
      // 先设置用户已登录状态
      authStore.user = { id: 1, username: 'testuser' }
      mockCookie.value = 'existing-token'
      
      mockAuthApi.logout.mockRejectedValue(new Error('网络错误'))

      await authStore.logout()

      // 即使API调用失败，也应该清除本地状态
      expect(authStore.user).toBeNull()
      expect(mockCookie.value).toBeNull()
    })
  })

  describe('用户信息更新', () => {
    it('应该正确更新用户资料', async () => {
      const authStore = useAuthStore()
      
      // 设置初始用户状态
      authStore.user = { id: 1, username: 'oldname', email: '<EMAIL>' }
      
      const updateData = { username: 'newname', bio: '新的个人简介' }
      const mockResponse = {
        success: true,
        data: { id: 1, username: 'newname', email: '<EMAIL>', bio: '新的个人简介' }
      }
      
      mockAuthApi.updateProfile.mockResolvedValue(mockResponse)

      const result = await authStore.updateProfile(updateData)

      expect(mockAuthApi.updateProfile).toHaveBeenCalledWith(updateData)
      expect(authStore.user).toEqual(mockResponse.data)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取当前用户信息', async () => {
      const authStore = useAuthStore()
      const mockResponse = {
        success: true,
        data: { id: 1, username: 'testuser', email: '<EMAIL>' }
      }
      
      mockAuthApi.getCurrentUser.mockResolvedValue(mockResponse)

      const result = await authStore.fetchCurrentUser()

      expect(mockAuthApi.getCurrentUser).toHaveBeenCalled()
      expect(authStore.user).toEqual(mockResponse.data)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('Token管理', () => {
    it('应该正确刷新token', async () => {
      const authStore = useAuthStore()
      const mockResponse = {
        success: true,
        data: { token: 'new-refreshed-token' }
      }
      
      mockAuthApi.refreshToken.mockResolvedValue(mockResponse)

      const result = await authStore.refreshToken()

      expect(mockAuthApi.refreshToken).toHaveBeenCalled()
      expect(mockCookie.value).toBe('new-refreshed-token')
      expect(result).toEqual(mockResponse)
    })

    it('应该在token刷新失败时清除用户状态', async () => {
      const authStore = useAuthStore()
      
      // 设置初始状态
      authStore.user = { id: 1, username: 'testuser' }
      mockCookie.value = 'expired-token'
      
      mockAuthApi.refreshToken.mockRejectedValue(new Error('Token已过期'))

      await expect(authStore.refreshToken()).rejects.toThrow('Token已过期')
      expect(authStore.user).toBeNull()
      expect(mockCookie.value).toBeNull()
    })
  })

  describe('计算属性', () => {
    it('isLoggedIn应该根据user状态正确计算', () => {
      const authStore = useAuthStore()
      
      // 未登录状态
      authStore.user = null
      expect(authStore.isLoggedIn).toBe(false)
      
      // 已登录状态
      authStore.user = { id: 1, username: 'testuser' }
      expect(authStore.isLoggedIn).toBe(true)
    })
  })

  describe('初始化', () => {
    it('应该正确初始化用户状态', async () => {
      const authStore = useAuthStore()
      
      // 模拟有有效token
      mockCookie.value = 'valid-token'
      const mockResponse = {
        success: true,
        data: { id: 1, username: 'testuser', email: '<EMAIL>' }
      }
      mockAuthApi.getCurrentUser.mockResolvedValue(mockResponse)

      await authStore.initializeAuth()

      expect(mockAuthApi.getCurrentUser).toHaveBeenCalled()
      expect(authStore.user).toEqual(mockResponse.data)
    })

    it('应该在初始化失败时清除token', async () => {
      const authStore = useAuthStore()
      
      // 模拟有无效token
      mockCookie.value = 'invalid-token'
      mockAuthApi.getCurrentUser.mockRejectedValue(new Error('Token无效'))

      await authStore.initializeAuth()

      expect(authStore.user).toBeNull()
      expect(mockCookie.value).toBeNull()
    })
  })
})
