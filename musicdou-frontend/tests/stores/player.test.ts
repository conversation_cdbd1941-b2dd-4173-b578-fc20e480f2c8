import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { setActive<PERSON>inia, createPinia } from 'pinia'

// Mock dependencies
global.ref = vi.fn((value) => ({ value }))
global.computed = vi.fn((fn) => ({ value: fn() }))

import { usePlayerStore } from '../../app/stores/player'

describe('Player Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      const playerStore = usePlayerStore()
      
      expect(playerStore.state.currentTrack).toBeNull()
      expect(playerStore.state.isPlaying).toBe(false)
      expect(playerStore.state.volume).toBe(0.8)
      expect(playerStore.state.currentTime).toBe(0)
      expect(playerStore.state.duration).toBe(0)
      expect(playerStore.state.queue).toEqual([])
      expect(playerStore.state.currentIndex).toBe(-1)
      expect(playerStore.state.shuffle).toBe(false)
      expect(playerStore.state.repeat).toBe('none')
    })
  })

  describe('基础状态设置', () => {
    it('应该正确设置当前音轨', () => {
      const playerStore = usePlayerStore()
      const mockTrack = {
        id: '1',
        title: 'Test Song',
        artist: 'Test Artist',
        duration: 180
      }

      playerStore.setCurrentTrack(mockTrack)

      expect(playerStore.state.currentTrack).toEqual(mockTrack)
    })

    it('应该正确设置播放状态', () => {
      const playerStore = usePlayerStore()

      playerStore.setPlaying(true)
      expect(playerStore.state.isPlaying).toBe(true)

      playerStore.setPlaying(false)
      expect(playerStore.state.isPlaying).toBe(false)
    })

    it('应该正确设置音量并限制范围', () => {
      const playerStore = usePlayerStore()

      // 正常音量
      playerStore.setVolume(0.5)
      expect(playerStore.state.volume).toBe(0.5)

      // 超出上限
      playerStore.setVolume(1.5)
      expect(playerStore.state.volume).toBe(1)

      // 超出下限
      playerStore.setVolume(-0.5)
      expect(playerStore.state.volume).toBe(0)
    })

    it('应该正确设置当前时间', () => {
      const playerStore = usePlayerStore()

      playerStore.setCurrentTime(60)
      expect(playerStore.state.currentTime).toBe(60)
    })

    it('应该正确设置音轨时长', () => {
      const playerStore = usePlayerStore()

      playerStore.setDuration(180)
      expect(playerStore.state.duration).toBe(180)
    })
  })

  describe('播放队列管理', () => {
    it('应该正确设置播放队列', () => {
      const playerStore = usePlayerStore()
      const mockQueue = [
        { id: '1', title: 'Song 1', artist: 'Artist 1' },
        { id: '2', title: 'Song 2', artist: 'Artist 2' },
        { id: '3', title: 'Song 3', artist: 'Artist 3' }
      ]

      playerStore.setQueue(mockQueue)

      expect(playerStore.state.queue).toEqual(mockQueue)
    })

    it('应该正确设置当前索引', () => {
      const playerStore = usePlayerStore()

      playerStore.setCurrentIndex(2)
      expect(playerStore.state.currentIndex).toBe(2)
    })

    it('应该正确添加音轨到队列', () => {
      const playerStore = usePlayerStore()
      const mockTrack = { id: '1', title: 'New Song', artist: 'New Artist' }

      playerStore.addToQueue(mockTrack)

      expect(playerStore.state.queue).toContain(mockTrack)
      expect(playerStore.state.queue.length).toBe(1)
    })

    it('应该正确从队列移除音轨', () => {
      const playerStore = usePlayerStore()
      const mockQueue = [
        { id: '1', title: 'Song 1', artist: 'Artist 1' },
        { id: '2', title: 'Song 2', artist: 'Artist 2' },
        { id: '3', title: 'Song 3', artist: 'Artist 3' }
      ]

      playerStore.setQueue(mockQueue)
      playerStore.removeFromQueue(1) // 移除索引为1的音轨

      expect(playerStore.state.queue.length).toBe(2)
      expect(playerStore.state.queue[1]).toEqual(mockQueue[2])
    })

    it('应该正确清空播放队列', () => {
      const playerStore = usePlayerStore()
      const mockQueue = [
        { id: '1', title: 'Song 1', artist: 'Artist 1' },
        { id: '2', title: 'Song 2', artist: 'Artist 2' }
      ]

      playerStore.setQueue(mockQueue)
      playerStore.clearQueue()

      expect(playerStore.state.queue).toEqual([])
      expect(playerStore.state.currentIndex).toBe(-1)
    })
  })

  describe('播放控制', () => {
    it('应该正确播放音轨', () => {
      const playerStore = usePlayerStore()
      const mockTrack = { id: '1', title: 'Test Song', artist: 'Test Artist' }
      const mockQueue = [mockTrack]

      playerStore.playTrack(mockTrack, mockQueue)

      expect(playerStore.state.currentTrack).toEqual(mockTrack)
      expect(playerStore.state.queue).toEqual(mockQueue)
      expect(playerStore.state.currentIndex).toBe(0)
    })

    it('应该正确播放下一首', () => {
      const playerStore = usePlayerStore()
      const mockQueue = [
        { id: '1', title: 'Song 1', artist: 'Artist 1' },
        { id: '2', title: 'Song 2', artist: 'Artist 2' },
        { id: '3', title: 'Song 3', artist: 'Artist 3' }
      ]

      playerStore.setQueue(mockQueue)
      playerStore.setCurrentIndex(0)
      playerStore.setCurrentTrack(mockQueue[0])

      playerStore.playNext()

      expect(playerStore.state.currentIndex).toBe(1)
      expect(playerStore.state.currentTrack).toEqual(mockQueue[1])
    })

    it('应该正确播放上一首', () => {
      const playerStore = usePlayerStore()
      const mockQueue = [
        { id: '1', title: 'Song 1', artist: 'Artist 1' },
        { id: '2', title: 'Song 2', artist: 'Artist 2' },
        { id: '3', title: 'Song 3', artist: 'Artist 3' }
      ]

      playerStore.setQueue(mockQueue)
      playerStore.setCurrentIndex(2)
      playerStore.setCurrentTrack(mockQueue[2])

      playerStore.playPrevious()

      expect(playerStore.state.currentIndex).toBe(1)
      expect(playerStore.state.currentTrack).toEqual(mockQueue[1])
    })

    it('应该在循环模式下正确处理队列边界', () => {
      const playerStore = usePlayerStore()
      const mockQueue = [
        { id: '1', title: 'Song 1', artist: 'Artist 1' },
        { id: '2', title: 'Song 2', artist: 'Artist 2' }
      ]

      playerStore.setQueue(mockQueue)
      playerStore.setRepeat('all')
      playerStore.setCurrentIndex(1) // 最后一首

      playerStore.playNext()

      expect(playerStore.state.currentIndex).toBe(0) // 应该回到第一首
      expect(playerStore.state.currentTrack).toEqual(mockQueue[0])
    })
  })

  describe('播放模式', () => {
    it('应该正确设置随机播放', () => {
      const playerStore = usePlayerStore()

      playerStore.setShuffle(true)
      expect(playerStore.state.shuffle).toBe(true)

      playerStore.setShuffle(false)
      expect(playerStore.state.shuffle).toBe(false)
    })

    it('应该正确设置重复模式', () => {
      const playerStore = usePlayerStore()

      playerStore.setRepeat('one')
      expect(playerStore.state.repeat).toBe('one')

      playerStore.setRepeat('all')
      expect(playerStore.state.repeat).toBe('all')

      playerStore.setRepeat('none')
      expect(playerStore.state.repeat).toBe('none')
    })

    it('应该正确切换重复模式', () => {
      const playerStore = usePlayerStore()

      // none -> one
      playerStore.state.repeat = 'none'
      playerStore.toggleRepeat()
      expect(playerStore.state.repeat).toBe('one')

      // one -> all
      playerStore.toggleRepeat()
      expect(playerStore.state.repeat).toBe('all')

      // all -> none
      playerStore.toggleRepeat()
      expect(playerStore.state.repeat).toBe('none')
    })
  })

  describe('计算属性', () => {
    it('hasCurrentTrack应该正确计算', () => {
      const playerStore = usePlayerStore()

      expect(playerStore.hasCurrentTrack).toBe(false)

      playerStore.setCurrentTrack({ id: '1', title: 'Test', artist: 'Test' })
      expect(playerStore.hasCurrentTrack).toBe(true)
    })

    it('hasQueue应该正确计算', () => {
      const playerStore = usePlayerStore()

      expect(playerStore.hasQueue).toBe(false)

      playerStore.setQueue([{ id: '1', title: 'Test', artist: 'Test' }])
      expect(playerStore.hasQueue).toBe(true)
    })

    it('canPlayNext应该正确计算', () => {
      const playerStore = usePlayerStore()
      const mockQueue = [
        { id: '1', title: 'Song 1', artist: 'Artist 1' },
        { id: '2', title: 'Song 2', artist: 'Artist 2' }
      ]

      playerStore.setQueue(mockQueue)
      
      // 在第一首时可以播放下一首
      playerStore.setCurrentIndex(0)
      expect(playerStore.canPlayNext).toBe(true)

      // 在最后一首时不能播放下一首（非循环模式）
      playerStore.setCurrentIndex(1)
      expect(playerStore.canPlayNext).toBe(false)

      // 循环模式下总是可以播放下一首
      playerStore.setRepeat('all')
      expect(playerStore.canPlayNext).toBe(true)
    })

    it('canPlayPrevious应该正确计算', () => {
      const playerStore = usePlayerStore()
      const mockQueue = [
        { id: '1', title: 'Song 1', artist: 'Artist 1' },
        { id: '2', title: 'Song 2', artist: 'Artist 2' }
      ]

      playerStore.setQueue(mockQueue)
      
      // 在第一首时不能播放上一首（非循环模式）
      playerStore.setCurrentIndex(0)
      expect(playerStore.canPlayPrevious).toBe(false)

      // 在第二首时可以播放上一首
      playerStore.setCurrentIndex(1)
      expect(playerStore.canPlayPrevious).toBe(true)

      // 循环模式下总是可以播放上一首
      playerStore.setCurrentIndex(0)
      playerStore.setRepeat('all')
      expect(playerStore.canPlayPrevious).toBe(true)
    })
  })

  describe('进度计算', () => {
    it('应该正确计算播放进度', () => {
      const playerStore = usePlayerStore()

      playerStore.setDuration(180)
      playerStore.setCurrentTime(60)

      expect(playerStore.progress).toBe(60 / 180)
    })

    it('应该在时长为0时返回0进度', () => {
      const playerStore = usePlayerStore()

      playerStore.setDuration(0)
      playerStore.setCurrentTime(60)

      expect(playerStore.progress).toBe(0)
    })
  })
})
