import { vi, beforeAll, afterEach, afterAll } from 'vitest'
import '@testing-library/jest-dom'
import { setupMockServer, resetMockServer, closeMockServer } from './mocks/server'

// Setup MSW
beforeAll(() => {
  setupMockServer()
})

afterEach(() => {
  resetMockServer()
})

afterAll(() => {
  closeMockServer()
})

// Mock Nuxt composables
vi.mock('#app', () => ({
  useNuxtApp: () => ({
    $router: {
      push: vi.fn(),
      replace: vi.fn(),
      go: vi.fn(),
      back: vi.fn(),
      forward: vi.fn()
    },
    $route: {
      path: '/',
      params: {},
      query: {},
      hash: '',
      name: 'index'
    }
  }),
  navigateTo: vi.fn(),
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn(),
    forward: vi.fn()
  }),
  useRoute: () => ({
    path: '/',
    params: {},
    query: {},
    hash: '',
    name: 'index'
  }),
  useCookie: vi.fn(() => ({
    value: null
  })),
  useRuntimeConfig: () => ({
    public: {
      apiBase: 'http://localhost:3000/api/v1'
    }
  }),
  useState: vi.fn((key, init) => {
    const state = ref(typeof init === 'function' ? init() : init)
    return state
  }),
  useHead: vi.fn(),
  useSeoMeta: vi.fn()
}))

// Mock Vue Router
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn(),
    forward: vi.fn()
  }),
  useRoute: () => ({
    path: '/',
    params: {},
    query: {},
    hash: '',
    name: 'index'
  })
}))

// Mock Pinia
vi.mock('pinia', () => ({
  defineStore: vi.fn(),
  createPinia: vi.fn(),
  setActivePinia: vi.fn()
}))

// Mock @vueuse/core
vi.mock('@vueuse/core', () => ({
  useLocalStorage: vi.fn(() => ref(null)),
  useSessionStorage: vi.fn(() => ref(null)),
  useDark: vi.fn(() => ref(false)),
  useToggle: vi.fn(() => [ref(false), vi.fn()]),
  useEventListener: vi.fn(),
  onClickOutside: vi.fn(),
  useClipboard: vi.fn(() => ({
    copy: vi.fn(),
    copied: ref(false),
    isSupported: ref(true)
  }))
}))

// Mock @nuxtjs/color-mode
vi.mock('@nuxtjs/color-mode', () => ({
  useColorMode: () => ({
    preference: ref('system'),
    value: ref('light'),
    unknown: ref(false)
  })
}))

// Mock Howler.js
vi.mock('howler', () => ({
  Howl: vi.fn().mockImplementation(() => ({
    play: vi.fn(),
    pause: vi.fn(),
    stop: vi.fn(),
    volume: vi.fn(),
    seek: vi.fn(),
    duration: vi.fn(() => 180),
    state: vi.fn(() => 'loaded'),
    playing: vi.fn(() => false),
    on: vi.fn(),
    off: vi.fn(),
    once: vi.fn(),
    unload: vi.fn()
  })),
  Howler: {
    volume: vi.fn(),
    mute: vi.fn(),
    stop: vi.fn(),
    unload: vi.fn()
  }
}))

// Mock Web APIs
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn()
  }))
})

Object.defineProperty(window, 'ResizeObserver', {
  writable: true,
  value: vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  }))
})

Object.defineProperty(window, 'IntersectionObserver', {
  writable: true,
  value: vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  }))
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
})

// Mock fetch
global.fetch = vi.fn()

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn()
}

// Setup global test utilities
import { ref, reactive, computed, readonly } from 'vue'
global.ref = ref
global.reactive = reactive
global.computed = computed
global.readonly = readonly

// Mock custom composables
vi.mock('~/composables/useApi', () => ({
  useApi: () => ({
    api: vi.fn()
  })
}))

vi.mock('~/composables/useNotification', () => ({
  useNotification: () => ({
    showError: vi.fn(),
    showSuccess: vi.fn(),
    showInfo: vi.fn(),
    showWarning: vi.fn()
  })
}))

// Global mock for composables
global.useApi = vi.fn(() => ({
  api: vi.fn()
}))

global.useNotification = vi.fn(() => ({
  showError: vi.fn(),
  showSuccess: vi.fn(),
  showInfo: vi.fn(),
  showWarning: vi.fn()
}))
