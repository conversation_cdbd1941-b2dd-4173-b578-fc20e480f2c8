import { test, expect } from '@playwright/test'

test.describe('Authentication', () => {
  test.describe('Login Page', () => {
    test.beforeEach(async ({ page }) => {
      await page.goto('/login')
    })

    test('should display login form', async ({ page }) => {
      await expect(page).toHaveTitle(/Login/)
      
      // Check form elements
      await expect(page.locator('input[name="email"]')).toBeVisible()
      await expect(page.locator('input[name="password"]')).toBeVisible()
      await expect(page.locator('button[type="submit"]')).toBeVisible()
    })

    test('should show validation errors for empty form', async ({ page }) => {
      // Try to submit empty form
      await page.click('button[type="submit"]')
      
      // Check for validation messages
      await expect(page.locator('text=Email is required')).toBeVisible()
      await expect(page.locator('text=Password is required')).toBeVisible()
    })

    test('should show validation error for invalid email', async ({ page }) => {
      await page.fill('input[name="email"]', 'invalid-email')
      await page.fill('input[name="password"]', 'password123')
      await page.click('button[type="submit"]')
      
      await expect(page.locator('text=Please enter a valid email')).toBeVisible()
    })

    test('should navigate to register page', async ({ page }) => {
      const registerLink = page.locator('a[href="/register"]')
      await expect(registerLink).toBeVisible()
      
      await registerLink.click()
      await expect(page).toHaveURL('/register')
    })

    test('should navigate to forgot password page', async ({ page }) => {
      const forgotPasswordLink = page.locator('a[href="/forgot-password"]')
      await expect(forgotPasswordLink).toBeVisible()
      
      await forgotPasswordLink.click()
      await expect(page).toHaveURL('/forgot-password')
    })
  })

  test.describe('Register Page', () => {
    test.beforeEach(async ({ page }) => {
      await page.goto('/register')
    })

    test('should display register form', async ({ page }) => {
      await expect(page).toHaveTitle(/Register/)
      
      // Check form elements
      await expect(page.locator('input[name="username"]')).toBeVisible()
      await expect(page.locator('input[name="email"]')).toBeVisible()
      await expect(page.locator('input[name="password"]')).toBeVisible()
      await expect(page.locator('input[name="confirmPassword"]')).toBeVisible()
      await expect(page.locator('button[type="submit"]')).toBeVisible()
    })

    test('should show password strength indicator', async ({ page }) => {
      const passwordInput = page.locator('input[name="password"]')
      await passwordInput.fill('weak')
      
      // Check for password strength indicator
      const strengthIndicator = page.locator('[data-testid="password-strength"]')
      if (await strengthIndicator.isVisible()) {
        await expect(strengthIndicator).toContainText(/weak|strong/i)
      }
    })

    test('should validate password confirmation', async ({ page }) => {
      await page.fill('input[name="password"]', 'password123')
      await page.fill('input[name="confirmPassword"]', 'different')
      await page.click('button[type="submit"]')
      
      await expect(page.locator('text=Passwords do not match')).toBeVisible()
    })
  })

  test.describe('Forgot Password Page', () => {
    test.beforeEach(async ({ page }) => {
      await page.goto('/forgot-password')
    })

    test('should display forgot password form', async ({ page }) => {
      await expect(page).toHaveTitle(/Forgot Password/)
      
      await expect(page.locator('input[name="email"]')).toBeVisible()
      await expect(page.locator('button[type="submit"]')).toBeVisible()
    })

    test('should validate email format', async ({ page }) => {
      await page.fill('input[name="email"]', 'invalid-email')
      await page.click('button[type="submit"]')
      
      await expect(page.locator('text=Please enter a valid email')).toBeVisible()
    })
  })
})
