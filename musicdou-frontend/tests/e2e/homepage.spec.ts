import { test, expect } from '@playwright/test'

test.describe('Homepage', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should load homepage successfully', async ({ page }) => {
    // Check if the page loads
    await expect(page).toHaveTitle(/MusicDou/)
    
    // Check for main navigation
    await expect(page.locator('nav')).toBeVisible()
    
    // Check for main content
    await expect(page.locator('main')).toBeVisible()
  })

  test('should have working navigation', async ({ page }) => {
    // Check navigation links
    const loginLink = page.locator('a[href="/login"]')
    const registerLink = page.locator('a[href="/register"]')
    
    await expect(loginLink).toBeVisible()
    await expect(registerLink).toBeVisible()
  })

  test('should support theme switching', async ({ page }) => {
    // Look for theme toggle button
    const themeToggle = page.locator('[data-testid="theme-toggle"]')
    
    if (await themeToggle.isVisible()) {
      // Test theme switching
      await themeToggle.click()
      
      // Check if theme changed (you might need to adjust this based on your implementation)
      const html = page.locator('html')
      await expect(html).toHaveClass(/dark|light/)
    }
  })

  test('should be responsive', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    await expect(page.locator('main')).toBeVisible()
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 })
    await expect(page.locator('main')).toBeVisible()
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 })
    await expect(page.locator('main')).toBeVisible()
  })

  test('should have accessible navigation', async ({ page }) => {
    // Check for proper ARIA labels and roles
    const nav = page.locator('nav')
    await expect(nav).toBeVisible()
    
    // Test keyboard navigation
    await page.keyboard.press('Tab')
    const focusedElement = page.locator(':focus')
    await expect(focusedElement).toBeVisible()
  })
})
