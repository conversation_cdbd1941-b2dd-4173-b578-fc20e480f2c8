import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { ref } from 'vue'

// Mock dependencies
const mockNotification = {
  error: vi.fn(),
  success: vi.fn(),
  info: vi.fn(),
  warning: vi.fn()
}

const mockConfig = {
  public: {
    apiBase: 'http://localhost:3000/api/v1'
  }
}

const mockCookie = ref(null)
const mockFetch = vi.fn()
const mockNavigateTo = vi.fn()

// Mock global functions
global.useNotification = vi.fn(() => mockNotification)
global.useRuntimeConfig = vi.fn(() => mockConfig)
global.useCookie = vi.fn(() => mockCookie)
global.$fetch = mockFetch
global.navigateTo = mockNavigateTo

import { useApi } from '../../app/composables/useApi'

describe('useApi Composable', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockCookie.value = null
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('基础API请求', () => {
    it('应该正确构建请求头', async () => {
      const { api } = useApi()
      mockFetch.mockResolvedValue({ success: true })

      await api('/test')

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/v1/test',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'X-Request-ID': expect.any(String)
          })
        })
      )
    })

    it('应该在有token时添加Authorization头', async () => {
      mockCookie.value = 'test-token'
      const { api } = useApi()
      mockFetch.mockResolvedValue({ success: true })

      await api('/test')

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/v1/test',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-token'
          })
        })
      )
    })

    it('应该处理完整URL', async () => {
      const { api } = useApi()
      mockFetch.mockResolvedValue({ success: true })

      await api('https://external-api.com/test')

      expect(mockFetch).toHaveBeenCalledWith(
        'https://external-api.com/test',
        expect.any(Object)
      )
    })
  })

  describe('错误处理', () => {
    it('应该处理401认证错误', async () => {
      mockCookie.value = 'invalid-token'
      const { api } = useApi()
      const error = { status: 401 }
      mockFetch.mockRejectedValue(error)

      await expect(api('/test')).rejects.toThrow()
      
      expect(mockCookie.value).toBeNull()
      expect(mockNotification.error).toHaveBeenCalledWith('登录已过期', '请重新登录')
      expect(mockNavigateTo).toHaveBeenCalledWith('/login')
    })

    it('应该处理500服务器错误', async () => {
      const { api } = useApi()
      const error = { status: 500 }
      mockFetch.mockRejectedValue(error)

      await expect(api('/test')).rejects.toThrow()
      
      expect(mockNotification.error).toHaveBeenCalledWith('服务器错误', '请稍后重试')
    })

    it('应该处理403权限错误', async () => {
      const { api } = useApi()
      const error = { status: 403 }
      mockFetch.mockRejectedValue(error)

      await expect(api('/test')).rejects.toThrow()
      
      expect(mockNotification.error).toHaveBeenCalledWith('权限不足', '您没有执行此操作的权限')
    })

    it('应该处理404资源不存在错误', async () => {
      const { api } = useApi()
      const error = { status: 404 }
      mockFetch.mockRejectedValue(error)

      await expect(api('/test')).rejects.toThrow()
      
      expect(mockNotification.error).toHaveBeenCalledWith('资源不存在', '请求的资源未找到')
    })

    it('应该处理429请求频繁错误', async () => {
      const { api } = useApi()
      const error = { status: 429 }
      mockFetch.mockRejectedValue(error)

      await expect(api('/test')).rejects.toThrow()
      
      expect(mockNotification.error).toHaveBeenCalledWith('请求过于频繁', '请稍后再试')
    })

    it('应该处理400请求参数错误', async () => {
      const { api } = useApi()
      const error = { 
        status: 400,
        data: { message: '参数验证失败' }
      }
      mockFetch.mockRejectedValue(error)

      await expect(api('/test')).rejects.toThrow()
      
      expect(mockNotification.error).toHaveBeenCalledWith('请求错误', '参数验证失败')
    })
  })

  describe('HTTP方法', () => {
    it('应该正确执行GET请求', async () => {
      const { get } = useApi()
      const mockResponse = { success: true, data: [] }
      mockFetch.mockResolvedValue(mockResponse)

      const result = await get('/users', { page: 1 })

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/v1/users',
        expect.objectContaining({
          method: 'GET',
          params: { page: 1 }
        })
      )
      expect(result).toEqual(mockResponse)
    })

    it('应该正确执行POST请求', async () => {
      const { post } = useApi()
      const mockResponse = { success: true, data: { id: 1 } }
      const requestBody = { name: 'Test User' }
      mockFetch.mockResolvedValue(mockResponse)

      const result = await post('/users', requestBody)

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/v1/users',
        expect.objectContaining({
          method: 'POST',
          body: requestBody
        })
      )
      expect(result).toEqual(mockResponse)
    })

    it('应该正确执行PUT请求', async () => {
      const { put } = useApi()
      const mockResponse = { success: true, data: { id: 1 } }
      const requestBody = { name: 'Updated User' }
      mockFetch.mockResolvedValue(mockResponse)

      const result = await put('/users/1', requestBody)

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/v1/users/1',
        expect.objectContaining({
          method: 'PUT',
          body: requestBody
        })
      )
      expect(result).toEqual(mockResponse)
    })

    it('应该正确执行DELETE请求', async () => {
      const { delete: del } = useApi()
      const mockResponse = { success: true }
      mockFetch.mockResolvedValue(mockResponse)

      const result = await del('/users/1')

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/v1/users/1',
        expect.objectContaining({
          method: 'DELETE'
        })
      )
      expect(result).toEqual(mockResponse)
    })
  })

  describe('文件上传', () => {
    it('应该正确处理文件上传', async () => {
      const { upload } = useApi()
      const mockResponse = { success: true, data: { url: 'http://example.com/file.jpg' } }
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      const mockProgress = vi.fn()
      mockFetch.mockResolvedValue(mockResponse)

      const result = await upload('/upload', mockFile, mockProgress)

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/v1/upload',
        expect.objectContaining({
          method: 'POST',
          body: expect.any(FormData)
        })
      )
      expect(result).toEqual(mockResponse)
    })
  })
})
