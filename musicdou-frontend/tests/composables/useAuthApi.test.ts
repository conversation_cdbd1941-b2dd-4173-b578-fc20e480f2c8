import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// Mock useApi
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  upload: vi.fn()
}

global.useApi = vi.fn(() => mockApi)

import { useAuthApi } from '../../app/composables/useAuthApi'

describe('useAuthApi Composable', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('用户认证', () => {
    it('应该正确调用注册API', async () => {
      const { register } = useAuthApi()
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      }
      const mockResponse = { success: true, data: { user: { id: 1 }, token: 'token123' } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await register(userData)

      expect(mockApi.post).toHaveBeenCalledWith('/auth/register', userData)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确调用登录API', async () => {
      const { login } = useAuthApi()
      const credentials = {
        email: '<EMAIL>',
        password: 'password123'
      }
      const mockResponse = { success: true, data: { user: { id: 1 }, token: 'token123' } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await login(credentials)

      expect(mockApi.post).toHaveBeenCalledWith('/auth/login', credentials)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确调用登出API', async () => {
      const { logout } = useAuthApi()
      const mockResponse = { success: true, data: { message: '登出成功' } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await logout()

      expect(mockApi.post).toHaveBeenCalledWith('/auth/logout')
      expect(result).toEqual(mockResponse)
    })

    it('应该正确调用刷新token API', async () => {
      const { refreshToken } = useAuthApi()
      const mockResponse = { success: true, data: { token: 'newtoken123' } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await refreshToken()

      expect(mockApi.post).toHaveBeenCalledWith('/auth/refresh')
      expect(result).toEqual(mockResponse)
    })
  })

  describe('用户资料管理', () => {
    it('应该正确获取当前用户信息', async () => {
      const { getCurrentUser } = useAuthApi()
      const mockResponse = { success: true, data: { id: 1, username: 'testuser' } }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getCurrentUser()

      expect(mockApi.get).toHaveBeenCalledWith('/auth/profile')
      expect(result).toEqual(mockResponse)
    })

    it('应该正确更新用户资料', async () => {
      const { updateProfile } = useAuthApi()
      const profileData = { username: 'newusername', bio: '新的个人简介' }
      const mockResponse = { success: true, data: { id: 1, ...profileData } }
      mockApi.put.mockResolvedValue(mockResponse)

      const result = await updateProfile(profileData)

      expect(mockApi.put).toHaveBeenCalledWith('/auth/profile', profileData)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确修改密码', async () => {
      const { changePassword } = useAuthApi()
      const passwordData = {
        currentPassword: 'oldpassword',
        newPassword: 'newpassword123'
      }
      const mockResponse = { success: true, data: { message: '密码修改成功' } }
      mockApi.put.mockResolvedValue(mockResponse)

      const result = await changePassword(passwordData)

      expect(mockApi.put).toHaveBeenCalledWith('/auth/password', passwordData)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确上传头像', async () => {
      const { uploadAvatar } = useAuthApi()
      const file = new File(['avatar'], 'avatar.jpg', { type: 'image/jpeg' })
      const mockResponse = { success: true, data: { avatarUrl: 'http://example.com/avatar.jpg' } }
      mockApi.upload.mockResolvedValue(mockResponse)

      const result = await uploadAvatar(file)

      expect(mockApi.upload).toHaveBeenCalledWith('/auth/avatar', file)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('签到功能', () => {
    it('应该正确调用每日签到API', async () => {
      const { dailyCheckin } = useAuthApi()
      const mockResponse = { success: true, data: { points: 10, message: '签到成功' } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await dailyCheckin()

      expect(mockApi.post).toHaveBeenCalledWith('/auth/signin')
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取签到状态', async () => {
      const { getCheckinStatus } = useAuthApi()
      const mockResponse = {
        success: true,
        data: {
          hasCheckedIn: true,
          consecutiveDays: 5,
          totalCheckins: 30,
          nextReward: 20
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getCheckinStatus()

      expect(mockApi.get).toHaveBeenCalledWith('/auth/checkin/status')
      expect(result).toEqual(mockResponse)
    })
  })

  describe('密码重置', () => {
    it('应该正确调用忘记密码API', async () => {
      const { forgotPassword } = useAuthApi()
      const email = '<EMAIL>'
      const mockResponse = { success: true, data: { message: '重置邮件已发送' } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await forgotPassword(email)

      expect(mockApi.post).toHaveBeenCalledWith('/auth/forgot-password', { email })
      expect(result).toEqual(mockResponse)
    })

    it('应该正确调用重置密码API', async () => {
      const { resetPassword } = useAuthApi()
      const resetData = {
        token: 'reset-token-123',
        newPassword: 'newpassword123'
      }
      const mockResponse = { success: true, data: { message: '密码重置成功' } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await resetPassword(resetData)

      expect(mockApi.post).toHaveBeenCalledWith('/auth/reset-password', resetData)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('邮箱验证', () => {
    it('应该正确调用验证邮箱API', async () => {
      const { verifyEmail } = useAuthApi()
      const token = 'verify-token-123'
      const mockResponse = { success: true, data: { message: '邮箱验证成功' } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await verifyEmail(token)

      expect(mockApi.post).toHaveBeenCalledWith('/auth/verify-email', { token })
      expect(result).toEqual(mockResponse)
    })

    it('应该正确调用重发验证邮件API', async () => {
      const { resendVerificationEmail } = useAuthApi()
      const mockResponse = { success: true, data: { message: '验证邮件已重发' } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await resendVerificationEmail()

      expect(mockApi.post).toHaveBeenCalledWith('/auth/resend-verification')
      expect(result).toEqual(mockResponse)
    })
  })

  describe('账户管理', () => {
    it('应该正确调用删除账户API', async () => {
      const { deleteAccount } = useAuthApi()
      const password = 'password123'
      const mockResponse = { success: true, data: { message: '账户删除成功' } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await deleteAccount(password)

      expect(mockApi.post).toHaveBeenCalledWith('/auth/delete-account', { password })
      expect(result).toEqual(mockResponse)
    })
  })

  describe('API方法可用性', () => {
    it('应该返回所有必要的API方法', () => {
      const authApi = useAuthApi()

      expect(authApi).toHaveProperty('register')
      expect(authApi).toHaveProperty('login')
      expect(authApi).toHaveProperty('getCurrentUser')
      expect(authApi).toHaveProperty('updateProfile')
      expect(authApi).toHaveProperty('changePassword')
      expect(authApi).toHaveProperty('uploadAvatar')
      expect(authApi).toHaveProperty('dailyCheckin')
      expect(authApi).toHaveProperty('getCheckinStatus')
      expect(authApi).toHaveProperty('logout')
      expect(authApi).toHaveProperty('refreshToken')
      expect(authApi).toHaveProperty('forgotPassword')
      expect(authApi).toHaveProperty('resetPassword')
      expect(authApi).toHaveProperty('verifyEmail')
      expect(authApi).toHaveProperty('resendVerificationEmail')
      expect(authApi).toHaveProperty('deleteAccount')
    })

    it('所有方法都应该是函数', () => {
      const authApi = useAuthApi()

      Object.values(authApi).forEach(method => {
        expect(typeof method).toBe('function')
      })
    })
  })
})
