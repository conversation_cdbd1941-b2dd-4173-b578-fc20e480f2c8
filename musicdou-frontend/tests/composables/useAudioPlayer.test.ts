import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// Mock player store
const mockPlayerStore = {
  state: {
    currentTrack: null,
    isPlaying: false,
    volume: 0.8,
    repeat: 'none'
  },
  setPlaying: vi.fn(),
  setDuration: vi.fn(),
  setCurrentTime: vi.fn(),
  setVolume: vi.fn(),
  playTrack: vi.fn(),
  playNext: vi.fn(),
  playPrevious: vi.fn(),
  stop: vi.fn(),
  canPlayNext: true,
  canPlayPrevious: true
}

// Mock global functions
global.usePlayerStore = vi.fn(() => mockPlayerStore)
global.watch = vi.fn()
global.onMounted = vi.fn()
global.onUnmounted = vi.fn()

// Mock Howler
vi.mock('howler', () => ({
  Howl: vi.fn(),
  Howler: {
    volume: vi.fn()
  }
}))

import { useAudioPlayer } from '../../app/composables/useAudioPlayer'

describe('useAudioPlayer Composable', () => {
  const mockTrack = {
    id: 1,
    title: 'Test Song',
    artist: 'Test Artist',
    url: 'http://example.com/song.mp3',
    duration: 180,
    cover: 'http://example.com/cover.jpg'
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockPlayerStore.state.isPlaying = false
    mockPlayerStore.state.currentTrack = null
    mockPlayerStore.canPlayNext = true
    mockPlayerStore.canPlayPrevious = true
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('基础功能', () => {
    it('应该正确初始化并返回播放控制方法', () => {
      const audioPlayer = useAudioPlayer()

      expect(audioPlayer).toHaveProperty('playTrack')
      expect(audioPlayer).toHaveProperty('togglePlay')
      expect(audioPlayer).toHaveProperty('playNext')
      expect(audioPlayer).toHaveProperty('playPrevious')
      expect(audioPlayer).toHaveProperty('stop')
      expect(audioPlayer).toHaveProperty('setVolume')
      expect(audioPlayer).toHaveProperty('seekTo')
      expect(audioPlayer).toHaveProperty('cleanup')
    })

    it('应该正确设置生命周期钩子', () => {
      useAudioPlayer()

      expect(global.onMounted).toHaveBeenCalled()
      expect(global.onUnmounted).toHaveBeenCalled()
    })

    it('应该正确设置状态监听器', () => {
      useAudioPlayer()

      // 验证是否设置了监听器 (currentTrack, isPlaying, volume)
      expect(global.watch).toHaveBeenCalledTimes(3)
    })
  })

  describe('播放队列控制', () => {
    it('当无法播放下一首时不应该执行操作', async () => {
      const { playNext } = useAudioPlayer()
      mockPlayerStore.canPlayNext = false

      await playNext()

      expect(mockPlayerStore.playNext).not.toHaveBeenCalled()
    })

    it('当无法播放上一首时不应该执行操作', async () => {
      const { playPrevious } = useAudioPlayer()
      mockPlayerStore.canPlayPrevious = false

      await playPrevious()

      expect(mockPlayerStore.playPrevious).not.toHaveBeenCalled()
    })
  })

  describe('音量控制', () => {
    it('应该限制音量范围在0-1之间', () => {
      const { setVolume } = useAudioPlayer()

      // 测试超出上限
      setVolume(1.5)
      expect(mockPlayerStore.setVolume).toHaveBeenCalledWith(1)

      // 测试超出下限
      setVolume(-0.5)
      expect(mockPlayerStore.setVolume).toHaveBeenCalledWith(0)
    })
  })

  describe('Store 交互', () => {
    it('应该正确使用 player store', () => {
      useAudioPlayer()

      expect(global.usePlayerStore).toHaveBeenCalled()
    })

    it('应该正确设置音量到 store', () => {
      const { setVolume } = useAudioPlayer()

      setVolume(0.7)

      expect(mockPlayerStore.setVolume).toHaveBeenCalledWith(0.7)
    })
  })
})
