import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// Mock useApi
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn()
}

global.useApi = vi.fn(() => mockApi)

import { useRecommendationApi } from '../../app/composables/useRecommendationApi'

describe('useRecommendationApi Composable', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('音乐推荐', () => {
    it('应该正确获取个性化音乐推荐', async () => {
      const { getPersonalizedMusic } = useRecommendationApi()
      const params = { limit: 20, type: 'daily' }
      const mockResponse = {
        success: true,
        data: {
          recommendations: [
            { id: 1, title: 'Recommended Song 1', score: 0.95 },
            { id: 2, title: 'Recommended Song 2', score: 0.88 }
          ],
          algorithm: 'collaborative_filtering',
          refreshTime: '2023-01-01T00:00:00Z'
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getPersonalizedMusic(params)

      expect(mockApi.get).toHaveBeenCalledWith('/recommendations/music/personalized', params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取相似音乐推荐', async () => {
      const { getSimilarMusic } = useRecommendationApi()
      const musicId = '123'
      const params = { limit: 10 }
      const mockResponse = {
        success: true,
        data: {
          recommendations: [
            { id: 2, title: 'Similar Song 1', similarity: 0.92 },
            { id: 3, title: 'Similar Song 2', similarity: 0.87 }
          ],
          basedOn: { id: 123, title: 'Original Song' }
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getSimilarMusic(musicId, params)

      expect(mockApi.get).toHaveBeenCalledWith(`/recommendations/music/${musicId}/similar`, params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取基于流派的推荐', async () => {
      const { getMusicByGenre } = useRecommendationApi()
      const genre = 'pop'
      const params = { limit: 15, subGenres: ['pop-rock', 'synth-pop'] }
      const mockResponse = {
        success: true,
        data: {
          recommendations: [
            { id: 1, title: 'Pop Song 1', genre: 'pop' },
            { id: 2, title: 'Pop Song 2', genre: 'pop-rock' }
          ],
          genre: 'pop'
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getMusicByGenre(genre, params)

      expect(mockApi.get).toHaveBeenCalledWith(`/recommendations/music/genre/${genre}`, params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取基于心情的推荐', async () => {
      const { getMusicByMood } = useRecommendationApi()
      const mood = 'happy'
      const params = { limit: 12 }
      const mockResponse = {
        success: true,
        data: {
          recommendations: [
            { id: 1, title: 'Happy Song 1', mood: 'happy', energy: 0.8 },
            { id: 2, title: 'Happy Song 2', mood: 'happy', energy: 0.9 }
          ],
          mood: 'happy'
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getMusicByMood(mood, params)

      expect(mockApi.get).toHaveBeenCalledWith(`/recommendations/music/mood/${mood}`, params)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('歌单推荐', () => {
    it('应该正确获取推荐歌单', async () => {
      const { getRecommendedPlaylists } = useRecommendationApi()
      const params = { limit: 10, type: 'curated' }
      const mockResponse = {
        success: true,
        data: {
          recommendations: [
            { id: 1, name: 'Recommended Playlist 1', score: 0.93 },
            { id: 2, name: 'Recommended Playlist 2', score: 0.89 }
          ],
          type: 'curated'
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getRecommendedPlaylists(params)

      expect(mockApi.get).toHaveBeenCalledWith('/recommendations/playlists', params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取相似歌单', async () => {
      const { getSimilarPlaylists } = useRecommendationApi()
      const playlistId = '123'
      const params = { limit: 8 }
      const mockResponse = {
        success: true,
        data: {
          recommendations: [
            { id: 2, name: 'Similar Playlist 1', similarity: 0.85 },
            { id: 3, name: 'Similar Playlist 2', similarity: 0.82 }
          ],
          basedOn: { id: 123, name: 'Original Playlist' }
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getSimilarPlaylists(playlistId, params)

      expect(mockApi.get).toHaveBeenCalledWith(`/recommendations/playlists/${playlistId}/similar`, params)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('用户推荐', () => {
    it('应该正确获取推荐用户', async () => {
      const { getRecommendedUsers } = useRecommendationApi()
      const params = { limit: 15, type: 'similar_taste' }
      const mockResponse = {
        success: true,
        data: {
          recommendations: [
            { id: 1, username: 'user1', commonInterests: ['pop', 'rock'] },
            { id: 2, username: 'user2', commonInterests: ['jazz', 'blues'] }
          ],
          type: 'similar_taste'
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getRecommendedUsers(params)

      expect(mockApi.get).toHaveBeenCalledWith('/recommendations/users', params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取推荐关注', async () => {
      const { getRecommendedFollows } = useRecommendationApi()
      const params = { limit: 10 }
      const mockResponse = {
        success: true,
        data: {
          recommendations: [
            { id: 1, username: 'artist1', type: 'artist', followers: 10000 },
            { id: 2, username: 'curator1', type: 'curator', playlists: 50 }
          ]
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getRecommendedFollows(params)

      expect(mockApi.get).toHaveBeenCalledWith('/recommendations/users/follow', params)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('推荐反馈', () => {
    it('应该正确提交推荐反馈', async () => {
      const { submitFeedback } = useRecommendationApi()
      const feedbackData = {
        recommendationId: '123',
        type: 'music',
        action: 'like',
        rating: 5
      }
      const mockResponse = {
        success: true,
        data: { message: '反馈提交成功' }
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await submitFeedback(feedbackData)

      expect(mockApi.post).toHaveBeenCalledWith('/recommendations/feedback', feedbackData)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确标记推荐为不感兴趣', async () => {
      const { markNotInterested } = useRecommendationApi()
      const recommendationId = '123'
      const type = 'music'
      const mockResponse = {
        success: true,
        data: { message: '已标记为不感兴趣' }
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await markNotInterested(recommendationId, type)

      expect(mockApi.post).toHaveBeenCalledWith('/recommendations/not-interested', {
        recommendationId,
        type
      })
      expect(result).toEqual(mockResponse)
    })
  })

  describe('推荐设置', () => {
    it('应该正确获取推荐设置', async () => {
      const { getRecommendationSettings } = useRecommendationApi()
      const mockResponse = {
        success: true,
        data: {
          preferences: {
            genres: ['pop', 'rock', 'jazz'],
            excludeGenres: ['metal'],
            moodBased: true,
            collaborativeFiltering: true
          },
          privacy: {
            shareListeningHistory: true,
            allowPersonalization: true
          }
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getRecommendationSettings()

      expect(mockApi.get).toHaveBeenCalledWith('/recommendations/settings')
      expect(result).toEqual(mockResponse)
    })

    it('应该正确更新推荐设置', async () => {
      const { updateRecommendationSettings } = useRecommendationApi()
      const settings = {
        preferences: {
          genres: ['pop', 'electronic'],
          moodBased: false
        },
        privacy: {
          shareListeningHistory: false
        }
      }
      const mockResponse = {
        success: true,
        data: { message: '设置更新成功', settings }
      }
      mockApi.put.mockResolvedValue(mockResponse)

      const result = await updateRecommendationSettings(settings)

      expect(mockApi.put).toHaveBeenCalledWith('/recommendations/settings', settings)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('推荐统计', () => {
    it('应该正确获取推荐统计', async () => {
      const { getRecommendationStats } = useRecommendationApi()
      const params = { period: 'month' }
      const mockResponse = {
        success: true,
        data: {
          totalRecommendations: 1000,
          acceptedRecommendations: 250,
          acceptanceRate: 0.25,
          topGenres: ['pop', 'rock', 'jazz'],
          averageRating: 4.2
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getRecommendationStats(params)

      expect(mockApi.get).toHaveBeenCalledWith('/recommendations/stats', params)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('API方法可用性', () => {
    it('应该返回所有必要的API方法', () => {
      const recommendationApi = useRecommendationApi()

      expect(recommendationApi).toHaveProperty('getPersonalizedMusic')
      expect(recommendationApi).toHaveProperty('getSimilarMusic')
      expect(recommendationApi).toHaveProperty('getMusicByGenre')
      expect(recommendationApi).toHaveProperty('getMusicByMood')
      expect(recommendationApi).toHaveProperty('getRecommendedPlaylists')
      expect(recommendationApi).toHaveProperty('getSimilarPlaylists')
      expect(recommendationApi).toHaveProperty('getRecommendedUsers')
      expect(recommendationApi).toHaveProperty('getRecommendedFollows')
      expect(recommendationApi).toHaveProperty('submitFeedback')
      expect(recommendationApi).toHaveProperty('markNotInterested')
      expect(recommendationApi).toHaveProperty('getRecommendationSettings')
      expect(recommendationApi).toHaveProperty('updateRecommendationSettings')
      expect(recommendationApi).toHaveProperty('getRecommendationStats')
    })

    it('所有方法都应该是函数', () => {
      const recommendationApi = useRecommendationApi()

      Object.values(recommendationApi).forEach(method => {
        expect(typeof method).toBe('function')
      })
    })
  })
})
