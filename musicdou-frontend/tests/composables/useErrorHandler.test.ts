import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// Mock global objects
global.navigator = { userAgent: 'test-agent' } as any
global.window = { location: { href: 'http://localhost:3000' } } as any

import { useErrorHandler } from '../../app/composables/useErrorHandler'

describe('useErrorHandler Composable', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('API错误处理', () => {
    it('应该正确处理网络错误', () => {
      const { handleApiError } = useErrorHandler()
      const networkError = { message: 'Network Error' }
      
      const result = handleApiError(networkError)
      
      expect(result).toEqual({
        code: 'NETWORK_ERROR',
        message: '网络连接错误',
        details: networkError
      })
    })

    it('应该正确处理标准API错误格式', () => {
      const { handleApiError } = useErrorHandler()
      const apiError = {
        response: {
          status: 400,
          data: {
            error: 'INVALID_CREDENTIALS',
            message: '用户名或密码错误'
          }
        }
      }
      
      const result = handleApiError(apiError)
      
      expect(result).toEqual({
        code: 'INVALID_CREDENTIALS',
        message: '用户名或密码错误',
        details: apiError.response.data
      })
    })

    it('应该正确处理备用错误格式', () => {
      const { handleApiError } = useErrorHandler()
      const apiError = {
        response: {
          status: 404,
          data: {
            code: 'MUSIC_NOT_FOUND',
            message: '音乐不存在'
          }
        }
      }
      
      const result = handleApiError(apiError)
      
      expect(result).toEqual({
        code: 'MUSIC_NOT_FOUND',
        message: '音乐不存在',
        details: apiError.response.data
      })
    })

    it('应该正确处理HTTP状态码错误', () => {
      const { handleApiError } = useErrorHandler()
      const httpError = {
        response: {
          status: 500,
          data: {}
        }
      }
      
      const result = handleApiError(httpError)
      
      expect(result).toEqual({
        code: 'HTTP_500',
        message: '服务器内部错误',
        details: httpError.response.data
      })
    })
  })

  describe('HTTP状态码消息', () => {
    it('应该返回正确的HTTP状态码消息', () => {
      const { getHttpStatusMessage } = useErrorHandler()
      
      expect(getHttpStatusMessage(400)).toBe('请求参数错误')
      expect(getHttpStatusMessage(401)).toBe('未授权访问')
      expect(getHttpStatusMessage(403)).toBe('权限不足')
      expect(getHttpStatusMessage(404)).toBe('资源不存在')
      expect(getHttpStatusMessage(500)).toBe('服务器内部错误')
    })

    it('应该处理未知状态码', () => {
      const { getHttpStatusMessage } = useErrorHandler()
      
      expect(getHttpStatusMessage(999)).toBe('HTTP错误 999')
    })
  })

  describe('错误显示', () => {
    it('应该正确显示字符串错误', () => {
      const { showError } = useErrorHandler()
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      showError('测试错误消息')
      
      expect(consoleSpy).toHaveBeenCalledWith('错误:', '测试错误消息')
      consoleSpy.mockRestore()
    })

    it('应该正确显示错误对象', () => {
      const { showError } = useErrorHandler()
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      const error = {
        code: 'TEST_ERROR',
        message: '测试错误对象',
        details: {}
      }
      
      showError(error)
      
      expect(consoleSpy).toHaveBeenCalledWith('错误:', '测试错误对象')
      consoleSpy.mockRestore()
    })
  })

  describe('表单验证错误处理', () => {
    it('应该正确处理表单验证错误', () => {
      const { handleValidationError } = useErrorHandler()
      const validationErrors = {
        email: ['邮箱格式不正确'],
        password: ['密码长度不足', '密码必须包含数字']
      }
      
      const result = handleValidationError(validationErrors)
      
      expect(result).toBe('邮箱格式不正确')
    })

    it('应该处理空的验证错误', () => {
      const { handleValidationError } = useErrorHandler()
      
      const result = handleValidationError({})
      
      expect(result).toBe('表单验证失败')
    })
  })

  describe('错误类型检查', () => {
    it('应该正确检查字符串错误类型', () => {
      const { isErrorType } = useErrorHandler()
      
      expect(isErrorType('INVALID_CREDENTIALS', 'INVALID_CREDENTIALS')).toBe(true)
      expect(isErrorType('INVALID_CREDENTIALS', 'USER_NOT_FOUND')).toBe(false)
    })

    it('应该正确检查错误对象类型', () => {
      const { isErrorType } = useErrorHandler()
      const error = { code: 'MUSIC_NOT_FOUND' }
      
      expect(isErrorType(error, 'MUSIC_NOT_FOUND')).toBe(true)
      expect(isErrorType(error, 'USER_NOT_FOUND')).toBe(false)
    })

    it('应该正确检查API响应错误类型', () => {
      const { isErrorType } = useErrorHandler()
      const apiError = {
        response: {
          data: {
            error: 'PLAYLIST_NOT_FOUND'
          }
        }
      }
      
      expect(isErrorType(apiError, 'PLAYLIST_NOT_FOUND')).toBe(true)
      expect(isErrorType(apiError, 'MUSIC_NOT_FOUND')).toBe(false)
    })
  })

  describe('友好错误消息', () => {
    it('应该返回错误消息映射中的友好消息', () => {
      const { getFriendlyMessage } = useErrorHandler()
      
      expect(getFriendlyMessage('INVALID_CREDENTIALS')).toBe('用户名或密码错误')
      expect(getFriendlyMessage('MUSIC_NOT_FOUND')).toBe('音乐不存在')
    })

    it('应该返回错误对象的消息', () => {
      const { getFriendlyMessage } = useErrorHandler()
      const error = { message: '自定义错误消息' }
      
      expect(getFriendlyMessage(error)).toBe('自定义错误消息')
    })

    it('应该返回API响应的消息', () => {
      const { getFriendlyMessage } = useErrorHandler()
      const apiError = {
        response: {
          data: {
            message: 'API错误消息'
          }
        }
      }
      
      expect(getFriendlyMessage(apiError)).toBe('API错误消息')
    })

    it('应该处理未知错误', () => {
      const { getFriendlyMessage } = useErrorHandler()
      
      expect(getFriendlyMessage({})).toBe('发生了未知错误')
    })
  })

  describe('错误日志记录', () => {
    it('应该正确记录错误日志', () => {
      const { logError } = useErrorHandler()
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      const error = {
        code: 'TEST_ERROR',
        message: '测试错误',
        details: {}
      }
      
      logError(error, '测试上下文')
      
      expect(consoleSpy).toHaveBeenCalledWith('错误日志:', expect.objectContaining({
        error,
        context: '测试上下文',
        timestamp: expect.any(String),
        userAgent: 'test-agent',
        url: 'http://localhost:3000'
      }))
      consoleSpy.mockRestore()
    })
  })

  describe('重试机制', () => {
    it('应该在成功时返回结果', async () => {
      const { withRetry } = useErrorHandler()
      const operation = vi.fn().mockResolvedValue('success')
      
      const result = await withRetry(operation, 3, 100)
      
      expect(result).toBe('success')
      expect(operation).toHaveBeenCalledTimes(1)
    })

    it('应该在失败后重试', async () => {
      const { withRetry } = useErrorHandler()
      const operation = vi.fn()
        .mockRejectedValueOnce(new Error('第一次失败'))
        .mockRejectedValueOnce(new Error('第二次失败'))
        .mockResolvedValue('success')
      
      const result = await withRetry(operation, 3, 10)
      
      expect(result).toBe('success')
      expect(operation).toHaveBeenCalledTimes(3)
    })

    it('应该在达到最大重试次数后抛出错误', async () => {
      const { withRetry } = useErrorHandler()
      const operation = vi.fn().mockRejectedValue(new Error('持续失败'))
      
      await expect(withRetry(operation, 2, 10)).rejects.toThrow('持续失败')
      expect(operation).toHaveBeenCalledTimes(2)
    })
  })

  describe('安全异步操作', () => {
    it('应该在成功时返回结果', async () => {
      const { safeAsync } = useErrorHandler()
      const operation = vi.fn().mockResolvedValue('success')
      
      const result = await safeAsync(operation)
      
      expect(result).toBe('success')
    })

    it('应该在失败时返回fallback值', async () => {
      const { safeAsync } = useErrorHandler()
      const operation = vi.fn().mockRejectedValue(new Error('操作失败'))
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      const result = await safeAsync(operation, 'fallback')
      
      expect(result).toBe('fallback')
      consoleSpy.mockRestore()
    })

    it('应该在失败时返回undefined（无fallback）', async () => {
      const { safeAsync } = useErrorHandler()
      const operation = vi.fn().mockRejectedValue(new Error('操作失败'))
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      const result = await safeAsync(operation)
      
      expect(result).toBeUndefined()
      consoleSpy.mockRestore()
    })
  })
})
