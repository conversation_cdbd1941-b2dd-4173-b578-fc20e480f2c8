import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// Mock useApi
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn()
}

global.useApi = vi.fn(() => mockApi)

import { useSearchApi } from '../../app/composables/useSearchApi'

describe('useSearchApi Composable', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('综合搜索', () => {
    it('应该正确执行综合搜索', async () => {
      const { search } = useSearchApi()
      const params = {
        q: 'test query',
        page: 1,
        limit: 20,
        type: 'all'
      }
      const mockResponse = {
        success: true,
        data: {
          music: { items: [{ id: 1, title: 'Test Song' }], total: 10 },
          playlists: { items: [{ id: 1, name: 'Test Playlist' }], total: 5 },
          users: { items: [{ id: 1, username: 'testuser' }], total: 3 }
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await search(params)

      expect(mockApi.get).toHaveBeenCalledWith('/search', params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确执行快速搜索', async () => {
      const { quickSearch } = useSearchApi()
      const query = 'quick test'
      const mockResponse = {
        success: true,
        data: {
          suggestions: ['quick test song', 'quick test artist'],
          results: [
            { type: 'music', id: 1, title: 'Quick Song' },
            { type: 'user', id: 1, username: 'quickuser' }
          ]
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await quickSearch(query)

      expect(mockApi.get).toHaveBeenCalledWith('/search/quick', { q: query })
      expect(result).toEqual(mockResponse)
    })
  })

  describe('分类搜索', () => {
    it('应该正确搜索音乐', async () => {
      const { searchMusic } = useSearchApi()
      const params = {
        q: 'music query',
        page: 1,
        limit: 20,
        genre: 'pop',
        sortBy: 'relevance'
      }
      const mockResponse = {
        success: true,
        data: {
          items: [{ id: 1, title: 'Music Result' }],
          total: 50,
          page: 1,
          limit: 20
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await searchMusic(params)

      expect(mockApi.get).toHaveBeenCalledWith('/search/music', params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确搜索歌单', async () => {
      const { searchPlaylists } = useSearchApi()
      const params = {
        q: 'playlist query',
        page: 1,
        limit: 15,
        sortBy: 'popular'
      }
      const mockResponse = {
        success: true,
        data: {
          items: [{ id: 1, name: 'Playlist Result' }],
          total: 25,
          page: 1,
          limit: 15
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await searchPlaylists(params)

      expect(mockApi.get).toHaveBeenCalledWith('/search/playlists', params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确搜索用户', async () => {
      const { searchUsers } = useSearchApi()
      const params = {
        q: 'user query',
        page: 1,
        limit: 10
      }
      const mockResponse = {
        success: true,
        data: {
          items: [{ id: 1, username: 'userresult' }],
          total: 15,
          page: 1,
          limit: 10
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await searchUsers(params)

      expect(mockApi.get).toHaveBeenCalledWith('/search/users', params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确搜索艺术家', async () => {
      const { searchArtists } = useSearchApi()
      const params = {
        q: 'artist query',
        page: 1,
        limit: 12
      }
      const mockResponse = {
        success: true,
        data: {
          items: [{ id: 1, name: 'Artist Result' }],
          total: 8,
          page: 1,
          limit: 12
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await searchArtists(params)

      expect(mockApi.get).toHaveBeenCalledWith('/search/artists', params)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('搜索建议', () => {
    it('应该正确获取搜索建议', async () => {
      const { getSuggestions } = useSearchApi()
      const query = 'test'
      const mockResponse = {
        success: true,
        data: {
          suggestions: [
            'test song',
            'test artist',
            'test playlist'
          ]
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getSuggestions(query)

      expect(mockApi.get).toHaveBeenCalledWith('/search/suggestions', { q: query })
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取热门搜索', async () => {
      const { getTrendingSearches } = useSearchApi()
      const params = { limit: 10, period: 'day' }
      const mockResponse = {
        success: true,
        data: {
          trending: [
            { query: 'popular song', count: 1000 },
            { query: 'trending artist', count: 800 }
          ]
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getTrendingSearches(params)

      expect(mockApi.get).toHaveBeenCalledWith('/search/trending', params)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('搜索历史', () => {
    it('应该正确获取搜索历史', async () => {
      const { getSearchHistory } = useSearchApi()
      const params = { limit: 20 }
      const mockResponse = {
        success: true,
        data: {
          history: [
            { query: 'recent search 1', timestamp: '2023-01-01' },
            { query: 'recent search 2', timestamp: '2023-01-02' }
          ]
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getSearchHistory(params)

      expect(mockApi.get).toHaveBeenCalledWith('/search/history', params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确保存搜索记录', async () => {
      const { saveSearchQuery } = useSearchApi()
      const query = 'saved search'
      const mockResponse = {
        success: true,
        data: { message: '搜索记录已保存' }
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await saveSearchQuery(query)

      expect(mockApi.post).toHaveBeenCalledWith('/search/history', { query })
      expect(result).toEqual(mockResponse)
    })

    it('应该正确清除搜索历史', async () => {
      const { clearSearchHistory } = useSearchApi()
      const mockResponse = {
        success: true,
        data: { message: '搜索历史已清除' }
      }
      mockApi.delete.mockResolvedValue(mockResponse)

      const result = await clearSearchHistory()

      expect(mockApi.delete).toHaveBeenCalledWith('/search/history')
      expect(result).toEqual(mockResponse)
    })

    it('应该正确删除单个搜索记录', async () => {
      const { deleteSearchQuery } = useSearchApi()
      const query = 'delete this search'
      const mockResponse = {
        success: true,
        data: { message: '搜索记录已删除' }
      }
      mockApi.delete.mockResolvedValue(mockResponse)

      const result = await deleteSearchQuery(query)

      expect(mockApi.delete).toHaveBeenCalledWith('/search/history/item', { query })
      expect(result).toEqual(mockResponse)
    })
  })

  describe('高级搜索', () => {
    it('应该正确执行高级搜索', async () => {
      const { advancedSearch } = useSearchApi()
      const params = {
        q: 'advanced query',
        filters: {
          genre: ['pop', 'rock'],
          year: { min: 2020, max: 2023 },
          duration: { min: 180, max: 300 }
        },
        sortBy: 'relevance',
        page: 1,
        limit: 20
      }
      const mockResponse = {
        success: true,
        data: {
          items: [{ id: 1, title: 'Advanced Result' }],
          total: 30,
          page: 1,
          limit: 20,
          filters: params.filters
        }
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await advancedSearch(params)

      expect(mockApi.post).toHaveBeenCalledWith('/search/advanced', params)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('API方法可用性', () => {
    it('应该返回所有必要的API方法', () => {
      const searchApi = useSearchApi()

      expect(searchApi).toHaveProperty('search')
      expect(searchApi).toHaveProperty('quickSearch')
      expect(searchApi).toHaveProperty('searchMusic')
      expect(searchApi).toHaveProperty('searchPlaylists')
      expect(searchApi).toHaveProperty('searchUsers')
      expect(searchApi).toHaveProperty('searchArtists')
      expect(searchApi).toHaveProperty('getSuggestions')
      expect(searchApi).toHaveProperty('getTrendingSearches')
      expect(searchApi).toHaveProperty('getSearchHistory')
      expect(searchApi).toHaveProperty('saveSearchQuery')
      expect(searchApi).toHaveProperty('clearSearchHistory')
      expect(searchApi).toHaveProperty('deleteSearchQuery')
      expect(searchApi).toHaveProperty('advancedSearch')
    })

    it('所有方法都应该是函数', () => {
      const searchApi = useSearchApi()

      Object.values(searchApi).forEach(method => {
        expect(typeof method).toBe('function')
      })
    })
  })
})
