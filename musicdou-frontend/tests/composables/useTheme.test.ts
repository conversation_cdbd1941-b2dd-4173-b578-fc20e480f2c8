import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { ref, computed } from 'vue'

// Mock useColorMode
const mockColorMode = ref('light')
const mockColorModeObject = {
  value: mockColorMode.value,
  preference: 'light'
}

// Create reactive mock
Object.defineProperty(mockColorModeObject, 'value', {
  get: () => mockColorMode.value,
  set: (val) => { mockColorMode.value = val }
})

Object.defineProperty(mockColorModeObject, 'preference', {
  get: () => mockColorMode.value,
  set: (val) => { mockColorMode.value = val }
})

global.useColorMode = vi.fn(() => mockColorModeObject)
global.computed = computed

import { useTheme } from '../../app/composables/useTheme'

describe('useTheme Composable', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockColorMode.value = 'light'
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('基础功能', () => {
    it('应该返回当前主题', () => {
      const { currentTheme } = useTheme()
      
      expect(currentTheme.value).toBe('light')
    })

    it('应该正确设置主题', () => {
      const { setTheme, currentTheme } = useTheme()
      
      setTheme('dark')
      expect(currentTheme.value).toBe('dark')
      
      setTheme('light')
      expect(currentTheme.value).toBe('light')
    })

    it('应该正确切换主题', () => {
      const { toggleTheme, currentTheme } = useTheme()
      
      // 从浅色切换到深色
      mockColorMode.value = 'light'
      toggleTheme()
      expect(mockColorMode.value).toBe('dark')
      
      // 从深色切换到浅色
      toggleTheme()
      expect(mockColorMode.value).toBe('light')
    })
  })

  describe('主题状态检测', () => {
    it('应该正确检测深色主题', () => {
      const { isDark, isLight } = useTheme()
      
      mockColorMode.value = 'dark'
      expect(isDark.value).toBe(true)
      expect(isLight.value).toBe(false)
    })

    it('应该正确检测浅色主题', () => {
      const { isDark, isLight } = useTheme()
      
      mockColorMode.value = 'light'
      expect(isDark.value).toBe(false)
      expect(isLight.value).toBe(true)
    })

    it('应该正确处理系统主题', () => {
      const { isDark, isLight } = useTheme()
      
      mockColorMode.value = 'system'
      expect(isDark.value).toBe(false)
      expect(isLight.value).toBe(false)
    })
  })

  describe('主题图标', () => {
    it('应该为深色主题返回月亮图标', () => {
      const { themeIcon } = useTheme()
      
      mockColorMode.value = 'dark'
      expect(themeIcon.value).toBe('MoonIcon')
    })

    it('应该为浅色主题返回太阳图标', () => {
      const { themeIcon } = useTheme()
      
      mockColorMode.value = 'light'
      expect(themeIcon.value).toBe('SunIcon')
    })

    it('应该为系统主题返回电脑图标', () => {
      const { themeIcon } = useTheme()
      
      mockColorMode.value = 'system'
      expect(themeIcon.value).toBe('ComputerDesktopIcon')
    })
  })

  describe('主题名称', () => {
    it('应该为深色主题返回正确名称', () => {
      const { themeName } = useTheme()
      
      mockColorMode.value = 'dark'
      expect(themeName.value).toBe('深色模式')
    })

    it('应该为浅色主题返回正确名称', () => {
      const { themeName } = useTheme()
      
      mockColorMode.value = 'light'
      expect(themeName.value).toBe('浅色模式')
    })

    it('应该为系统主题返回正确名称', () => {
      const { themeName } = useTheme()
      
      mockColorMode.value = 'system'
      expect(themeName.value).toBe('跟随系统')
    })
  })

  describe('响应式更新', () => {
    it('计算属性应该响应主题变化', () => {
      const { currentTheme, isDark, isLight, themeIcon, themeName } = useTheme()
      
      // 初始状态
      mockColorMode.value = 'light'
      expect(currentTheme.value).toBe('light')
      expect(isDark.value).toBe(false)
      expect(isLight.value).toBe(true)
      expect(themeIcon.value).toBe('SunIcon')
      expect(themeName.value).toBe('浅色模式')
      
      // 切换到深色
      mockColorMode.value = 'dark'
      expect(currentTheme.value).toBe('dark')
      expect(isDark.value).toBe(true)
      expect(isLight.value).toBe(false)
      expect(themeIcon.value).toBe('MoonIcon')
      expect(themeName.value).toBe('深色模式')
    })
  })
})
