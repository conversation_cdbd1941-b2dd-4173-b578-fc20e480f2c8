import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// Mock useApi
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
  upload: vi.fn()
}

global.useApi = vi.fn(() => mockApi)

import { useMusicApi } from '../../app/composables/useMusicApi'

describe('useMusicApi Composable', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('音乐列表获取', () => {
    it('应该正确获取音乐列表', async () => {
      const { getMusicList } = useMusicApi()
      const params = {
        page: 1,
        limit: 20,
        genre: 'pop',
        sortBy: 'latest' as const
      }
      const mockResponse = {
        success: true,
        data: {
          items: [{ id: 1, title: 'Test Song' }],
          total: 100,
          page: 1,
          limit: 20
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getMusicList(params)

      expect(mockApi.get).toHaveBeenCalledWith('/music', params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取音乐详情', async () => {
      const { getMusicById } = useMusicApi()
      const musicId = '123'
      const mockResponse = {
        success: true,
        data: { id: 123, title: 'Test Song', artist: 'Test Artist' }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getMusicById(musicId)

      expect(mockApi.get).toHaveBeenCalledWith('/music/123')
      expect(result).toEqual(mockResponse)
    })

    it('应该正确搜索音乐', async () => {
      const { searchMusic } = useMusicApi()
      const searchParams = {
        q: 'test song',
        page: 1,
        limit: 10,
        sortBy: 'relevance' as const
      }
      const mockResponse = {
        success: true,
        data: {
          items: [{ id: 1, title: 'Test Song' }],
          total: 5,
          page: 1,
          limit: 10
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await searchMusic(searchParams)

      expect(mockApi.get).toHaveBeenCalledWith('/music/search', searchParams)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('特殊音乐列表', () => {
    it('应该正确获取热门音乐', async () => {
      const { getTrendingMusic } = useMusicApi()
      const params = { period: 'week' as const, limit: 10 }
      const mockResponse = {
        success: true,
        data: [{ id: 1, title: 'Trending Song' }]
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getTrendingMusic(params)

      expect(mockApi.get).toHaveBeenCalledWith('/music/trending', params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取最新音乐', async () => {
      const { getLatestMusic } = useMusicApi()
      const params = { limit: 15, genre: 'rock' }
      const mockResponse = {
        success: true,
        data: [{ id: 1, title: 'Latest Song' }]
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getLatestMusic(params)

      expect(mockApi.get).toHaveBeenCalledWith('/music/recent', params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取流行音乐', async () => {
      const { getPopularMusic } = useMusicApi()
      const params = { limit: 20, genre: 'pop' }
      const mockResponse = {
        success: true,
        data: [{ id: 1, title: 'Popular Song' }]
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getPopularMusic(params)

      expect(mockApi.get).toHaveBeenCalledWith('/music/popular', params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取推荐音乐', async () => {
      const { getRecommendedMusic } = useMusicApi()
      const params = { limit: 10, type: 'similar' as const }
      const mockResponse = {
        success: true,
        data: [{ id: 1, title: 'Recommended Song' }]
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getRecommendedMusic(params)

      expect(mockApi.get).toHaveBeenCalledWith('/music/recommendations', params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取发现音乐', async () => {
      const { getDiscoverMusic } = useMusicApi()
      const params = { limit: 12, genre: 'jazz' }
      const mockResponse = {
        success: true,
        data: [{ id: 1, title: 'Discover Song' }]
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getDiscoverMusic(params)

      expect(mockApi.get).toHaveBeenCalledWith('/music/discover', params)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('音乐互动', () => {
    it('应该正确点赞音乐', async () => {
      const { likeMusic } = useMusicApi()
      const musicId = '123'
      const mockResponse = {
        success: true,
        data: { message: '点赞成功', liked: true }
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await likeMusic(musicId)

      expect(mockApi.post).toHaveBeenCalled()
      expect(result).toEqual(mockResponse)
    })

    it('应该正确取消点赞音乐', async () => {
      const { unlikeMusic } = useMusicApi()
      const musicId = '123'
      const mockResponse = {
        success: true,
        data: { message: '取消点赞成功', liked: false }
      }
      mockApi.delete.mockResolvedValue(mockResponse)

      const result = await unlikeMusic(musicId)

      expect(mockApi.delete).toHaveBeenCalled()
      expect(result).toEqual(mockResponse)
    })
  })

  describe('播放统计', () => {
    it('应该正确记录播放次数', async () => {
      const { recordPlay } = useMusicApi()
      const musicId = '123'
      const mockResponse = {
        success: true,
        data: { message: '播放记录成功', playCount: 101 }
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await recordPlay(musicId)

      expect(mockApi.post).toHaveBeenCalledWith(`/music/${musicId}/play`)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取播放统计', async () => {
      const { getMusicStats } = useMusicApi()
      const musicId = '123'
      const mockResponse = {
        success: true,
        data: {
          totalPlays: 1000,
          dailyPlays: 50,
          weeklyPlays: 300,
          monthlyPlays: 800
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getMusicStats(musicId)

      expect(mockApi.get).toHaveBeenCalledWith(`/music/${musicId}/stats`)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('音乐管理', () => {
    it('应该正确删除音乐', async () => {
      const { deleteMusic } = useMusicApi()
      const musicId = '123'
      const mockResponse = {
        success: true,
        data: { message: '音乐删除成功' }
      }
      mockApi.delete.mockResolvedValue(mockResponse)

      const result = await deleteMusic(musicId)

      expect(mockApi.delete).toHaveBeenCalled()
      expect(result).toEqual(mockResponse)
    })
  })

  describe('API方法可用性', () => {
    it('应该返回所有必要的API方法', () => {
      const musicApi = useMusicApi()

      expect(musicApi).toHaveProperty('getMusicList')
      expect(musicApi).toHaveProperty('getMusicById')
      expect(musicApi).toHaveProperty('searchMusic')
      expect(musicApi).toHaveProperty('getTrendingMusic')
      expect(musicApi).toHaveProperty('getLatestMusic')
      expect(musicApi).toHaveProperty('getPopularMusic')
      expect(musicApi).toHaveProperty('getRecommendedMusic')
      expect(musicApi).toHaveProperty('getDiscoverMusic')
    })

    it('所有方法都应该是函数', () => {
      const musicApi = useMusicApi()

      Object.values(musicApi).forEach(method => {
        expect(typeof method).toBe('function')
      })
    })
  })
})
