import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// Mock useApi
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn()
}

global.useApi = vi.fn(() => mockApi)

import { usePlaylistApi } from '../../app/composables/usePlaylistApi'

describe('usePlaylistApi Composable', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('歌单基础操作', () => {
    it('应该正确获取歌单列表', async () => {
      const { getPlaylists } = usePlaylistApi()
      const params = { page: 1, limit: 20, type: 'public' }
      const mockResponse = {
        success: true,
        data: {
          items: [{ id: 1, name: 'Test Playlist' }],
          total: 50,
          page: 1,
          limit: 20
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getPlaylists(params)

      expect(mockApi.get).toHaveBeenCalledWith('/playlists', params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取歌单详情', async () => {
      const { getPlaylistById } = usePlaylistApi()
      const playlistId = '123'
      const mockResponse = {
        success: true,
        data: { id: 123, name: 'Test Playlist', songs: [] }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getPlaylistById(playlistId)

      expect(mockApi.get).toHaveBeenCalledWith('/playlists/123')
      expect(result).toEqual(mockResponse)
    })

    it('应该正确创建歌单', async () => {
      const { createPlaylist } = usePlaylistApi()
      const playlistData = {
        name: 'New Playlist',
        description: 'Test description',
        isPublic: true
      }
      const mockResponse = {
        success: true,
        data: { id: 123, ...playlistData }
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await createPlaylist(playlistData)

      expect(mockApi.post).toHaveBeenCalledWith('/playlists', playlistData)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确更新歌单', async () => {
      const { updatePlaylist } = usePlaylistApi()
      const playlistId = '123'
      const updateData = { name: 'Updated Playlist' }
      const mockResponse = {
        success: true,
        data: { id: 123, name: 'Updated Playlist' }
      }
      mockApi.put.mockResolvedValue(mockResponse)

      const result = await updatePlaylist(playlistId, updateData)

      expect(mockApi.put).toHaveBeenCalledWith('/playlists/123', updateData)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确删除歌单', async () => {
      const { deletePlaylist } = usePlaylistApi()
      const playlistId = '123'
      const mockResponse = {
        success: true,
        data: { message: '歌单删除成功' }
      }
      mockApi.delete.mockResolvedValue(mockResponse)

      const result = await deletePlaylist(playlistId)

      expect(mockApi.delete).toHaveBeenCalledWith('/playlists/123')
      expect(result).toEqual(mockResponse)
    })
  })

  describe('歌单歌曲管理', () => {
    it('应该正确添加歌曲到歌单', async () => {
      const { addSongToPlaylist } = usePlaylistApi()
      const playlistId = '123'
      const songId = '456'
      const mockResponse = {
        success: true,
        data: { message: '歌曲添加成功' }
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await addSongToPlaylist(playlistId, songId)

      expect(mockApi.post).toHaveBeenCalledWith(`/playlists/${playlistId}/songs`, { songId })
      expect(result).toEqual(mockResponse)
    })

    it('应该正确从歌单移除歌曲', async () => {
      const { removeSongFromPlaylist } = usePlaylistApi()
      const playlistId = '123'
      const songId = '456'
      const mockResponse = {
        success: true,
        data: { message: '歌曲移除成功' }
      }
      mockApi.delete.mockResolvedValue(mockResponse)

      const result = await removeSongFromPlaylist(playlistId, songId)

      expect(mockApi.delete).toHaveBeenCalledWith(`/playlists/${playlistId}/songs/${songId}`)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确批量添加歌曲到歌单', async () => {
      const { addSongsToPlaylist } = usePlaylistApi()
      const playlistId = '123'
      const songIds = ['456', '789', '101']
      const mockResponse = {
        success: true,
        data: { message: '歌曲批量添加成功', added: 3 }
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await addSongsToPlaylist(playlistId, songIds)

      expect(mockApi.post).toHaveBeenCalledWith(`/playlists/${playlistId}/songs/batch`, { songIds })
      expect(result).toEqual(mockResponse)
    })

    it('应该正确重新排序歌单歌曲', async () => {
      const { reorderPlaylistSongs } = usePlaylistApi()
      const playlistId = '123'
      const songIds = ['789', '456', '101']
      const mockResponse = {
        success: true,
        data: { message: '歌曲排序成功' }
      }
      mockApi.put.mockResolvedValue(mockResponse)

      const result = await reorderPlaylistSongs(playlistId, songIds)

      expect(mockApi.put).toHaveBeenCalledWith(`/playlists/${playlistId}/reorder`, { songIds })
      expect(result).toEqual(mockResponse)
    })
  })

  describe('歌单互动', () => {
    it('应该正确关注歌单', async () => {
      const { followPlaylist } = usePlaylistApi()
      const playlistId = '123'
      const mockResponse = {
        success: true,
        data: { message: '关注成功', followed: true }
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await followPlaylist(playlistId)

      expect(mockApi.post).toHaveBeenCalledWith(`/playlists/${playlistId}/follow`)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确取消关注歌单', async () => {
      const { unfollowPlaylist } = usePlaylistApi()
      const playlistId = '123'
      const mockResponse = {
        success: true,
        data: { message: '取消关注成功', followed: false }
      }
      mockApi.delete.mockResolvedValue(mockResponse)

      const result = await unfollowPlaylist(playlistId)

      expect(mockApi.delete).toHaveBeenCalledWith(`/playlists/${playlistId}/follow`)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确点赞歌单', async () => {
      const { likePlaylist } = usePlaylistApi()
      const playlistId = '123'
      const mockResponse = {
        success: true,
        data: { message: '点赞成功', liked: true }
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await likePlaylist(playlistId)

      expect(mockApi.post).toHaveBeenCalledWith(`/playlists/${playlistId}/like`)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('歌单搜索和发现', () => {
    it('应该正确搜索歌单', async () => {
      const { searchPlaylists } = usePlaylistApi()
      const searchParams = {
        q: 'test playlist',
        page: 1,
        limit: 10
      }
      const mockResponse = {
        success: true,
        data: {
          items: [{ id: 1, name: 'Test Playlist' }],
          total: 5,
          page: 1,
          limit: 10
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await searchPlaylists(searchParams)

      expect(mockApi.get).toHaveBeenCalledWith('/playlists/search', searchParams)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取推荐歌单', async () => {
      const { getRecommendedPlaylists } = usePlaylistApi()
      const params = { limit: 10, type: 'similar' }
      const mockResponse = {
        success: true,
        data: [{ id: 1, name: 'Recommended Playlist' }]
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getRecommendedPlaylists(params)

      expect(mockApi.get).toHaveBeenCalledWith('/playlists/recommendations', params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取热门歌单', async () => {
      const { getTrendingPlaylists } = usePlaylistApi()
      const params = { period: 'week', limit: 15 }
      const mockResponse = {
        success: true,
        data: [{ id: 1, name: 'Trending Playlist' }]
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getTrendingPlaylists(params)

      expect(mockApi.get).toHaveBeenCalledWith('/playlists/trending', params)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('API方法可用性', () => {
    it('应该返回所有必要的API方法', () => {
      const playlistApi = usePlaylistApi()

      expect(playlistApi).toHaveProperty('getPlaylists')
      expect(playlistApi).toHaveProperty('getPlaylistById')
      expect(playlistApi).toHaveProperty('createPlaylist')
      expect(playlistApi).toHaveProperty('updatePlaylist')
      expect(playlistApi).toHaveProperty('deletePlaylist')
      expect(playlistApi).toHaveProperty('addSongToPlaylist')
      expect(playlistApi).toHaveProperty('removeSongFromPlaylist')
    })

    it('所有方法都应该是函数', () => {
      const playlistApi = usePlaylistApi()

      Object.values(playlistApi).forEach(method => {
        expect(typeof method).toBe('function')
      })
    })
  })
})
