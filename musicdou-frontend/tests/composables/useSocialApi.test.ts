import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// Mock useApi
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn()
}

global.useApi = vi.fn(() => mockApi)

import { useSocialApi } from '../../app/composables/useSocialApi'

describe('useSocialApi Composable', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('用户关注', () => {
    it('应该正确关注用户', async () => {
      const { followUser } = useSocialApi()
      const userId = '123'
      const mockResponse = {
        success: true,
        data: { message: '关注成功', followed: true }
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await followUser(userId)

      expect(mockApi.post).toHaveBeenCalledWith(`/social/users/${userId}/follow`)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确取消关注用户', async () => {
      const { unfollowUser } = useSocialApi()
      const userId = '123'
      const mockResponse = {
        success: true,
        data: { message: '取消关注成功', followed: false }
      }
      mockApi.delete.mockResolvedValue(mockResponse)

      const result = await unfollowUser(userId)

      expect(mockApi.delete).toHaveBeenCalledWith(`/social/users/${userId}/follow`)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取关注列表', async () => {
      const { getUserFollowing } = useSocialApi()
      const userId = '123'
      const params = { page: 1, limit: 20 }
      const mockResponse = {
        success: true,
        data: {
          items: [{ id: 1, username: 'user1' }],
          total: 50,
          page: 1,
          limit: 20
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getUserFollowing(userId, params)

      expect(mockApi.get).toHaveBeenCalledWith(`/social/users/${userId}/following`, params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取粉丝列表', async () => {
      const { getUserFollowers } = useSocialApi()
      const userId = '123'
      const params = { page: 1, limit: 20 }
      const mockResponse = {
        success: true,
        data: {
          items: [{ id: 1, username: 'follower1' }],
          total: 100,
          page: 1,
          limit: 20
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getUserFollowers(userId, params)

      expect(mockApi.get).toHaveBeenCalledWith(`/social/users/${userId}/followers`, params)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('评论功能', () => {
    it('应该正确添加评论', async () => {
      const { addComment } = useSocialApi()
      const targetType = 'music'
      const targetId = '123'
      const content = 'Great song!'
      const mockResponse = {
        success: true,
        data: { id: 1, content, targetType, targetId }
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await addComment(targetType, targetId, content)

      expect(mockApi.post).toHaveBeenCalledWith('/social/comments', {
        targetType,
        targetId,
        content
      })
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取评论列表', async () => {
      const { getComments } = useSocialApi()
      const targetType = 'music'
      const targetId = '123'
      const params = { page: 1, limit: 10 }
      const mockResponse = {
        success: true,
        data: {
          items: [{ id: 1, content: 'Great song!' }],
          total: 25,
          page: 1,
          limit: 10
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getComments(targetType, targetId, params)

      expect(mockApi.get).toHaveBeenCalledWith(`/social/comments/${targetType}/${targetId}`, params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确删除评论', async () => {
      const { deleteComment } = useSocialApi()
      const commentId = '123'
      const mockResponse = {
        success: true,
        data: { message: '评论删除成功' }
      }
      mockApi.delete.mockResolvedValue(mockResponse)

      const result = await deleteComment(commentId)

      expect(mockApi.delete).toHaveBeenCalledWith(`/social/comments/${commentId}`)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确点赞评论', async () => {
      const { likeComment } = useSocialApi()
      const commentId = '123'
      const mockResponse = {
        success: true,
        data: { message: '点赞成功', liked: true }
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await likeComment(commentId)

      expect(mockApi.post).toHaveBeenCalledWith(`/social/comments/${commentId}/like`)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('动态功能', () => {
    it('应该正确获取动态列表', async () => {
      const { getFeed } = useSocialApi()
      const params = { page: 1, limit: 20, type: 'following' }
      const mockResponse = {
        success: true,
        data: {
          items: [{ id: 1, type: 'music_share', content: 'Shared a song' }],
          total: 100,
          page: 1,
          limit: 20
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getFeed(params)

      expect(mockApi.get).toHaveBeenCalledWith('/social/feed', params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确发布动态', async () => {
      const { postActivity } = useSocialApi()
      const activityData = {
        type: 'music_share',
        content: 'Check out this amazing song!',
        targetId: '123'
      }
      const mockResponse = {
        success: true,
        data: { id: 1, ...activityData }
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await postActivity(activityData)

      expect(mockApi.post).toHaveBeenCalledWith('/social/activities', activityData)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取用户动态', async () => {
      const { getUserActivities } = useSocialApi()
      const userId = '123'
      const params = { page: 1, limit: 15 }
      const mockResponse = {
        success: true,
        data: {
          items: [{ id: 1, type: 'playlist_create' }],
          total: 30,
          page: 1,
          limit: 15
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getUserActivities(userId, params)

      expect(mockApi.get).toHaveBeenCalledWith(`/social/users/${userId}/activities`, params)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('消息功能', () => {
    it('应该正确发送私信', async () => {
      const { sendMessage } = useSocialApi()
      const receiverId = '123'
      const content = 'Hello there!'
      const mockResponse = {
        success: true,
        data: { id: 1, receiverId, content, sent: true }
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await sendMessage(receiverId, content)

      expect(mockApi.post).toHaveBeenCalledWith('/social/messages', {
        receiverId,
        content
      })
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取消息列表', async () => {
      const { getMessages } = useSocialApi()
      const conversationId = '123'
      const params = { page: 1, limit: 50 }
      const mockResponse = {
        success: true,
        data: {
          items: [{ id: 1, content: 'Hello!' }],
          total: 100,
          page: 1,
          limit: 50
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getMessages(conversationId, params)

      expect(mockApi.get).toHaveBeenCalledWith(`/social/conversations/${conversationId}/messages`, params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确获取会话列表', async () => {
      const { getConversations } = useSocialApi()
      const params = { page: 1, limit: 20 }
      const mockResponse = {
        success: true,
        data: {
          items: [{ id: 1, lastMessage: 'Hello!' }],
          total: 10,
          page: 1,
          limit: 20
        }
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await getConversations(params)

      expect(mockApi.get).toHaveBeenCalledWith('/social/conversations', params)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('API方法可用性', () => {
    it('应该返回所有必要的API方法', () => {
      const socialApi = useSocialApi()

      expect(socialApi).toHaveProperty('followUser')
      expect(socialApi).toHaveProperty('unfollowUser')
      expect(socialApi).toHaveProperty('getUserFollowing')
      expect(socialApi).toHaveProperty('getUserFollowers')
      expect(socialApi).toHaveProperty('addComment')
      expect(socialApi).toHaveProperty('getComments')
      expect(socialApi).toHaveProperty('deleteComment')
      expect(socialApi).toHaveProperty('likeComment')
      expect(socialApi).toHaveProperty('getFeed')
      expect(socialApi).toHaveProperty('postActivity')
      expect(socialApi).toHaveProperty('getUserActivities')
      expect(socialApi).toHaveProperty('sendMessage')
      expect(socialApi).toHaveProperty('getMessages')
      expect(socialApi).toHaveProperty('getConversations')
    })

    it('所有方法都应该是函数', () => {
      const socialApi = useSocialApi()

      Object.values(socialApi).forEach(method => {
        expect(typeof method).toBe('function')
      })
    })
  })
})
