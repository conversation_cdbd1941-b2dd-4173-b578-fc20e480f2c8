import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { ref, computed, readonly } from 'vue'

// Mock Vue functions
global.ref = ref
global.computed = computed
global.readonly = readonly

import { useNotification } from '../../app/composables/useNotification'

describe('useNotification Composable', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.restoreAllMocks()
    vi.useRealTimers()
  })

  describe('基础通知管理', () => {
    it('应该正确添加通知', () => {
      const { addNotification, notifications } = useNotification()
      
      const id = addNotification({
        type: 'info',
        title: '测试通知',
        message: '这是一个测试消息'
      })
      
      expect(notifications.value).toHaveLength(1)
      expect(notifications.value[0]).toMatchObject({
        id,
        type: 'info',
        title: '测试通知',
        message: '这是一个测试消息',
        duration: 5000
      })
    })

    it('应该正确移除通知', () => {
      const { addNotification, removeNotification, notifications } = useNotification()
      
      const id = addNotification({
        type: 'info',
        title: '测试通知'
      })
      
      expect(notifications.value).toHaveLength(1)
      
      removeNotification(id)
      expect(notifications.value).toHaveLength(0)
    })

    it('应该正确清除所有通知', () => {
      const { addNotification, clearAll, notifications } = useNotification()
      
      addNotification({ type: 'info', title: '通知1' })
      addNotification({ type: 'success', title: '通知2' })
      
      expect(notifications.value).toHaveLength(2)
      
      clearAll()
      expect(notifications.value).toHaveLength(0)
    })

    it('应该自动移除非持久化通知', () => {
      const { addNotification, notifications } = useNotification()
      
      addNotification({
        type: 'info',
        title: '自动移除通知',
        duration: 1000
      })
      
      expect(notifications.value).toHaveLength(1)
      
      // 快进时间
      vi.advanceTimersByTime(1000)
      
      expect(notifications.value).toHaveLength(0)
    })

    it('不应该自动移除持久化通知', () => {
      const { addNotification, notifications } = useNotification()
      
      addNotification({
        type: 'info',
        title: '持久化通知',
        persistent: true,
        duration: 1000
      })
      
      expect(notifications.value).toHaveLength(1)
      
      // 快进时间
      vi.advanceTimersByTime(1000)
      
      expect(notifications.value).toHaveLength(1)
    })
  })

  describe('快捷通知方法', () => {
    it('应该正确创建成功通知', () => {
      const { success, notifications } = useNotification()
      
      const id = success('操作成功', '数据已保存')
      
      expect(notifications.value[0]).toMatchObject({
        id,
        type: 'success',
        title: '操作成功',
        message: '数据已保存'
      })
    })

    it('应该正确创建错误通知', () => {
      const { error, notifications } = useNotification()
      
      const id = error('操作失败', '请重试')
      
      expect(notifications.value[0]).toMatchObject({
        id,
        type: 'error',
        title: '操作失败',
        message: '请重试',
        duration: 8000
      })
    })

    it('应该正确创建警告通知', () => {
      const { warning, notifications } = useNotification()
      
      const id = warning('注意', '此操作不可撤销')
      
      expect(notifications.value[0]).toMatchObject({
        id,
        type: 'warning',
        title: '注意',
        message: '此操作不可撤销',
        duration: 6000
      })
    })

    it('应该正确创建信息通知', () => {
      const { info, notifications } = useNotification()
      
      const id = info('提示', '新版本可用')
      
      expect(notifications.value[0]).toMatchObject({
        id,
        type: 'info',
        title: '提示',
        message: '新版本可用'
      })
    })

    it('应该正确创建加载通知', () => {
      const { loading, notifications } = useNotification()
      
      const id = loading('加载中', '请稍候...')
      
      expect(notifications.value[0]).toMatchObject({
        id,
        type: 'info',
        title: '加载中',
        message: '请稍候...',
        persistent: true
      })
    })
  })

  describe('确认对话框', () => {
    it('应该正确创建确认对话框', () => {
      const { confirm, notifications } = useNotification()
      const onConfirm = vi.fn()
      const onCancel = vi.fn()
      
      const id = confirm('确认删除', '此操作不可撤销', onConfirm, onCancel)
      
      const notification = notifications.value[0]
      expect(notification).toMatchObject({
        id,
        type: 'warning',
        title: '确认删除',
        message: '此操作不可撤销',
        persistent: true
      })
      
      expect(notification.actions).toHaveLength(2)
      expect(notification.actions![0].label).toBe('确认')
      expect(notification.actions![1].label).toBe('取消')
    })

    it('确认对话框的按钮应该正确执行回调', () => {
      const { confirm, notifications } = useNotification()
      const onConfirm = vi.fn()
      const onCancel = vi.fn()
      
      confirm('确认删除', '此操作不可撤销', onConfirm, onCancel)
      
      const notification = notifications.value[0]
      
      // 点击确认按钮
      notification.actions![0].action()
      expect(onConfirm).toHaveBeenCalled()
      
      // 点击取消按钮
      notification.actions![1].action()
      expect(onCancel).toHaveBeenCalled()
    })
  })

  describe('通知更新和查询', () => {
    it('应该正确更新通知', () => {
      const { addNotification, updateNotification, notifications } = useNotification()
      
      const id = addNotification({
        type: 'info',
        title: '原标题'
      })
      
      updateNotification(id, {
        title: '新标题',
        message: '新消息'
      })
      
      expect(notifications.value[0]).toMatchObject({
        title: '新标题',
        message: '新消息'
      })
    })

    it('应该正确计算通知数量', () => {
      const { addNotification, count } = useNotification()
      
      expect(count.value).toBe(0)
      
      addNotification({ type: 'info', title: '通知1' })
      expect(count.value).toBe(1)
      
      addNotification({ type: 'success', title: '通知2' })
      expect(count.value).toBe(2)
    })

    it('应该正确检查特定类型的通知', () => {
      const { addNotification, hasType } = useNotification()
      
      expect(hasType('error')).toBe(false)
      
      addNotification({ type: 'error', title: '错误通知' })
      expect(hasType('error')).toBe(true)
      expect(hasType('success')).toBe(false)
    })

    it('应该正确获取特定类型的通知', () => {
      const { addNotification, getByType } = useNotification()
      
      addNotification({ type: 'error', title: '错误1' })
      addNotification({ type: 'success', title: '成功1' })
      addNotification({ type: 'error', title: '错误2' })
      
      const errorNotifications = getByType('error')
      expect(errorNotifications).toHaveLength(2)
      expect(errorNotifications[0].title).toBe('错误1')
      expect(errorNotifications[1].title).toBe('错误2')
      
      const successNotifications = getByType('success')
      expect(successNotifications).toHaveLength(1)
      expect(successNotifications[0].title).toBe('成功1')
    })
  })

  describe('ID生成', () => {
    it('应该生成唯一的ID', () => {
      const { addNotification } = useNotification()
      
      const id1 = addNotification({ type: 'info', title: '通知1' })
      const id2 = addNotification({ type: 'info', title: '通知2' })
      
      expect(id1).not.toBe(id2)
      expect(typeof id1).toBe('string')
      expect(typeof id2).toBe('string')
    })
  })
})
