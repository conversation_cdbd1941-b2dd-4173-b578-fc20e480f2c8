
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for musicdou-frontend/pages/discover.vue</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">musicdou-frontend/pages</a> discover.vue</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/299</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/299</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">&lt;template&gt;
<span class="cstat-no" title="statement not covered" >  &lt;div class="min-h-screen bg-gray-50 dark:bg-gray-900"&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;!-- 页面头部 --&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div class="flex items-center justify-between"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100"&gt;</span>
              发现音乐
<span class="cstat-no" title="statement not covered" >            &lt;/h1&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;p class="text-gray-600 dark:text-gray-400 mt-2"&gt;</span>
              探索个性化推荐和热门内容
<span class="cstat-no" title="statement not covered" >            &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
          
<span class="cstat-no" title="statement not covered" >          &lt;!-- 快速搜索 --&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div class="flex items-center space-x-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;NuxtLink</span>
<span class="cstat-no" title="statement not covered" >              to="/search"</span>
<span class="cstat-no" title="statement not covered" >              class="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 </span>
                     text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600
                     transition-colors duration-200"
            &gt;
<span class="cstat-no" title="statement not covered" >              &lt;Icon name="MagnifyingGlassIcon" class="w-4 h-4" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span&gt;搜索&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/NuxtLink&gt;</span>
            
<span class="cstat-no" title="statement not covered" >            &lt;!-- 设置按钮 --&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;button</span>
<span class="cstat-no" title="statement not covered" >              @click="showSettings = true"</span>
<span class="cstat-no" title="statement not covered" >              class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 </span>
                     rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
<span class="cstat-no" title="statement not covered" >              title="推荐设置"</span>
            &gt;
<span class="cstat-no" title="statement not covered" >              &lt;Icon name="Cog6ToothIcon" class="w-5 h-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    &lt;div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;!-- 加载状态 --&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div v-if="isInitialLoading" class="flex justify-center py-12"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;Loading size="xl" /&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      &lt;!-- 主要内容 --&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div v-else class="space-y-8"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;!-- 个性化推荐 --&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;RecommendationSection</span>
<span class="cstat-no" title="statement not covered" >          v-if="recommendationStore.forYou.length &gt; 0"</span>
<span class="cstat-no" title="statement not covered" >          title="为你推荐"</span>
<span class="cstat-no" title="statement not covered" >          description="基于你的喜好和听歌历史"</span>
<span class="cstat-no" title="statement not covered" >          icon="HeartIcon"</span>
<span class="cstat-no" title="statement not covered" >          :items="recommendationStore.forYou"</span>
<span class="cstat-no" title="statement not covered" >          type="music"</span>
<span class="cstat-no" title="statement not covered" >          layout="horizontal"</span>
<span class="cstat-no" title="statement not covered" >          :is-loading="recommendationStore.isLoading"</span>
<span class="cstat-no" title="statement not covered" >          @refresh="refreshForYou"</span>
<span class="cstat-no" title="statement not covered" >          @view-all="viewAllForYou"</span>
<span class="cstat-no" title="statement not covered" >          @play="handlePlayMusic"</span>
<span class="cstat-no" title="statement not covered" >          @like="handleLikeMusic"</span>
<span class="cstat-no" title="statement not covered" >          @add-to-playlist="handleAddToPlaylist"</span>
<span class="cstat-no" title="statement not covered" >        /&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        &lt;!-- 新发布 --&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;RecommendationSection</span>
<span class="cstat-no" title="statement not covered" >          v-if="recommendationStore.newReleases.length &gt; 0"</span>
<span class="cstat-no" title="statement not covered" >          title="新发布"</span>
<span class="cstat-no" title="statement not covered" >          description="最新上线的音乐作品"</span>
<span class="cstat-no" title="statement not covered" >          icon="SparklesIcon"</span>
<span class="cstat-no" title="statement not covered" >          :items="recommendationStore.newReleases"</span>
<span class="cstat-no" title="statement not covered" >          type="music"</span>
<span class="cstat-no" title="statement not covered" >          layout="horizontal"</span>
<span class="cstat-no" title="statement not covered" >          :is-loading="recommendationStore.isLoading"</span>
<span class="cstat-no" title="statement not covered" >          @refresh="refreshNewReleases"</span>
<span class="cstat-no" title="statement not covered" >          @view-all="viewAllNewReleases"</span>
<span class="cstat-no" title="statement not covered" >          @play="handlePlayMusic"</span>
<span class="cstat-no" title="statement not covered" >          @like="handleLikeMusic"</span>
<span class="cstat-no" title="statement not covered" >          @add-to-playlist="handleAddToPlaylist"</span>
<span class="cstat-no" title="statement not covered" >        /&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        &lt;!-- 热门音乐 --&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;RecommendationSection</span>
<span class="cstat-no" title="statement not covered" >          v-if="recommendationStore.trending.length &gt; 0"</span>
<span class="cstat-no" title="statement not covered" >          title="热门音乐"</span>
<span class="cstat-no" title="statement not covered" >          description="当前最受欢迎的音乐"</span>
<span class="cstat-no" title="statement not covered" >          icon="FireIcon"</span>
<span class="cstat-no" title="statement not covered" >          :items="recommendationStore.trending"</span>
<span class="cstat-no" title="statement not covered" >          type="music"</span>
<span class="cstat-no" title="statement not covered" >          layout="horizontal"</span>
<span class="cstat-no" title="statement not covered" >          :is-loading="recommendationStore.isLoading"</span>
<span class="cstat-no" title="statement not covered" >          @refresh="refreshTrending"</span>
<span class="cstat-no" title="statement not covered" >          @view-all="viewAllTrending"</span>
<span class="cstat-no" title="statement not covered" >          @play="handlePlayMusic"</span>
<span class="cstat-no" title="statement not covered" >          @like="handleLikeMusic"</span>
<span class="cstat-no" title="statement not covered" >          @add-to-playlist="handleAddToPlaylist"</span>
<span class="cstat-no" title="statement not covered" >        /&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        &lt;!-- 推荐歌单 --&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;RecommendationSection</span>
<span class="cstat-no" title="statement not covered" >          v-if="recommendationStore.recommendedPlaylists.length &gt; 0"</span>
<span class="cstat-no" title="statement not covered" >          title="推荐歌单"</span>
<span class="cstat-no" title="statement not covered" >          description="精选歌单推荐"</span>
<span class="cstat-no" title="statement not covered" >          icon="QueueListIcon"</span>
<span class="cstat-no" title="statement not covered" >          :items="recommendationStore.recommendedPlaylists"</span>
<span class="cstat-no" title="statement not covered" >          type="playlist"</span>
<span class="cstat-no" title="statement not covered" >          layout="grid"</span>
<span class="cstat-no" title="statement not covered" >          :grid-cols="4"</span>
<span class="cstat-no" title="statement not covered" >          :is-loading="recommendationStore.isLoading"</span>
<span class="cstat-no" title="statement not covered" >          @refresh="refreshRecommendedPlaylists"</span>
<span class="cstat-no" title="statement not covered" >          @view-all="viewAllRecommendedPlaylists"</span>
<span class="cstat-no" title="statement not covered" >          @play="handlePlayPlaylist"</span>
<span class="cstat-no" title="statement not covered" >          @like="handleLikePlaylist"</span>
<span class="cstat-no" title="statement not covered" >        /&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        &lt;!-- 推荐分类 --&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div v-if="recommendationStore.categories.length &gt; 0" class="space-y-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div class="flex items-center justify-between"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100"&gt;</span>
              音乐分类
<span class="cstat-no" title="statement not covered" >            &lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;NuxtLink</span>
<span class="cstat-no" title="statement not covered" >              to="/discover/categories"</span>
<span class="cstat-no" title="statement not covered" >              class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 </span>
                     font-medium transition-colors duration-200"
<span class="cstat-no" title="statement not covered" >            &gt;</span>
              查看全部分类
<span class="cstat-no" title="statement not covered" >            &lt;/NuxtLink&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
          
<span class="cstat-no" title="statement not covered" >          &lt;div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;NuxtLink</span>
<span class="cstat-no" title="statement not covered" >              v-for="category in recommendationStore.categories.slice(0, 12)"</span>
<span class="cstat-no" title="statement not covered" >              :key="category.id"</span>
<span class="cstat-no" title="statement not covered" >              :to="`/discover/categories/${category.id}`"</span>
<span class="cstat-no" title="statement not covered" >              class="group relative aspect-square rounded-lg overflow-hidden bg-gradient-to-br from-primary-400 to-primary-600 </span>
                     hover:scale-105 transition-transform duration-200"
            &gt;
<span class="cstat-no" title="statement not covered" >              &lt;img</span>
<span class="cstat-no" title="statement not covered" >                v-if="category.coverUrl"</span>
<span class="cstat-no" title="statement not covered" >                :src="category.coverUrl"</span>
<span class="cstat-no" title="statement not covered" >                :alt="category.name"</span>
<span class="cstat-no" title="statement not covered" >                class="absolute inset-0 w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"</span>
<span class="cstat-no" title="statement not covered" >              /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div class="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-30 transition-all duration-200"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div class="absolute inset-0 flex items-center justify-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;h3 class="text-white font-semibold text-center px-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  {{ category.name }}</span>
<span class="cstat-no" title="statement not covered" >                &lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/NuxtLink&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        &lt;!-- 相似艺术家 --&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;RecommendationSection</span>
<span class="cstat-no" title="statement not covered" >          v-if="recommendationStore.similarArtists.length &gt; 0"</span>
<span class="cstat-no" title="statement not covered" >          title="你可能喜欢的艺术家"</span>
<span class="cstat-no" title="statement not covered" >          description="基于你的音乐品味推荐"</span>
<span class="cstat-no" title="statement not covered" >          icon="UserGroupIcon"</span>
<span class="cstat-no" title="statement not covered" >          :items="recommendationStore.similarArtists"</span>
<span class="cstat-no" title="statement not covered" >          type="user"</span>
<span class="cstat-no" title="statement not covered" >          layout="horizontal"</span>
<span class="cstat-no" title="statement not covered" >          :is-loading="recommendationStore.isLoading"</span>
<span class="cstat-no" title="statement not covered" >          @refresh="refreshSimilarArtists"</span>
<span class="cstat-no" title="statement not covered" >          @view-all="viewAllSimilarArtists"</span>
<span class="cstat-no" title="statement not covered" >          @follow="handleFollowUser"</span>
<span class="cstat-no" title="statement not covered" >        /&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        &lt;!-- 心情推荐 --&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div class="flex items-center justify-between mb-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div class="flex items-center space-x-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;Icon name="FaceSmileIcon" class="w-6 h-6 text-primary-600 dark:text-primary-400" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100"&gt;</span>
                  心情音乐
<span class="cstat-no" title="statement not covered" >                &lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p class="text-sm text-gray-600 dark:text-gray-400 mt-1"&gt;</span>
                  根据心情选择音乐
<span class="cstat-no" title="statement not covered" >                &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
          
<span class="cstat-no" title="statement not covered" >          &lt;div class="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-6 gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;button</span>
<span class="cstat-no" title="statement not covered" >              v-for="mood in moods"</span>
<span class="cstat-no" title="statement not covered" >              :key="mood.id"</span>
<span class="cstat-no" title="statement not covered" >              @click="handleMoodSelect(mood.id)"</span>
<span class="cstat-no" title="statement not covered" >              class="flex flex-col items-center p-4 rounded-lg border border-gray-200 dark:border-gray-700</span>
                     hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-primary-300 dark:hover:border-primary-600
                     transition-all duration-200 group"
            &gt;
<span class="cstat-no" title="statement not covered" >              &lt;div :class="['w-12 h-12 rounded-full flex items-center justify-center mb-2', mood.bgColor]"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;Icon :name="mood.icon" :class="['w-6 h-6', mood.textColor]" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span class="text-sm font-medium text-gray-900 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400"&gt;</span>
<span class="cstat-no" title="statement not covered" >                {{ mood.name }}</span>
<span class="cstat-no" title="statement not covered" >              &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    &lt;!-- 推荐设置模态框 --&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;RecommendationSettings</span>
<span class="cstat-no" title="statement not covered" >      v-if="showSettings"</span>
<span class="cstat-no" title="statement not covered" >      @close="showSettings = false"</span>
<span class="cstat-no" title="statement not covered" >      @settings-updated="handleSettingsUpdated"</span>
<span class="cstat-no" title="statement not covered" >    /&gt;</span>
<span class="cstat-no" title="statement not covered" >  &lt;/div&gt;</span>
&lt;/template&gt;
&nbsp;
&lt;script setup lang="ts"&gt;
import type { Music, Playlist, User } from '~/types'
&nbsp;
// 页面元数据
<span class="cstat-no" title="statement not covered" >definePageMeta({</span>
<span class="cstat-no" title="statement not covered" >  title: '发现',</span>
<span class="cstat-no" title="statement not covered" >  description: '发现新音乐和个性化推荐'</span>
<span class="cstat-no" title="statement not covered" >})</span>
&nbsp;
// 状态管理
<span class="cstat-no" title="statement not covered" >const recommendationStore = useRecommendationStore()</span>
<span class="cstat-no" title="statement not covered" >const { recordUserAction } = useRecommendationApi()</span>
&nbsp;
// 响应式数据
<span class="cstat-no" title="statement not covered" >const isInitialLoading = ref(true)</span>
<span class="cstat-no" title="statement not covered" >const showSettings = ref(false)</span>
&nbsp;
// 心情选项
<span class="cstat-no" title="statement not covered" >const moods = [</span>
<span class="cstat-no" title="statement not covered" >  { id: 'happy', name: '开心', icon: 'FaceSmileIcon', bgColor: 'bg-yellow-100 dark:bg-yellow-900', textColor: 'text-yellow-600 dark:text-yellow-400' },</span>
<span class="cstat-no" title="statement not covered" >  { id: 'sad', name: '伤感', icon: 'FaceFrownIcon', bgColor: 'bg-blue-100 dark:bg-blue-900', textColor: 'text-blue-600 dark:text-blue-400' },</span>
<span class="cstat-no" title="statement not covered" >  { id: 'energetic', name: '活力', icon: 'BoltIcon', bgColor: 'bg-orange-100 dark:bg-orange-900', textColor: 'text-orange-600 dark:text-orange-400' },</span>
<span class="cstat-no" title="statement not covered" >  { id: 'calm', name: '平静', icon: 'CloudIcon', bgColor: 'bg-green-100 dark:bg-green-900', textColor: 'text-green-600 dark:text-green-400' },</span>
<span class="cstat-no" title="statement not covered" >  { id: 'romantic', name: '浪漫', icon: 'HeartIcon', bgColor: 'bg-pink-100 dark:bg-pink-900', textColor: 'text-pink-600 dark:text-pink-400' },</span>
<span class="cstat-no" title="statement not covered" >  { id: 'focus', name: '专注', icon: 'EyeIcon', bgColor: 'bg-purple-100 dark:bg-purple-900', textColor: 'text-purple-600 dark:text-purple-400' }</span>
<span class="cstat-no" title="statement not covered" >]</span>
&nbsp;
// 初始化
<span class="cstat-no" title="statement not covered" >onMounted(async () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  try {</span>
    // 初始化推荐设置
<span class="cstat-no" title="statement not covered" >    recommendationStore.initializeSettings()</span>
    
    // 加载推荐数据
<span class="cstat-no" title="statement not covered" >    await recommendationStore.initializeRecommendations()</span>
    
    // 加载推荐歌单
<span class="cstat-no" title="statement not covered" >    await recommendationStore.loadRecommendedPlaylists()</span>
    
    // 加载相似艺术家
<span class="cstat-no" title="statement not covered" >    await recommendationStore.loadSimilarArtists()</span>
    
    // 开始自动刷新
<span class="cstat-no" title="statement not covered" >    recommendationStore.startAutoRefresh()</span>
<span class="cstat-no" title="statement not covered" >  } catch (error) {</span>
<span class="cstat-no" title="statement not covered" >    console.error('初始化发现页面失败:', error)</span>
<span class="cstat-no" title="statement not covered" >  } finally {</span>
<span class="cstat-no" title="statement not covered" >    isInitialLoading.value = false</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >})</span>
&nbsp;
// 组件卸载时停止自动刷新
<span class="cstat-no" title="statement not covered" >onUnmounted(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  recommendationStore.stopAutoRefresh()</span>
<span class="cstat-no" title="statement not covered" >})</span>
&nbsp;
// 刷新方法
<span class="cstat-no" title="statement not covered" >const refreshForYou = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  recommendationStore.loadForYouRecommendations()</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const refreshNewReleases = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  recommendationStore.loadNewReleases()</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const refreshTrending = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  recommendationStore.loadTrendingMusic()</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const refreshRecommendedPlaylists = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  recommendationStore.loadRecommendedPlaylists()</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const refreshSimilarArtists = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  recommendationStore.loadSimilarArtists()</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
// 查看全部方法
<span class="cstat-no" title="statement not covered" >const viewAllForYou = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  navigateTo('/discover/for-you')</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const viewAllNewReleases = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  navigateTo('/discover/new-releases')</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const viewAllTrending = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  navigateTo('/discover/trending')</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const viewAllRecommendedPlaylists = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  navigateTo('/discover/playlists')</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const viewAllSimilarArtists = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  navigateTo('/discover/artists')</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
// 事件处理
<span class="cstat-no" title="statement not covered" >const handlePlayMusic = async (music: Music) =&gt; {</span>
  // 记录播放行为
<span class="cstat-no" title="statement not covered" >  await recordUserAction({</span>
<span class="cstat-no" title="statement not covered" >    type: 'play',</span>
<span class="cstat-no" title="statement not covered" >    musicId: music.id,</span>
<span class="cstat-no" title="statement not covered" >    context: 'discover'</span>
<span class="cstat-no" title="statement not covered" >  })</span>
  
  // 这里集成音乐播放器
<span class="cstat-no" title="statement not covered" >  console.log('播放音乐:', music)</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const handleLikeMusic = async (music: Music) =&gt; {</span>
  // 记录点赞行为
<span class="cstat-no" title="statement not covered" >  await recordUserAction({</span>
<span class="cstat-no" title="statement not covered" >    type: 'like',</span>
<span class="cstat-no" title="statement not covered" >    musicId: music.id,</span>
<span class="cstat-no" title="statement not covered" >    context: 'discover'</span>
<span class="cstat-no" title="statement not covered" >  })</span>
  
  // 这里处理点赞逻辑
<span class="cstat-no" title="statement not covered" >  console.log('点赞音乐:', music)</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const handleAddToPlaylist = (music: Music) =&gt; {</span>
  // 这里打开添加到歌单的模态框
<span class="cstat-no" title="statement not covered" >  console.log('添加到歌单:', music)</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const handlePlayPlaylist = async (playlist: Playlist) =&gt; {</span>
  // 记录播放行为
<span class="cstat-no" title="statement not covered" >  await recordUserAction({</span>
<span class="cstat-no" title="statement not covered" >    type: 'play',</span>
<span class="cstat-no" title="statement not covered" >    playlistId: playlist.id,</span>
<span class="cstat-no" title="statement not covered" >    context: 'discover'</span>
<span class="cstat-no" title="statement not covered" >  })</span>
  
  // 这里处理歌单播放
<span class="cstat-no" title="statement not covered" >  console.log('播放歌单:', playlist)</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const handleLikePlaylist = async (playlist: Playlist) =&gt; {</span>
  // 记录点赞行为
<span class="cstat-no" title="statement not covered" >  await recordUserAction({</span>
<span class="cstat-no" title="statement not covered" >    type: 'like',</span>
<span class="cstat-no" title="statement not covered" >    playlistId: playlist.id,</span>
<span class="cstat-no" title="statement not covered" >    context: 'discover'</span>
<span class="cstat-no" title="statement not covered" >  })</span>
  
  // 这里处理歌单点赞
<span class="cstat-no" title="statement not covered" >  console.log('点赞歌单:', playlist)</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const handleFollowUser = (user: User) =&gt; {</span>
  // 这里处理关注用户
<span class="cstat-no" title="statement not covered" >  console.log('关注用户:', user)</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const handleMoodSelect = (moodId: string) =&gt; {</span>
  // 跳转到心情推荐页面
<span class="cstat-no" title="statement not covered" >  navigateTo(`/discover/mood/${moodId}`)</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const handleSettingsUpdated = () =&gt; {</span>
  // 设置更新后刷新推荐
<span class="cstat-no" title="statement not covered" >  recommendationStore.refreshRecommendations(true)</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
// SEO优化
<span class="cstat-no" title="statement not covered" >useHead({</span>
<span class="cstat-no" title="statement not covered" >  title: '发现音乐 - MusicDou',</span>
<span class="cstat-no" title="statement not covered" >  meta: [</span>
<span class="cstat-no" title="statement not covered" >    {</span>
<span class="cstat-no" title="statement not covered" >      name: 'description',</span>
<span class="cstat-no" title="statement not covered" >      content: '在MusicDou发现新音乐，获取个性化推荐，探索热门内容和精选歌单'</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  ]</span>
<span class="cstat-no" title="statement not covered" >})</span>
&lt;/script&gt;
&nbsp;
&lt;style scoped&gt;
/* 分类卡片渐变背景 */
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
&nbsp;
/* 心情按钮悬停效果 */
.group:hover .group-hover\:text-primary-600 {
  color: rgb(37 99 235);
}
&nbsp;
.dark .group:hover .group-hover\:text-primary-400 {
  color: rgb(96 165 250);
}
&nbsp;
/* 平滑过渡 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
&nbsp;
/* 响应式调整 */
@media (max-width: 640px) {
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
&nbsp;
@media (max-width: 768px) {
  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  .sm\:grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
&lt;/style&gt;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-02T04:37:10.655Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    