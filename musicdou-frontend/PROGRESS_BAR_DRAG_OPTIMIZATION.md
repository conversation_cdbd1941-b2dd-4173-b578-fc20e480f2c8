# 进度条拖拽优化

## 优化目标
用户在拖拽进度条时，音乐继续正常播放，只有在松开鼠标左键时才真正跳转到新位置，提供更流畅的用户体验。

## 实现方案

### 1. 添加预览时间状态
在 `playerStore` 中添加预览时间状态，用于拖拽时显示预览位置：

```typescript
// 拖拽预览时间（用于拖拽时显示预览位置，不影响实际播放）
const previewTime = ref<number | null>(null)

const setPreviewTime = (time: number | null) => {
  previewTime.value = time
}
```

### 2. 修改进度更新逻辑
`updateProgress` 函数支持两种模式：

```typescript
const updateProgress = (clientX: number, shouldSeek: boolean = true) => {
  // 计算新的播放时间
  const newTime = (percentage / 100) * playerStore.state.duration
  
  if (shouldSeek) {
    // 真正跳转到指定时间
    audioPlayer.seekTo(newTime)
  } else {
    // 仅更新UI显示，不跳转音频
    playerStore.setPreviewTime(newTime)
  }
}
```

### 3. 智能进度条显示
进度条根据拖拽状态显示不同的时间：

```typescript
const playbackProgress = computed(() => {
  // 如果正在拖拽，使用预览时间；否则使用实际播放时间
  const displayTime = isProgressDragging.value && playerStore.previewTime !== null 
    ? playerStore.previewTime 
    : currentTime
    
  return Math.min(100, (displayTime / duration) * 100)
})
```

### 4. 智能时间显示
时间显示也会根据拖拽状态切换：

```typescript
const currentTimeFormatted = computed(() => {
  // 如果正在拖拽，显示预览时间；否则显示实际播放时间
  const displayTime = isProgressDragging.value && playerStore.previewTime !== null 
    ? playerStore.previewTime 
    : playerStore.state.currentTime
  return formatTime(displayTime)
})
```

### 5. 优化事件处理
- **点击事件**：立即跳转 `updateProgress(e.clientX, true)`
- **拖拽开始**：只更新预览 `updateProgress(e.clientX, false)`
- **拖拽过程**：只更新预览 `updateProgress(e.clientX, false)`
- **拖拽结束**：真正跳转 `updateProgress(e.clientX, true)`

```typescript
const handleProgressMouseDown = (e: MouseEvent) => {
  isProgressDragging.value = true
  updateProgress(e.clientX, false) // 开始拖拽时不跳转，只更新预览

  const handleMouseMove = (e: MouseEvent) => {
    if (isProgressDragging.value) {
      updateProgress(e.clientX, false) // 拖拽过程中不跳转，只更新预览
    }
  }

  const handleMouseUp = (e: MouseEvent) => {
    if (isProgressDragging.value) {
      updateProgress(e.clientX, true) // 松开时才真正跳转
    }
    isProgressDragging.value = false
    playerStore.setPreviewTime(null) // 清除预览时间
  }
}
```

## 用户体验提升

### 优化前：
- ❌ 拖拽时音频会不断跳转，造成播放中断
- ❌ 拖拽过程中音频播放不流畅
- ❌ 用户难以精确定位到想要的位置

### 优化后：
- ✅ 拖拽时音频继续正常播放
- ✅ 进度条和时间显示实时预览拖拽位置
- ✅ 只有松开鼠标时才真正跳转
- ✅ 提供流畅的拖拽体验

## 技术亮点

### 1. 状态分离
- **实际播放状态**：`currentTime` - 音频实际播放位置
- **预览状态**：`previewTime` - 拖拽时的预览位置
- **UI显示**：根据拖拽状态智能切换显示内容

### 2. 事件优化
- **延迟跳转**：拖拽过程中不执行音频跳转操作
- **预览反馈**：实时更新UI显示，给用户即时反馈
- **状态清理**：拖拽结束后清理预览状态

### 3. 性能优化
- **减少音频操作**：避免拖拽过程中频繁的 `seek` 操作
- **流畅播放**：音频播放不受拖拽影响
- **响应式更新**：UI更新使用 Vue 的响应式系统

## 实现细节

### 状态管理
```typescript
// playerStore.ts
const previewTime = ref<number | null>(null)
const setPreviewTime = (time: number | null) => {
  previewTime.value = time
}
```

### UI响应
```typescript
// 进度条宽度计算
const displayTime = isProgressDragging.value && playerStore.previewTime !== null 
  ? playerStore.previewTime 
  : currentTime

// 时间显示计算  
const currentTimeFormatted = computed(() => {
  const displayTime = isProgressDragging.value && playerStore.previewTime !== null 
    ? playerStore.previewTime 
    : playerStore.state.currentTime
  return formatTime(displayTime)
})
```

### 事件流程
1. **mousedown** → 设置拖拽状态，更新预览时间
2. **mousemove** → 持续更新预览时间，不跳转音频
3. **mouseup** → 执行真正的音频跳转，清理预览状态

## 兼容性
- ✅ 保持原有的点击跳转功能
- ✅ 保持原有的键盘控制功能
- ✅ 保持原有的API接口不变
- ✅ 向后兼容所有现有功能

## 测试建议
1. **拖拽测试**：拖拽进度条时音频应继续播放
2. **预览测试**：拖拽时时间显示应实时更新
3. **跳转测试**：松开鼠标时应跳转到正确位置
4. **点击测试**：点击进度条应立即跳转
5. **边界测试**：测试拖拽到开头和结尾的情况
