{"name": "musicdou-frontend", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint . --ext .vue,.js,.ts", "lint:fix": "eslint . --ext .vue,.js,.ts --fix", "format": "prettier --write .", "type-check": "nuxt typecheck", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:all": "npm run test:run && npm run test:e2e"}, "dependencies": {"@nuxtjs/color-mode": "^3.5.2", "@nuxtjs/tailwindcss": "^6.14.0", "@pinia/nuxt": "^0.11.2", "@types/howler": "^2.2.12", "@vueuse/motion": "^3.0.3", "@vueuse/nuxt": "^13.6.0", "howler": "^2.2.4", "howler.js": "^2.1.2", "nuxt": "^4.0.2", "vue": "^3.5.18", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@playwright/test": "^1.54.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/vue": "^8.1.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-vue": "^6.0.1", "@vitest/coverage-v8": "^3.2.4", "@vue/test-utils": "^2.4.6", "c8": "^10.1.3", "eslint": "^9.32.0", "eslint-plugin-vue": "^10.4.0", "happy-dom": "^18.0.1", "jsdom": "^26.1.0", "msw": "^2.10.4", "playwright": "^1.54.1", "prettier": "^3.6.2", "vitest": "^3.2.4"}}