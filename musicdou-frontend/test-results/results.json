{"config": {"configFile": "/Users/<USER>/Desktop/musicdou-frontend/playwright.config.ts", "rootDir": "/Users/<USER>/Desktop/musicdou-frontend/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Desktop/musicdou-frontend/tests/e2e/global-setup.ts", "globalTeardown": "/Users/<USER>/Desktop/musicdou-frontend/tests/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Desktop/musicdou-frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Desktop/musicdou-frontend/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/musicdou-frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Desktop/musicdou-frontend/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/musicdou-frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Desktop/musicdou-frontend/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/musicdou-frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Desktop/musicdou-frontend/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/musicdou-frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Desktop/musicdou-frontend/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [], "errors": [], "stats": {"startTime": "2025-08-02T03:24:14.009Z", "duration": 190416.438, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}