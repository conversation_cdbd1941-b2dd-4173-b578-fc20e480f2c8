// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },
  debug: false,

  // 开发服务器配置
  devServer: {
    port: parseInt(process.env.NUXT_DEV_PORT || '3001'),
    host: '0.0.0.0'
  },

  // 模块配置
  modules: [
    '@nuxtjs/tailwindcss',
    '@nuxtjs/color-mode',
    '@pinia/nuxt',
    '@vueuse/nuxt'
  ],

  // 组件自动导入配置
  components: [
    {
      path: '~/components',
      pathPrefix: false,
    }
  ],

  // 颜色模式配置
  colorMode: {
    preference: 'system',
    fallback: 'light',
    hid: 'nuxt-color-mode-script',
    globalName: '__NUXT_COLOR_MODE__',
    componentName: 'ColorScheme',
    classPrefix: '',
    classSuffix: '',
    storageKey: 'nuxt-color-mode'
  },

  // CSS配置 - 让Tailwind使用默认配置
  // css: ['~/assets/css/tailwind.css'],

  // 运行时配置
  runtimeConfig: {
    public: {
      apiBase: process.env.API_BASE_URL || 'http://localhost:3000/api/v1',
      authCookieName: process.env.AUTH_COOKIE_NAME || 'auth-token',
      maxUploadSize: process.env.MAX_UPLOAD_SIZE || '50MB',
      allowedAudioFormats: process.env.ALLOWED_AUDIO_FORMATS || 'mp3,flac,wav,aac,m4a',
      allowedImageFormats: process.env.ALLOWED_IMAGE_FORMATS || 'jpg,jpeg,png,webp,gif',
      enableSocialFeatures: process.env.ENABLE_SOCIAL_FEATURES === 'true',
      enableRecommendations: process.env.ENABLE_RECOMMENDATIONS === 'true',
      enableUpload: process.env.ENABLE_UPLOAD === 'true'
    }
  },

  // TypeScript配置
  typescript: {
    strict: true,
    typeCheck: false
  },

  // Nitro配置
  nitro: {
    // 移除代理配置，直接使用完整URL
  },

  // SSR配置
  ssr: true,

  // 实验性功能配置
  experimental: {
    payloadExtraction: false
  },

  // 客户端配置
  app: {
    head: {
      viewport: 'width=device-width,initial-scale=1',
      charset: 'utf-8'
    }
  }
})
