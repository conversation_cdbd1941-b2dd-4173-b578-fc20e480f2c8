# QQ音乐风格三栏布局测试指南

## 🎯 测试目标
验证新实现的QQ音乐风格三栏布局的功能完整性和用户体验。

## 🖥️ 访问地址
- 开发服务器: http://localhost:3002
- 确保服务器正在运行: `npm run dev`

## 📋 功能测试清单

### 1. 基础布局测试
- [ ] **三栏布局显示**: 左侧导航栏 + 中间内容区 + 右侧播放队列
- [ ] **响应式设计**: 调整浏览器窗口大小，检查布局适配
- [ ] **移动端适配**: 使用开发者工具模拟移动设备

### 2. 左侧导航栏测试
- [ ] **主要导航**: 点击"发现音乐"和"音乐社区"
- [ ] **我的歌单**: 展开/收起我的歌单列表
- [ ] **收藏歌单**: 展开/收起收藏歌单列表
- [ ] **侧边栏折叠**: 点击折叠按钮测试
- [ ] **歌单创建**: 点击创建歌单按钮

### 3. 顶部用户头像区域测试
- [ ] **用户信息显示**: 检查用户头像和信息
- [ ] **搜索功能**: 测试全局搜索框
- [ ] **主题切换**: 切换深色/浅色模式
- [ ] **通知中心**: 点击通知图标
- [ ] **用户菜单**: 悬停/点击用户头像显示下拉菜单

### 4. 右侧播放队列测试
- [ ] **当前播放**: 显示正在播放的歌曲
- [ ] **播放队列**: 显示完整播放列表
- [ ] **拖拽排序**: 拖拽歌曲重新排序
- [ ] **队列管理**: 删除歌曲、清空队列
- [ ] **响应式隐藏**: 小屏幕下自动隐藏

### 5. 页面导航测试
- [ ] **首页**: 访问 `/` 检查主页布局
- [ ] **发现音乐**: 访问 `/discover` 检查发现页面
- [ ] **歌单列表**: 访问 `/playlists` 检查歌单列表
- [ ] **歌单详情**: 访问 `/playlists/[id]` 检查歌单详情
- [ ] **页面切换**: 测试页面间的导航切换

### 6. 交互体验测试
- [ ] **动画效果**: 检查过渡动画是否流畅
- [ ] **加载状态**: 检查加载指示器
- [ ] **错误处理**: 测试错误状态显示
- [ ] **通知系统**: 检查操作反馈通知

## 🐛 常见问题排查

### 布局问题
- 检查CSS类名是否正确应用
- 验证Tailwind CSS是否正常加载
- 检查响应式断点是否生效

### 组件问题
- 确认所有组件正确导入
- 检查TypeScript类型错误
- 验证Props传递是否正确

### 数据问题
- 检查API调用是否成功
- 验证状态管理是否正常
- 确认用户认证状态

## 📊 性能检查
- [ ] **首屏加载时间**: 使用开发者工具检查
- [ ] **组件渲染性能**: 检查是否有不必要的重渲染
- [ ] **内存使用**: 长时间使用后检查内存泄漏

## 🎨 视觉检查
- [ ] **设计一致性**: 与QQ音乐界面对比
- [ ] **颜色主题**: 深色/浅色模式切换
- [ ] **字体和间距**: 检查视觉层次
- [ ] **图标显示**: 确认所有图标正常显示

## 📱 移动端专项测试
- [ ] **触摸交互**: 测试触摸滑动和点击
- [ ] **侧边栏**: 移动端侧边栏滑出/隐藏
- [ ] **搜索模态框**: 移动端搜索体验
- [ ] **播放队列**: 移动端播放控制

## ✅ 测试完成标准
- 所有功能测试项目通过
- 无控制台错误或警告
- 响应式设计在各种屏幕尺寸下正常
- 用户交互流畅自然
- 性能表现良好

## 🚀 下一步建议
1. **用户测试**: 邀请真实用户体验并收集反馈
2. **性能优化**: 根据测试结果进行性能调优
3. **功能扩展**: 基于用户反馈添加新功能
4. **代码优化**: 重构和优化代码结构

---

**测试时间**: ___________
**测试人员**: ___________
**测试结果**: ___________
