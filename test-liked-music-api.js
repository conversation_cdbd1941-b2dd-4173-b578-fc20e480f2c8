#!/usr/bin/env node

/**
 * 测试喜欢的音乐API接口
 * 用于验证 /playlists/liked 页面的API集成
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';

// 测试用户凭据
const TEST_USER = {
  email: '<EMAIL>',
  password: '12345678'
};

let authToken = '';

/**
 * 登录获取认证token
 */
async function login() {
  try {
    console.log('🔐 正在登录...');
    const response = await axios.post(`${BASE_URL}/auth/login`, TEST_USER);
    
    if (response.data.success) {
      authToken = response.data.data.token;
      console.log('✅ 登录成功');
      return true;
    } else {
      console.error('❌ 登录失败:', response.data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ 登录请求失败:', error.message);
    return false;
  }
}

/**
 * 获取用户信息
 */
async function getUserInfo() {
  try {
    console.log('\n👤 获取用户信息...');
    const response = await axios.get(`${BASE_URL}/auth/profile`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (response.data.success) {
      const user = response.data.data;
      console.log('✅ 用户信息:', {
        id: user._id,
        username: user.username,
        email: user.email
      });
      return user;
    } else {
      console.error('❌ 获取用户信息失败:', response.data.message);
      return null;
    }
  } catch (error) {
    console.error('❌ 获取用户信息请求失败:', error.message);
    return null;
  }
}

/**
 * 获取音乐列表（用于测试点赞）
 */
async function getMusicList() {
  try {
    console.log('\n🎵 获取音乐列表...');
    const response = await axios.get(`${BASE_URL}/music?limit=5`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (response.data.success) {
      const music = response.data.data.music || [];
      console.log(`✅ 获取到 ${music.length} 首音乐`);
      return music;
    } else {
      console.error('❌ 获取音乐列表失败:', response.data.message);
      return [];
    }
  } catch (error) {
    console.error('❌ 获取音乐列表请求失败:', error.message);
    return [];
  }
}

/**
 * 点赞音乐
 */
async function likeMusic(musicId) {
  try {
    console.log(`\n❤️ 点赞音乐 ${musicId}...`);
    const response = await axios.post(`${BASE_URL}/likes`, {
      targetType: 'music',
      targetId: musicId
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (response.data.success) {
      console.log('✅ 点赞成功');
      return true;
    } else {
      console.log('ℹ️ 点赞结果:', response.data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ 点赞请求失败:', error.message);
    return false;
  }
}

/**
 * 获取用户点赞的音乐列表
 */
async function getUserLikedMusic(userId) {
  try {
    console.log('\n💖 获取用户喜欢的音乐...');
    const response = await axios.get(`${BASE_URL}/likes/user/${userId}?targetType=music&limit=20`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (response.data.success) {
      const likes = response.data.data.likes || [];
      console.log(`✅ 获取到 ${likes.length} 首喜欢的音乐`);
      
      // 显示详细信息
      likes.forEach((like, index) => {
        const music = like.target;
        if (music) {
          console.log(`  ${index + 1}. ${music.title} - ${music.artist} (${music.album || '未知专辑'})`);
        }
      });
      
      return {
        likes,
        pagination: response.data.data.pagination
      };
    } else {
      console.error('❌ 获取喜欢的音乐失败:', response.data.message);
      return { likes: [], pagination: null };
    }
  } catch (error) {
    console.error('❌ 获取喜欢的音乐请求失败:', error.message);
    return { likes: [], pagination: null };
  }
}

/**
 * 取消点赞音乐
 */
async function unlikeMusic(musicId) {
  try {
    console.log(`\n💔 取消点赞音乐 ${musicId}...`);
    const response = await axios.delete(`${BASE_URL}/likes`, {
      data: {
        targetType: 'music',
        targetId: musicId
      },
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (response.data.success) {
      console.log('✅ 取消点赞成功');
      return true;
    } else {
      console.log('ℹ️ 取消点赞结果:', response.data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ 取消点赞请求失败:', error.message);
    return false;
  }
}

/**
 * 主测试函数
 */
async function main() {
  console.log('🚀 开始测试喜欢的音乐API接口\n');
  
  // 1. 登录
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('\n❌ 测试终止：登录失败');
    return;
  }
  
  // 2. 获取用户信息
  const user = await getUserInfo();
  if (!user) {
    console.log('\n❌ 测试终止：无法获取用户信息');
    return;
  }
  
  // 3. 获取音乐列表
  const musicList = await getMusicList();
  if (musicList.length === 0) {
    console.log('\n⚠️ 警告：没有可用的音乐进行测试');
  }
  
  // 4. 获取当前喜欢的音乐
  const initialLiked = await getUserLikedMusic(user._id);
  console.log(`\n📊 初始状态：用户有 ${initialLiked.likes.length} 首喜欢的音乐`);
  
  // 5. 如果有音乐，测试点赞功能
  if (musicList.length > 0) {
    const testMusic = musicList[0];
    console.log(`\n🎯 使用音乐进行测试: ${testMusic.title} - ${testMusic.artist}`);
    
    // 点赞
    await likeMusic(testMusic._id);
    
    // 再次获取喜欢的音乐
    const afterLike = await getUserLikedMusic(user._id);
    console.log(`\n📊 点赞后：用户有 ${afterLike.likes.length} 首喜欢的音乐`);
    
    // 取消点赞
    await unlikeMusic(testMusic._id);
    
    // 最后获取喜欢的音乐
    const afterUnlike = await getUserLikedMusic(user._id);
    console.log(`\n📊 取消点赞后：用户有 ${afterUnlike.likes.length} 首喜欢的音乐`);
  }
  
  console.log('\n🎉 测试完成！');
  console.log('\n📝 测试总结:');
  console.log('  ✅ 用户认证');
  console.log('  ✅ 获取用户信息');
  console.log('  ✅ 获取音乐列表');
  console.log('  ✅ 获取用户喜欢的音乐');
  if (musicList.length > 0) {
    console.log('  ✅ 点赞音乐');
    console.log('  ✅ 取消点赞音乐');
  }
  console.log('\n🔗 前端页面可以访问: http://localhost:3001/playlists/liked');
}

// 运行测试
main().catch(error => {
  console.error('\n💥 测试过程中发生错误:', error.message);
  process.exit(1);
});
