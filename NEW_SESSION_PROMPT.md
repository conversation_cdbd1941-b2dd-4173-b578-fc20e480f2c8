# 🎵 MusicDou 前端开发 - 新会话提示词

## 📋 项目背景

我正在开发一个名为 MusicDou 的音乐流媒体平台前端项目，基于 Nuxt.js 4.0.2 + Vue 3 + TypeScript，采用网易云音乐风格的设计。

**技术栈：**
- Nuxt.js 4.0.2 (Vue 3.4+, TypeScript 5.0+)
- Tailwind CSS 3.4+ (响应式设计)
- Pinia 2.1+ (状态管理)
- Howler.js 2.2+ (音频播放)
- JWT 认证系统

**项目结构：**
- 前端：http://localhost:3001
- 后端：http://localhost:3000/api/v1
- 工作目录：`/Users/<USER>/Desktop/nusicdou`

测试账号**********
密码12345678
前端目录musicdou-frontend
后端目录musicdou-backend

## 🎯 当前项目状态

### ✅ 已完成功能
1. **网易云音乐风格布局** - 三栏式设计（左侧导航、中间内容、右侧播放队列）
2. **底部播放器UI** - 播放进度条拖拽、音量控制拖拽、播放控制按钮
3. **主题切换功能** - 深色/浅色模式切换（已修复图标显示问题）
4. **用户认证基础** - 登录/注册页面、JWT认证中间件、退出登录API对接
5. **响应式设计** - 支持移动端适配

### ✅ 最新完成功能
6. **用户状态获取** - 在主布局中实现用户信息获取，页面加载时自动调用认证初始化
7. **认证中间件优化** - 改进用户信息获取逻辑，避免重复API调用，提升性能和用户体验
8. **个人资料页面** - 创建完整的个人资料页面，包含用户信息、积分显示、签到功能、头像上传、密码修改等功能
9. **用户设置页面** - 创建完整的设置页面，包含通知设置、隐私设置、播放设置、外观设置、账户安全等，支持实时保存和同步
10. **个人资料编辑功能** - 完善个人资料页面的所有编辑功能，包括用户名、邮箱、个人简介修改
11. **头像上传功能** - 实现头像上传和更换功能，支持图片文件上传和实时预览
12. **每日签到系统** - 完整的签到功能，包括签到状态检查、积分奖励和连续签到天数统计
13. **密码修改功能** - 在设置页面实现安全的密码更新功能，包含表单验证和错误处理
14. **账户安全设置** - 实现两步验证开关、登录设备管理等账户安全功能

### 🎉 阶段性成果
**用户认证与个人资料管理系统已完成**
- ✅ 用户认证系统完整实现
- ✅ 个人资料页面功能完善
- ✅ 用户设置页面功能完整
- ✅ 所有核心用户管理功能已实现

### 🔄 下一阶段建议
**可以考虑的后续开发方向：**
- 音乐播放功能完善
- 歌单管理系统
- 社交功能实现
- 音乐推荐算法
- 移动端适配优化

## 📋 接下来的优先任务

### 🔥 优先级1：完善用户认证系统
1. **完成用户状态获取** [进行中]
2. **优化认证中间件** - 改进用户信息获取逻辑，避免重复API调用

### 🔥 优先级2：实现个人信息页面
1. **创建个人资料页面** - `/profile` 路由，展示用户信息、积分、签到状态
2. **实现资料编辑功能** - 用户名、邮箱、个人简介等字段修改
3. **实现头像上传功能** - 对接头像上传API
4. **实现每日签到功能** - 签到状态和积分奖励

### 🔥 优先级3：实现设置页面
1. **创建设置页面结构** - `/settings` 路由和导航
2. **实现密码修改功能** - 安全的密码更新
3. **实现通知设置** - 邮件通知、系统通知开关
4. **实现隐私设置** - 个人资料可见性等
5. **实现账户安全设置** - 两步验证、登录设备管理

## 🔧 关键文件和API

### 主要文件
- `app/layouts/main.vue` - 主布局文件（用户头像显示）
- `app/stores/auth.ts` - 认证状态管理
- `app/composables/useAuthApi.ts` - 认证API接口
- `app/middleware/auth.ts` - 认证中间件

### 关键API接口
- `GET /auth/profile` - 获取用户信息
- `PUT /auth/profile` - 更新用户资料
- `POST /auth/avatar` - 上传头像
- `POST /auth/signin` - 每日签到
- `PUT /auth/password` - 修改密码
- `POST /auth/logout` - 用户登出

## 📖 详细任务文档

项目根目录下有两个详细的任务文档：
- `NEXT_TASKS.md` - 完整的任务文档和技术实现指南
- `PRIORITY_TASKS.md` - 优先任务清单和快速开始指南

## 🎯 请帮我继续开发

**当前需要你帮我：**

1. **继续完成当前进行中的任务**：实现用户状态获取功能
   - 修改 `app/layouts/main.vue` 中的用户头像显示逻辑
   - 在 onMounted 中调用用户信息获取
   - 确保用户头像和用户名正确显示

2. **按优先级推进后续任务**：
   - 创建个人资料页面
   - 实现设置页面
   - 对接更多API功能

3. **保持代码质量**：
   - 遵循现有的代码风格和架构
   - 使用 TypeScript 类型安全
   - 保持网易云音乐的设计风格
   - 确保响应式设计

**开发要求：**
- 优先使用 Pinia store 进行状态管理
- 统一的错误处理和用户反馈
- 遵循现有的组件设计规范
- 确保移动端兼容性

请查看项目文件，了解当前状态，然后帮我继续完成开发任务。如果需要了解更多细节，可以查看 `NEXT_TASKS.md` 和 `PRIORITY_TASKS.md` 文件。
