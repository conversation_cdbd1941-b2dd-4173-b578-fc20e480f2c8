const Notification = require('../models/Notification');
const User = require('../models/User');
const Music = require('../models/Music');
const Playlist = require('../models/Playlist');
const Comment = require('../models/Comment');
const Activity = require('../models/Activity');
const Follow = require('../models/Follow');

/**
 * 通知服务
 * 负责生成、发送和管理各种社交通知
 */
class NotificationService {

  /**
   * 创建关注通知
   */
  static async createFollowNotification(followerId, followingId, metadata = {}) {
    try {
      const follower = await User.findById(followerId, 'username avatar profile.displayName').lean();
      const following = await User.findById(followingId, 'username avatar profile.displayName privacySettings.notificationSettings').lean();

      if (!follower || !following) {
        throw new Error('User not found');
      }

      // 检查用户通知设置
      const notificationSettings = following.privacySettings?.notificationSettings?.follow;
      if (!notificationSettings?.inApp) {
        return null; // 用户关闭了关注通知
      }

      const notificationData = {
        recipient: followingId,
        sender: followerId,
        type: 'follow',
        title: '新的关注者',
        content: `${follower.profile?.displayName || follower.username} 关注了你`,
        target: {
          type: 'user',
          id: followerId,
          snapshot: {
            username: follower.username,
            displayName: follower.profile?.displayName,
            avatar: follower.avatar
          }
        },
        priority: 'normal',
        group: 'social',
        channels: {
          inApp: notificationSettings.inApp,
          email: notificationSettings.email,
          push: notificationSettings.push
        },
        actionUrl: `/users/${follower.username}`,
        metadata: {
          source: metadata.source || 'system',
          extra: metadata
        }
      };

      const notification = await Notification.create(notificationData);

      // 异步发送推送通知
      this.sendPushNotification(notification).catch(error => {
        console.error('Error sending follow notification push:', error);
      });

      return notification;
    } catch (error) {
      console.error('Error creating follow notification:', error);
      throw error;
    }
  }

  /**
   * 创建点赞通知
   */
  static async createLikeNotification(likerId, targetType, targetId, metadata = {}) {
    try {
      const liker = await User.findById(likerId, 'username avatar profile.displayName').lean();
      if (!liker) {
        throw new Error('Liker not found');
      }

      let target, recipient, notificationSettings;
      let title, content, actionUrl;

      // 根据点赞目标类型获取相关信息
      switch (targetType) {
        case 'music':
          target = await Music.findById(targetId).populate('uploader', 'username avatar profile.displayName privacySettings.notificationSettings').lean();
          if (!target) return null;
          
          recipient = target.uploader._id;
          notificationSettings = target.uploader.privacySettings?.notificationSettings?.like;
          title = '音乐获得点赞';
          content = `${liker.profile?.displayName || liker.username} 点赞了你的音乐《${target.title}》`;
          actionUrl = `/music/${targetId}`;
          break;

        case 'comment':
          target = await Comment.findById(targetId).populate('author', 'username avatar profile.displayName privacySettings.notificationSettings').lean();
          if (!target) return null;
          
          recipient = target.author._id;
          notificationSettings = target.author.privacySettings?.notificationSettings?.like;
          title = '评论获得点赞';
          content = `${liker.profile?.displayName || liker.username} 点赞了你的评论`;
          actionUrl = `/music/${target.musicId}#comment-${targetId}`;
          break;

        case 'playlist':
          target = await Playlist.findById(targetId).populate('creator', 'username avatar profile.displayName privacySettings.notificationSettings').lean();
          if (!target) return null;
          
          recipient = target.creator._id;
          notificationSettings = target.creator.privacySettings?.notificationSettings?.like;
          title = '歌单获得点赞';
          content = `${liker.profile?.displayName || liker.username} 点赞了你的歌单《${target.name}》`;
          actionUrl = `/playlists/${targetId}`;
          break;

        case 'activity':
          target = await Activity.findById(targetId).populate('user', 'username avatar profile.displayName privacySettings.notificationSettings').lean();
          if (!target) return null;
          
          recipient = target.user._id;
          notificationSettings = target.user.privacySettings?.notificationSettings?.like;
          title = '动态获得点赞';
          content = `${liker.profile?.displayName || liker.username} 点赞了你的动态`;
          actionUrl = `/activities/${targetId}`;
          break;

        default:
          throw new Error('Invalid target type for like notification');
      }

      // 不给自己发通知
      if (recipient.toString() === likerId.toString()) {
        return null;
      }

      // 检查用户通知设置
      if (!notificationSettings?.inApp) {
        return null;
      }

      const notificationData = {
        recipient,
        sender: likerId,
        type: `like_${targetType}`,
        title,
        content,
        target: {
          type: targetType,
          id: targetId,
          snapshot: this.createTargetSnapshot(targetType, target)
        },
        priority: 'normal',
        group: 'social',
        channels: {
          inApp: notificationSettings.inApp,
          email: notificationSettings.email,
          push: notificationSettings.push
        },
        actionUrl,
        metadata: {
          source: metadata.source || 'system',
          extra: metadata
        }
      };

      const notification = await Notification.create(notificationData);

      // 异步发送推送通知
      this.sendPushNotification(notification).catch(error => {
        console.error('Error sending like notification push:', error);
      });

      return notification;
    } catch (error) {
      console.error('Error creating like notification:', error);
      throw error;
    }
  }

  /**
   * 创建评论通知
   */
  static async createCommentNotification(commenterId, targetType, targetId, commentId, metadata = {}) {
    try {
      const commenter = await User.findById(commenterId, 'username avatar profile.displayName').lean();
      if (!commenter) {
        throw new Error('Commenter not found');
      }

      let target, recipient, notificationSettings;
      let title, content, actionUrl;

      switch (targetType) {
        case 'music':
          target = await Music.findById(targetId).populate('uploader', 'username avatar profile.displayName privacySettings.notificationSettings').lean();
          if (!target) return null;
          
          recipient = target.uploader._id;
          notificationSettings = target.uploader.privacySettings?.notificationSettings?.comment;
          title = '音乐收到新评论';
          content = `${commenter.profile?.displayName || commenter.username} 评论了你的音乐《${target.title}》`;
          actionUrl = `/music/${targetId}#comment-${commentId}`;
          break;

        case 'activity':
          target = await Activity.findById(targetId).populate('user', 'username avatar profile.displayName privacySettings.notificationSettings').lean();
          if (!target) return null;
          
          recipient = target.user._id;
          notificationSettings = target.user.privacySettings?.notificationSettings?.comment;
          title = '动态收到新评论';
          content = `${commenter.profile?.displayName || commenter.username} 评论了你的动态`;
          actionUrl = `/activities/${targetId}#comment-${commentId}`;
          break;

        default:
          throw new Error('Invalid target type for comment notification');
      }

      // 不给自己发通知
      if (recipient.toString() === commenterId.toString()) {
        return null;
      }

      // 检查用户通知设置
      if (!notificationSettings?.inApp) {
        return null;
      }

      const notificationData = {
        recipient,
        sender: commenterId,
        type: `comment_${targetType}`,
        title,
        content,
        target: {
          type: targetType,
          id: targetId,
          snapshot: this.createTargetSnapshot(targetType, target)
        },
        priority: 'normal',
        group: 'social',
        channels: {
          inApp: notificationSettings.inApp,
          email: notificationSettings.email,
          push: notificationSettings.push
        },
        actionUrl,
        metadata: {
          source: metadata.source || 'system',
          extra: { commentId, ...metadata }
        }
      };

      const notification = await Notification.create(notificationData);

      // 异步发送推送通知
      this.sendPushNotification(notification).catch(error => {
        console.error('Error sending comment notification push:', error);
      });

      return notification;
    } catch (error) {
      console.error('Error creating comment notification:', error);
      throw error;
    }
  }

  /**
   * 创建回复通知
   */
  static async createReplyNotification(replierId, originalCommentId, replyCommentId, metadata = {}) {
    try {
      const replier = await User.findById(replierId, 'username avatar profile.displayName').lean();
      const originalComment = await Comment.findById(originalCommentId)
        .populate('author', 'username avatar profile.displayName privacySettings.notificationSettings')
        .lean();

      if (!replier || !originalComment) {
        return null;
      }

      const recipient = originalComment.author._id;

      // 不给自己发通知
      if (recipient.toString() === replierId.toString()) {
        return null;
      }

      // 检查用户通知设置
      const notificationSettings = originalComment.author.privacySettings?.notificationSettings?.reply;
      if (!notificationSettings?.inApp) {
        return null;
      }

      const notificationData = {
        recipient,
        sender: replierId,
        type: 'reply_comment',
        title: '评论收到回复',
        content: `${replier.profile?.displayName || replier.username} 回复了你的评论`,
        target: {
          type: 'comment',
          id: originalCommentId,
          snapshot: {
            content: originalComment.content.substring(0, 100),
            musicId: originalComment.musicId
          }
        },
        priority: 'normal',
        group: 'social',
        channels: {
          inApp: notificationSettings.inApp,
          email: notificationSettings.email,
          push: notificationSettings.push
        },
        actionUrl: `/music/${originalComment.musicId}#comment-${replyCommentId}`,
        metadata: {
          source: metadata.source || 'system',
          extra: { originalCommentId, replyCommentId, ...metadata }
        }
      };

      const notification = await Notification.create(notificationData);

      // 异步发送推送通知
      this.sendPushNotification(notification).catch(error => {
        console.error('Error sending reply notification push:', error);
      });

      return notification;
    } catch (error) {
      console.error('Error creating reply notification:', error);
      throw error;
    }
  }

  /**
   * 创建目标对象快照
   */
  static createTargetSnapshot(targetType, target) {
    switch (targetType) {
      case 'music':
        return {
          title: target.title,
          artist: target.artist,
          album: target.album,
          coverImage: target.coverImage
        };
      case 'playlist':
        return {
          name: target.name,
          description: target.description,
          coverImage: target.coverImage,
          songCount: target.songs?.length || 0
        };
      case 'comment':
        return {
          content: target.content.substring(0, 100),
          musicId: target.musicId
        };
      case 'activity':
        return {
          title: target.title,
          type: target.type,
          description: target.description?.substring(0, 100)
        };
      default:
        return {};
    }
  }

  /**
   * 创建分享通知
   */
  static async createShareNotification(sharerId, targetType, targetId, metadata = {}) {
    try {
      const sharer = await User.findById(sharerId, 'username avatar profile.displayName').lean();
      if (!sharer) {
        throw new Error('Sharer not found');
      }

      let target, recipient, notificationSettings;
      let title, content, actionUrl;

      switch (targetType) {
        case 'music':
          target = await Music.findById(targetId).populate('uploader', 'username avatar profile.displayName privacySettings.notificationSettings').lean();
          if (!target) return null;

          recipient = target.uploader._id;
          notificationSettings = target.uploader.privacySettings?.notificationSettings?.share;
          title = '音乐被分享';
          content = `${sharer.profile?.displayName || sharer.username} 分享了你的音乐《${target.title}》`;
          actionUrl = `/music/${targetId}`;
          break;

        case 'playlist':
          target = await Playlist.findById(targetId).populate('creator', 'username avatar profile.displayName privacySettings.notificationSettings').lean();
          if (!target) return null;

          recipient = target.creator._id;
          notificationSettings = target.creator.privacySettings?.notificationSettings?.share;
          title = '歌单被分享';
          content = `${sharer.profile?.displayName || sharer.username} 分享了你的歌单《${target.name}》`;
          actionUrl = `/playlists/${targetId}`;
          break;

        default:
          throw new Error('Invalid target type for share notification');
      }

      // 不给自己发通知
      if (recipient.toString() === sharerId.toString()) {
        return null;
      }

      // 检查用户通知设置
      if (!notificationSettings?.inApp) {
        return null;
      }

      const notificationData = {
        recipient,
        sender: sharerId,
        type: `share_${targetType}`,
        title,
        content,
        target: {
          type: targetType,
          id: targetId,
          snapshot: this.createTargetSnapshot(targetType, target)
        },
        priority: 'normal',
        group: 'social',
        channels: {
          inApp: notificationSettings.inApp,
          email: notificationSettings.email,
          push: notificationSettings.push
        },
        actionUrl,
        metadata: {
          source: metadata.source || 'system',
          extra: metadata
        }
      };

      const notification = await Notification.create(notificationData);

      // 异步发送推送通知
      this.sendPushNotification(notification).catch(error => {
        console.error('Error sending share notification push:', error);
      });

      return notification;
    } catch (error) {
      console.error('Error creating share notification:', error);
      throw error;
    }
  }

  /**
   * 创建新动态通知（给关注者）
   */
  static async createNewActivityNotification(activityId, metadata = {}) {
    try {
      const activity = await Activity.findById(activityId)
        .populate('user', 'username avatar profile.displayName')
        .lean();

      if (!activity) {
        throw new Error('Activity not found');
      }

      // 只为公开和关注者可见的动态发送通知
      if (activity.privacy === 'private') {
        return [];
      }

      // 获取关注者列表
      const followers = await Follow.find({
        following: activity.user._id,
        status: 'active'
      }).populate('follower', 'username privacySettings.notificationSettings').lean();

      const notifications = [];

      for (const follow of followers) {
        const notificationSettings = follow.follower.privacySettings?.notificationSettings?.newActivity;

        // 检查用户是否开启了新动态通知
        if (!notificationSettings?.inApp) {
          continue;
        }

        const notificationData = {
          recipient: follow.follower._id,
          sender: activity.user._id,
          type: 'new_activity',
          title: '关注的人发布了新动态',
          content: `${activity.user.profile?.displayName || activity.user.username} 发布了新动态：${activity.title}`,
          target: {
            type: 'activity',
            id: activityId,
            snapshot: this.createTargetSnapshot('activity', activity)
          },
          priority: 'low',
          group: 'social',
          channels: {
            inApp: notificationSettings.inApp,
            email: notificationSettings.email,
            push: notificationSettings.push
          },
          actionUrl: `/activities/${activityId}`,
          metadata: {
            source: metadata.source || 'system',
            batchId: `activity_${activityId}`,
            extra: metadata
          }
        };

        try {
          const notification = await Notification.create(notificationData);
          notifications.push(notification);
        } catch (error) {
          console.error('Error creating activity notification for user:', follow.follower._id, error);
        }
      }

      // 批量发送推送通知
      this.sendBatchPushNotifications(notifications).catch(error => {
        console.error('Error sending batch activity notifications:', error);
      });

      return notifications;
    } catch (error) {
      console.error('Error creating new activity notifications:', error);
      throw error;
    }
  }

  /**
   * 创建新音乐通知（给关注者）
   */
  static async createNewMusicNotification(musicId, uploaderId, metadata = {}) {
    try {
      const music = await Music.findById(musicId).lean();
      const uploader = await User.findById(uploaderId, 'username avatar profile.displayName').lean();

      if (!music || !uploader) {
        return [];
      }

      // 获取关注者列表
      const followers = await Follow.find({
        following: uploaderId,
        status: 'active'
      }).populate('follower', 'username privacySettings.notificationSettings').lean();

      const notifications = [];

      for (const follow of followers) {
        const notificationSettings = follow.follower.privacySettings?.notificationSettings?.newMusic;

        if (!notificationSettings?.inApp) {
          continue;
        }

        const notificationData = {
          recipient: follow.follower._id,
          sender: uploaderId,
          type: 'new_music',
          title: '关注的人上传了新音乐',
          content: `${uploader.profile?.displayName || uploader.username} 上传了新音乐《${music.title}》`,
          target: {
            type: 'music',
            id: musicId,
            snapshot: this.createTargetSnapshot('music', music)
          },
          priority: 'normal',
          group: 'content',
          channels: {
            inApp: notificationSettings.inApp,
            email: notificationSettings.email,
            push: notificationSettings.push
          },
          actionUrl: `/music/${musicId}`,
          metadata: {
            source: metadata.source || 'system',
            batchId: `music_${musicId}`,
            extra: metadata
          }
        };

        try {
          const notification = await Notification.create(notificationData);
          notifications.push(notification);
        } catch (error) {
          console.error('Error creating music notification for user:', follow.follower._id, error);
        }
      }

      // 批量发送推送通知
      this.sendBatchPushNotifications(notifications).catch(error => {
        console.error('Error sending batch music notifications:', error);
      });

      return notifications;
    } catch (error) {
      console.error('Error creating new music notifications:', error);
      throw error;
    }
  }

  /**
   * 发送推送通知（占位符方法）
   */
  static async sendPushNotification(notification) {
    try {
      // 这里可以集成实际的推送服务
      // 目前只是记录日志
      console.log(`Sending push notification: ${notification.title} to user ${notification.recipient}`);

      // 更新推送状态
      if (notification.channels.push) {
        notification.delivery.push.status = 'sent';
        notification.delivery.push.sentAt = new Date();
        await notification.save();
      }

      return true;
    } catch (error) {
      console.error('Error sending push notification:', error);

      // 更新推送失败状态
      notification.delivery.push.status = 'failed';
      notification.delivery.push.error = error.message;
      await notification.save();

      return false;
    }
  }

  /**
   * 批量发送推送通知
   */
  static async sendBatchPushNotifications(notifications) {
    try {
      const results = await Promise.allSettled(
        notifications.map(notification => this.sendPushNotification(notification))
      );

      const successCount = results.filter(result => result.status === 'fulfilled' && result.value).length;
      const failureCount = results.length - successCount;

      console.log(`Batch push notifications: ${successCount} sent, ${failureCount} failed`);

      return { successCount, failureCount, total: notifications.length };
    } catch (error) {
      console.error('Error sending batch push notifications:', error);
      throw error;
    }
  }

  /**
   * 创建系统通知
   */
  static async createSystemNotification(recipients, title, content, options = {}) {
    try {
      const {
        priority = 'normal',
        actionUrl = null,
        icon = null,
        tags = [],
        expiresAt = null,
        channels = { inApp: true, email: false, push: true },
        metadata = {}
      } = options;

      // 如果recipients是'all'，获取所有活跃用户
      let recipientIds = recipients;
      if (recipients === 'all') {
        const users = await User.find({ isActive: true }, '_id').lean();
        recipientIds = users.map(user => user._id);
      } else if (!Array.isArray(recipients)) {
        recipientIds = [recipients];
      }

      const notifications = [];

      for (const recipientId of recipientIds) {
        const notificationData = {
          recipient: recipientId,
          sender: null, // 系统通知没有发送者
          type: 'system',
          title,
          content,
          target: {
            type: 'system',
            id: null,
            snapshot: {}
          },
          priority,
          group: 'system',
          channels,
          icon,
          actionUrl,
          tags,
          expiresAt,
          metadata: {
            source: 'system',
            ...metadata
          }
        };

        try {
          const notification = await Notification.create(notificationData);
          notifications.push(notification);
        } catch (error) {
          console.error('Error creating system notification for user:', recipientId, error);
        }
      }

      // 批量发送推送通知
      this.sendBatchPushNotifications(notifications).catch(error => {
        console.error('Error sending batch system notifications:', error);
      });

      return notifications;
    } catch (error) {
      console.error('Error creating system notifications:', error);
      throw error;
    }
  }

  /**
   * 创建成就通知
   */
  static async createAchievementNotification(userId, achievementData, metadata = {}) {
    try {
      const user = await User.findById(userId, 'privacySettings.notificationSettings').lean();
      if (!user) {
        throw new Error('User not found');
      }

      const notificationSettings = user.privacySettings?.notificationSettings?.achievement;
      if (!notificationSettings?.inApp) {
        return null;
      }

      const notificationData = {
        recipient: userId,
        sender: null, // 成就通知没有发送者
        type: 'achievement',
        title: '获得新成就',
        content: `恭喜你获得成就：${achievementData.name}`,
        target: {
          type: 'achievement',
          id: achievementData.id,
          snapshot: {
            name: achievementData.name,
            description: achievementData.description,
            icon: achievementData.icon,
            points: achievementData.points
          }
        },
        priority: 'high',
        group: 'achievement',
        channels: {
          inApp: notificationSettings.inApp,
          email: notificationSettings.email,
          push: notificationSettings.push
        },
        icon: achievementData.icon,
        actionUrl: `/achievements/${achievementData.id}`,
        tags: ['achievement', achievementData.category],
        metadata: {
          source: metadata.source || 'system',
          extra: { achievementData, ...metadata }
        }
      };

      const notification = await Notification.create(notificationData);

      // 异步发送推送通知
      this.sendPushNotification(notification).catch(error => {
        console.error('Error sending achievement notification push:', error);
      });

      return notification;
    } catch (error) {
      console.error('Error creating achievement notification:', error);
      throw error;
    }
  }

  /**
   * 创建里程碑通知
   */
  static async createMilestoneNotification(userId, milestoneData, metadata = {}) {
    try {
      const user = await User.findById(userId, 'privacySettings.notificationSettings').lean();
      if (!user) {
        throw new Error('User not found');
      }

      const notificationSettings = user.privacySettings?.notificationSettings?.achievement;
      if (!notificationSettings?.inApp) {
        return null;
      }

      const notificationData = {
        recipient: userId,
        sender: null,
        type: 'milestone',
        title: '达成新里程碑',
        content: `恭喜你达成里程碑：${milestoneData.name}`,
        target: {
          type: 'milestone',
          id: milestoneData.id,
          snapshot: {
            name: milestoneData.name,
            description: milestoneData.description,
            value: milestoneData.value,
            unit: milestoneData.unit
          }
        },
        priority: 'normal',
        group: 'achievement',
        channels: {
          inApp: notificationSettings.inApp,
          email: notificationSettings.email,
          push: notificationSettings.push
        },
        icon: '🎯',
        actionUrl: `/profile/achievements`,
        tags: ['milestone', milestoneData.category],
        metadata: {
          source: metadata.source || 'system',
          extra: { milestoneData, ...metadata }
        }
      };

      const notification = await Notification.create(notificationData);

      // 异步发送推送通知
      this.sendPushNotification(notification).catch(error => {
        console.error('Error sending milestone notification push:', error);
      });

      return notification;
    } catch (error) {
      console.error('Error creating milestone notification:', error);
      throw error;
    }
  }

  /**
   * 批量标记通知为已读
   */
  static async markNotificationsAsRead(userId, notificationIds) {
    try {
      const result = await Notification.markBatchAsRead(userId, notificationIds);
      return result;
    } catch (error) {
      console.error('Error marking notifications as read:', error);
      throw error;
    }
  }

  /**
   * 获取用户未读通知数量
   */
  static async getUnreadCount(userId, type = null) {
    try {
      return await Notification.getUnreadCount(userId, type);
    } catch (error) {
      console.error('Error getting unread count:', error);
      throw error;
    }
  }

  /**
   * 清理过期通知
   */
  static async cleanupExpiredNotifications() {
    try {
      const result = await Notification.cleanupExpired();
      console.log(`Cleaned up ${result.deletedCount} expired notifications`);
      return result;
    } catch (error) {
      console.error('Error cleaning up expired notifications:', error);
      throw error;
    }
  }

  /**
   * 检查通知频率限制
   */
  static async checkNotificationFrequency(userId, type) {
    try {
      const user = await User.findById(userId, 'privacySettings.notificationSettings').lean();
      if (!user) return false;

      const frequency = user.privacySettings?.notificationSettings?.frequency;
      if (!frequency) return true;

      // 检查每日通知数量限制
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const todayCount = await Notification.countDocuments({
        recipient: userId,
        createdAt: { $gte: today }
      });

      if (todayCount >= frequency.maxPerDay) {
        return false;
      }

      // 检查批量通知间隔
      const lastNotification = await Notification.findOne({
        recipient: userId,
        type
      }).sort({ createdAt: -1 }).lean();

      if (lastNotification) {
        const timeDiff = Date.now() - lastNotification.createdAt.getTime();
        const minInterval = frequency.batchInterval * 60 * 1000; // 转换为毫秒

        if (timeDiff < minInterval) {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Error checking notification frequency:', error);
      return true; // 出错时默认允许发送
    }
  }

  /**
   * 检查免打扰时间
   */
  static isQuietHours(userId) {
    // 这里可以实现免打扰时间检查逻辑
    // 目前返回false，表示不在免打扰时间
    return false;
  }
}

module.exports = NotificationService;
