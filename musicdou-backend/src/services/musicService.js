const Music = require('../models/Music');
const UserBehavior = require('../models/UserBehavior');
const BatchOperation = require('../models/BatchOperation');
const { deleteFile, getPresignedUrl, BUCKETS } = require('../config/minio');

class MusicService {
  /**
   * 创建新的音乐记录
   * @param {Object} musicData - 音乐数据
   * @returns {Promise<Object>} 创建的音乐记录
   */
  static async createMusic(musicData) {
    try {
      const music = new Music(musicData);
      await music.save();
      
      // 填充上传者信息
      await music.populate('uploadedBy', 'username profile.displayName');
      
      return music;
    } catch (error) {
      throw new Error(`Failed to create music: ${error.message}`);
    }
  }

  /**
   * 根据ID获取音乐详情
   * @param {string} musicId - 音乐ID
   * @param {boolean} includePrivate - 是否包含未审核的音乐
   * @returns {Promise<Object>} 音乐详情
   */
  static async getMusicById(musicId, includePrivate = false) {
    try {
      let query = { _id: musicId };
      if (!includePrivate) {
        query.status = 'approved';
      }

      const music = await Music.findOne(query)
        .populate('uploadedBy', 'username profile.displayName')
        .populate('reviewedBy', 'username profile.displayName');

      if (!music) {
        throw new Error('Music not found');
      }

      return music;
    } catch (error) {
      throw new Error(`Failed to get music: ${error.message}`);
    }
  }

  /**
   * 获取音乐列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 音乐列表和分页信息
   */
  static async getMusicList(options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        status = 'approved',
        uploadedBy,
        quality,
        genre,
        artist,
        sortBy = 'newest'
      } = options;

      const skip = (page - 1) * limit;
      let query = {};

      // 状态过滤
      if (status) query.status = status;
      
      // 上传者过滤
      if (uploadedBy) query.uploadedBy = uploadedBy;
      
      // 音质过滤
      if (quality) query.quality = quality;
      
      // 流派过滤
      if (genre) query.genre = new RegExp(genre, 'i');
      
      // 艺术家过滤
      if (artist) query.artist = new RegExp(artist, 'i');

      // 排序
      let sort = {};
      switch (sortBy) {
        case 'newest':
          sort = { createdAt: -1 };
          break;
        case 'oldest':
          sort = { createdAt: 1 };
          break;
        case 'popular':
          sort = { playCount: -1, favoriteCount: -1 };
          break;
        case 'title':
          sort = { title: 1 };
          break;
        case 'artist':
          sort = { artist: 1, title: 1 };
          break;
        default:
          sort = { createdAt: -1 };
      }

      const [musicList, total] = await Promise.all([
        Music.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .populate('uploadedBy', 'username profile.displayName'),
        Music.countDocuments(query)
      ]);

      return {
        music: musicList,
        pagination: {
          current: page,
          total: Math.ceil(total / limit),
          count: musicList.length,
          totalCount: total
        }
      };
    } catch (error) {
      throw new Error(`Failed to get music list: ${error.message}`);
    }
  }

  /**
   * 搜索音乐
   * @param {string} query - 搜索关键词
   * @param {Object} options - 搜索选项
   * @returns {Promise<Object>} 搜索结果
   */
  static async searchMusic(query, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'relevance',
        quality,
        genre,
        artist
      } = options;

      const skip = (page - 1) * limit;
      
      const results = await Music.searchMusic(query, {
        limit,
        skip,
        sortBy,
        quality,
        genre,
        artist
      });

      const total = await Music.countDocuments({
        $text: { $search: query },
        status: 'approved',
        ...(quality && { quality }),
        ...(genre && { genre: new RegExp(genre, 'i') }),
        ...(artist && { artist: new RegExp(artist, 'i') })
      });

      return {
        music: results,
        pagination: {
          current: page,
          total: Math.ceil(total / limit),
          count: results.length,
          totalCount: total
        },
        query
      };
    } catch (error) {
      throw new Error(`Failed to search music: ${error.message}`);
    }
  }

  /**
   * 更新音乐信息
   * @param {string} musicId - 音乐ID
   * @param {Object} updateData - 更新数据
   * @param {string} userId - 操作用户ID
   * @returns {Promise<Object>} 更新后的音乐记录
   */
  static async updateMusic(musicId, updateData, userId) {
    try {
      const music = await Music.findById(musicId);
      if (!music) {
        throw new Error('Music not found');
      }

      // 检查权限（只有上传者或管理员可以修改）
      if (music.uploadedBy.toString() !== userId) {
        // 这里需要检查用户是否为管理员，暂时跳过
        // throw new Error('Permission denied');
      }

      Object.assign(music, updateData);
      await music.save();

      await music.populate('uploadedBy', 'username profile.displayName');
      return music;
    } catch (error) {
      throw new Error(`Failed to update music: ${error.message}`);
    }
  }

  /**
   * 删除音乐
   * @param {string} musicId - 音乐ID
   * @param {string} userId - 操作用户ID
   * @returns {Promise<boolean>} 删除结果
   */
  static async deleteMusic(musicId, userId) {
    try {
      const music = await Music.findById(musicId);
      if (!music) {
        throw new Error('Music not found');
      }

      // 检查权限
      if (music.uploadedBy.toString() !== userId) {
        // 这里需要检查用户是否为管理员，暂时跳过
        // throw new Error('Permission denied');
      }

      // 删除MinIO中的文件
      try {
        await deleteFile(music.bucket, music.filePath);
        
        // 如果有封面图片，也删除
        if (music.coverImage.objectName) {
          await deleteFile(music.coverImage.bucket, music.coverImage.objectName);
        }
      } catch (minioError) {
        console.warn('Failed to delete files from MinIO:', minioError.message);
        // 继续删除数据库记录，即使MinIO删除失败
      }

      await Music.findByIdAndDelete(musicId);
      return true;
    } catch (error) {
      throw new Error(`Failed to delete music: ${error.message}`);
    }
  }

  /**
   * 审核音乐
   * @param {string} musicId - 音乐ID
   * @param {string} status - 审核状态 (approved/rejected)
   * @param {string} reviewerId - 审核者ID
   * @param {string} reviewNote - 审核备注
   * @returns {Promise<Object>} 审核后的音乐记录
   */
  static async reviewMusic(musicId, status, reviewerId, reviewNote = null) {
    try {
      const music = await Music.findById(musicId);
      if (!music) {
        throw new Error('Music not found');
      }

      music.status = status;
      music.reviewedBy = reviewerId;
      music.reviewedAt = new Date();
      music.reviewNote = reviewNote;

      await music.save();
      await music.populate(['uploadedBy', 'reviewedBy'], 'username profile.displayName');

      return music;
    } catch (error) {
      throw new Error(`Failed to review music: ${error.message}`);
    }
  }

  /**
   * 获取音乐播放URL
   * @param {string} musicId - 音乐ID
   * @param {number} expiry - URL过期时间（秒）
   * @returns {Promise<string>} 播放URL
   */
  static async getMusicPlayUrl(musicId, expiry = 24 * 60 * 60) {
    try {
      const music = await Music.findOne({ _id: musicId, status: 'approved' });
      if (!music) {
        throw new Error('Music not found or not approved');
      }

      // 增加播放次数
      await music.incrementPlayCount();

      // 生成预签名URL
      const playUrl = await getPresignedUrl(music.bucket, music.filePath, expiry);
      
      return {
        playUrl,
        music: {
          id: music._id,
          title: music.title,
          artist: music.artist,
          album: music.album,
          duration: music.duration,
          coverImage: music.coverImage.url
        }
      };
    } catch (error) {
      throw new Error(`Failed to get play URL: ${error.message}`);
    }
  }

  /**
   * 获取热门音乐
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 热门音乐列表
   */
  static async getPopularMusic(limit = 10) {
    try {
      return await Music.getPopular(limit);
    } catch (error) {
      throw new Error(`Failed to get popular music: ${error.message}`);
    }
  }

  /**
   * 获取最新音乐
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 最新音乐列表
   */
  static async getRecentMusic(limit = 10) {
    try {
      return await Music.getRecent(limit);
    } catch (error) {
      throw new Error(`Failed to get recent music: ${error.message}`);
    }
  }

  /**
   * 获取音乐统计信息
   * @returns {Promise<Object>} 统计信息
   */
  static async getMusicStats() {
    try {
      const [
        totalMusic,
        approvedMusic,
        pendingMusic,
        rejectedMusic,
        totalPlayCount,
        totalDownloadCount
      ] = await Promise.all([
        Music.countDocuments(),
        Music.countDocuments({ status: 'approved' }),
        Music.countDocuments({ status: 'pending' }),
        Music.countDocuments({ status: 'rejected' }),
        Music.aggregate([{ $group: { _id: null, total: { $sum: '$playCount' } } }]),
        Music.aggregate([{ $group: { _id: null, total: { $sum: '$downloadCount' } } }])
      ]);

      return {
        totalMusic,
        approvedMusic,
        pendingMusic,
        rejectedMusic,
        totalPlayCount: totalPlayCount[0]?.total || 0,
        totalDownloadCount: totalDownloadCount[0]?.total || 0
      };
    } catch (error) {
      throw new Error(`Failed to get music stats: ${error.message}`);
    }
  }

  /**
   * 批量审核音乐
   * @param {Array} musicIds - 音乐ID数组
   * @param {string} status - 审核状态 (approved/rejected)
   * @param {string} reviewerId - 审核者ID
   * @param {string} reviewNote - 审核备注
   * @returns {Promise<Object>} 批量审核结果
   */
  static async batchReviewMusic(musicIds, status, reviewerId, reviewNote = null) {
    try {
      const results = {
        successCount: 0,
        failedCount: 0,
        results: []
      };

      for (const musicId of musicIds) {
        try {
          const music = await Music.findById(musicId);
          if (!music) {
            results.failedCount++;
            results.results.push({
              musicId,
              success: false,
              error: 'Music not found'
            });
            continue;
          }

          music.status = status;
          music.reviewedBy = reviewerId;
          music.reviewedAt = new Date();
          music.reviewNote = reviewNote;

          await music.save();
          results.successCount++;
          results.results.push({
            musicId,
            success: true,
            title: music.title,
            artist: music.artist
          });
        } catch (error) {
          results.failedCount++;
          results.results.push({
            musicId,
            success: false,
            error: error.message
          });
        }
      }

      return results;
    } catch (error) {
      throw new Error(`Failed to batch review music: ${error.message}`);
    }
  }

  /**
   * 获取审核历史
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 审核历史列表
   */
  static async getReviewHistory(options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        reviewerId,
        status,
        startDate,
        endDate
      } = options;

      const skip = (page - 1) * limit;
      let query = {
        reviewedBy: { $ne: null },
        reviewedAt: { $ne: null }
      };

      // 审核者过滤
      if (reviewerId) query.reviewedBy = reviewerId;

      // 状态过滤
      if (status) query.status = status;

      // 日期范围过滤
      if (startDate || endDate) {
        query.reviewedAt = {};
        if (startDate) query.reviewedAt.$gte = new Date(startDate);
        if (endDate) query.reviewedAt.$lte = new Date(endDate);
      }

      const [reviewHistory, total] = await Promise.all([
        Music.find(query)
          .sort({ reviewedAt: -1 })
          .skip(skip)
          .limit(limit)
          .populate('uploadedBy', 'username profile.displayName')
          .populate('reviewedBy', 'username profile.displayName'),
        Music.countDocuments(query)
      ]);

      return {
        reviews: reviewHistory,
        pagination: {
          current: page,
          total: Math.ceil(total / limit),
          count: reviewHistory.length,
          totalCount: total
        }
      };
    } catch (error) {
      throw new Error(`Failed to get review history: ${error.message}`);
    }
  }

  /**
   * 获取审核统计信息
   * @param {string} period - 统计周期 (7d, 30d, 90d, all)
   * @returns {Promise<Object>} 审核统计信息
   */
  static async getReviewStats(period = '7d') {
    try {
      let dateFilter = {};
      const now = new Date();

      switch (period) {
        case '7d':
          dateFilter = { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) };
          break;
        case '30d':
          dateFilter = { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) };
          break;
        case '90d':
          dateFilter = { $gte: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) };
          break;
        case 'all':
          dateFilter = { $ne: null };
          break;
        default:
          dateFilter = { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) };
      }

      const [
        totalReviewed,
        approvedCount,
        rejectedCount,
        reviewerStats,
        dailyStats
      ] = await Promise.all([
        // 总审核数量
        Music.countDocuments({
          reviewedAt: dateFilter
        }),

        // 通过数量
        Music.countDocuments({
          status: 'approved',
          reviewedAt: dateFilter
        }),

        // 拒绝数量
        Music.countDocuments({
          status: 'rejected',
          reviewedAt: dateFilter
        }),

        // 审核者统计
        Music.aggregate([
          { $match: { reviewedAt: dateFilter } },
          {
            $group: {
              _id: '$reviewedBy',
              count: { $sum: 1 },
              approved: {
                $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
              },
              rejected: {
                $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
              }
            }
          },
          {
            $lookup: {
              from: 'users',
              localField: '_id',
              foreignField: '_id',
              as: 'reviewer'
            }
          },
          { $unwind: '$reviewer' },
          {
            $project: {
              reviewerId: '$_id',
              reviewerName: '$reviewer.username',
              count: 1,
              approved: 1,
              rejected: 1
            }
          },
          { $sort: { count: -1 } }
        ]),

        // 每日统计（最近7天）
        Music.aggregate([
          {
            $match: {
              reviewedAt: {
                $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
              }
            }
          },
          {
            $group: {
              _id: {
                $dateToString: {
                  format: '%Y-%m-%d',
                  date: '$reviewedAt'
                }
              },
              count: { $sum: 1 },
              approved: {
                $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
              },
              rejected: {
                $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
              }
            }
          },
          { $sort: { _id: 1 } }
        ])
      ]);

      return {
        period,
        summary: {
          totalReviewed,
          approvedCount,
          rejectedCount,
          approvalRate: totalReviewed > 0 ? (approvedCount / totalReviewed * 100).toFixed(2) : 0
        },
        reviewerStats,
        dailyStats
      };
    } catch (error) {
      throw new Error(`Failed to get review stats: ${error.message}`);
    }
  }

  /**
   * 高级搜索音乐
   * @param {string} query - 搜索关键词
   * @param {Object} options - 高级搜索选项
   * @returns {Promise<Object>} 搜索结果
   */
  static async advancedSearchMusic(query, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'relevance',
        quality,
        genre,
        artist,
        album,
        year,
        minDuration,
        maxDuration,
        minBitrate,
        maxBitrate,
        uploadedBy,
        dateRange,
        tags,
        hasLyrics,
        exactMatch = false,
        searchFields = ['title', 'artist', 'album']
      } = options;

      const skip = (page - 1) * limit;
      let searchQuery = { status: 'approved' };

      // 文本搜索
      if (query && query.trim()) {
        if (exactMatch) {
          // 精确匹配
          const orConditions = searchFields.map(field => ({
            [field]: new RegExp(`^${query.trim()}$`, 'i')
          }));
          searchQuery.$or = orConditions;
        } else {
          // 模糊搜索
          searchQuery.$text = { $search: query.trim() };
        }
      }

      // 基本过滤器
      if (quality) searchQuery.quality = quality;
      if (genre) searchQuery.genre = new RegExp(genre, 'i');
      if (artist) searchQuery.artist = new RegExp(artist, 'i');
      if (album) searchQuery.album = new RegExp(album, 'i');
      if (year) searchQuery.year = year;
      if (uploadedBy) searchQuery.uploadedBy = uploadedBy;
      if (hasLyrics !== undefined) searchQuery.hasLyrics = hasLyrics;

      // 数值范围过滤器
      if (minDuration || maxDuration) {
        searchQuery.duration = {};
        if (minDuration) searchQuery.duration.$gte = minDuration;
        if (maxDuration) searchQuery.duration.$lte = maxDuration;
      }

      if (minBitrate || maxBitrate) {
        searchQuery.bitrate = {};
        if (minBitrate) searchQuery.bitrate.$gte = minBitrate;
        if (maxBitrate) searchQuery.bitrate.$lte = maxBitrate;
      }

      // 日期范围过滤器
      if (dateRange) {
        const { start, end } = dateRange;
        if (start || end) {
          searchQuery.createdAt = {};
          if (start) searchQuery.createdAt.$gte = new Date(start);
          if (end) searchQuery.createdAt.$lte = new Date(end);
        }
      }

      // 标签过滤器
      if (tags && tags.length > 0) {
        searchQuery.tags = { $in: tags };
      }

      // 排序
      let sort = {};
      switch (sortBy) {
        case 'relevance':
          sort = query && !exactMatch ? { score: { $meta: 'textScore' } } : { createdAt: -1 };
          break;
        case 'newest':
          sort = { createdAt: -1 };
          break;
        case 'oldest':
          sort = { createdAt: 1 };
          break;
        case 'popular':
          sort = { playCount: -1, favoriteCount: -1 };
          break;
        case 'title':
          sort = { title: 1 };
          break;
        case 'artist':
          sort = { artist: 1, title: 1 };
          break;
        case 'duration':
          sort = { duration: -1 };
          break;
        case 'bitrate':
          sort = { bitrate: -1 };
          break;
        default:
          sort = { createdAt: -1 };
      }

      const [musicList, total] = await Promise.all([
        Music.find(searchQuery)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .populate('uploadedBy', 'username profile.displayName'),
        Music.countDocuments(searchQuery)
      ]);

      return {
        music: musicList,
        pagination: {
          current: page,
          total: Math.ceil(total / limit),
          count: musicList.length,
          totalCount: total
        },
        query,
        filters: {
          quality,
          genre,
          artist,
          album,
          year,
          minDuration,
          maxDuration,
          minBitrate,
          maxBitrate,
          tags,
          hasLyrics
        }
      };
    } catch (error) {
      throw new Error(`Failed to perform advanced search: ${error.message}`);
    }
  }

  /**
   * 获取搜索建议
   * @param {string} query - 搜索关键词
   * @param {string} type - 建议类型 (all, artist, album, title)
   * @param {number} limit - 限制数量
   * @returns {Promise<Object>} 搜索建议
   */
  static async getSearchSuggestions(query, type = 'all', limit = 10) {
    try {
      const suggestions = {
        artists: [],
        albums: [],
        titles: [],
        genres: []
      };

      const regex = new RegExp(query, 'i');

      if (type === 'all' || type === 'artist') {
        const artists = await Music.distinct('artist', {
          artist: regex,
          status: 'approved'
        });
        suggestions.artists = artists.slice(0, limit);
      }

      if (type === 'all' || type === 'album') {
        const albums = await Music.distinct('album', {
          album: regex,
          status: 'approved'
        });
        suggestions.albums = albums.slice(0, limit);
      }

      if (type === 'all' || type === 'title') {
        const titles = await Music.find({
          title: regex,
          status: 'approved'
        })
        .select('title artist')
        .limit(limit);
        suggestions.titles = titles.map(music => ({
          title: music.title,
          artist: music.artist
        }));
      }

      if (type === 'all' || type === 'genre') {
        const genres = await Music.distinct('genre', {
          genre: regex,
          status: 'approved'
        });
        suggestions.genres = genres.slice(0, limit);
      }

      return suggestions;
    } catch (error) {
      throw new Error(`Failed to get search suggestions: ${error.message}`);
    }
  }

  /**
   * 获取过滤器选项
   * @returns {Promise<Object>} 过滤器选项
   */
  static async getFilterOptions() {
    try {
      const [
        qualities,
        genres,
        artists,
        years,
        durationRange,
        bitrateRange
      ] = await Promise.all([
        // 音质选项
        Music.distinct('quality', { status: 'approved' }),

        // 流派选项
        Music.distinct('genre', { status: 'approved' }),

        // 热门艺术家
        Music.aggregate([
          { $match: { status: 'approved' } },
          { $group: { _id: '$artist', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 50 },
          { $project: { artist: '$_id', count: 1, _id: 0 } }
        ]),

        // 年份范围
        Music.aggregate([
          { $match: { status: 'approved', year: { $ne: null } } },
          {
            $group: {
              _id: null,
              minYear: { $min: '$year' },
              maxYear: { $max: '$year' }
            }
          }
        ]),

        // 时长范围
        Music.aggregate([
          { $match: { status: 'approved', duration: { $ne: null } } },
          {
            $group: {
              _id: null,
              minDuration: { $min: '$duration' },
              maxDuration: { $max: '$duration' }
            }
          }
        ]),

        // 比特率范围
        Music.aggregate([
          { $match: { status: 'approved', bitrate: { $ne: null } } },
          {
            $group: {
              _id: null,
              minBitrate: { $min: '$bitrate' },
              maxBitrate: { $max: '$bitrate' }
            }
          }
        ])
      ]);

      return {
        qualities: qualities.filter(q => q),
        genres: genres.filter(g => g).sort(),
        artists: artists.map(a => a.artist).filter(a => a),
        yearRange: years[0] || { minYear: null, maxYear: null },
        durationRange: durationRange[0] || { minDuration: null, maxDuration: null },
        bitrateRange: bitrateRange[0] || { minBitrate: null, maxBitrate: null }
      };
    } catch (error) {
      throw new Error(`Failed to get filter options: ${error.message}`);
    }
  }

  /**
   * 获取相似音乐
   * @param {string} musicId - 音乐ID
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 相似音乐列表
   */
  static async getSimilarMusic(musicId, limit = 10) {
    try {
      const music = await Music.findOne({ _id: musicId, status: 'approved' });
      if (!music) {
        throw new Error('Music not found');
      }

      // 基于多个维度查找相似音乐
      const similarityQueries = [
        // 同艺术家
        {
          artist: music.artist,
          _id: { $ne: musicId }
        },
        // 同专辑
        {
          album: music.album,
          _id: { $ne: musicId }
        },
        // 同流派
        {
          genre: music.genre,
          _id: { $ne: musicId }
        },
        // 相似标签
        {
          tags: { $in: music.tags || [] },
          _id: { $ne: musicId }
        }
      ];

      const similarMusic = [];

      for (const query of similarityQueries) {
        if (similarMusic.length >= limit) break;

        const results = await Music.find({
          ...query,
          status: 'approved'
        })
        .limit(limit - similarMusic.length)
        .populate('uploadedBy', 'username profile.displayName');

        // 避免重复
        const newResults = results.filter(result =>
          !similarMusic.some(existing => existing._id.toString() === result._id.toString())
        );

        similarMusic.push(...newResults);
      }

      // 如果还没有足够的相似音乐，添加热门音乐
      if (similarMusic.length < limit) {
        const popularMusic = await Music.find({
          status: 'approved',
          _id: { $ne: musicId, $nin: similarMusic.map(m => m._id) }
        })
        .sort({ playCount: -1, favoriteCount: -1 })
        .limit(limit - similarMusic.length)
        .populate('uploadedBy', 'username profile.displayName');

        similarMusic.push(...popularMusic);
      }

      return similarMusic.slice(0, limit);
    } catch (error) {
      throw new Error(`Failed to get similar music: ${error.message}`);
    }
  }

  /**
   * 获取个性化推荐音乐
   * @param {string} userId - 用户ID
   * @param {Object} options - 推荐选项
   * @returns {Promise<Object>} 推荐音乐列表
   */
  static async getRecommendedMusic(userId, options = {}) {
    try {
      const { limit = 20, algorithm = 'hybrid' } = options;

      let recommendations = [];

      switch (algorithm) {
        case 'collaborative':
          recommendations = await this.getCollaborativeRecommendations(userId, limit);
          break;
        case 'content':
          recommendations = await this.getContentBasedRecommendations(userId, limit);
          break;
        case 'hybrid':
        default:
          recommendations = await this.getHybridRecommendations(userId, limit);
          break;
      }

      return {
        algorithm,
        recommendations,
        count: recommendations.length
      };
    } catch (error) {
      throw new Error(`Failed to get recommended music: ${error.message}`);
    }
  }

  /**
   * 协同过滤推荐
   * @param {string} userId - 用户ID
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 推荐音乐列表
   */
  static async getCollaborativeRecommendations(userId, limit = 20) {
    try {
      // 获取用户偏好
      const userPreferences = await UserBehavior.getUserPreferences(userId, { limit: 10 });

      if (userPreferences.length === 0) {
        // 如果用户没有行为记录，返回热门音乐
        return await this.getPopularMusic(limit);
      }

      // 找到相似用户
      const similarUsers = await this.findSimilarUsers(userId, 10);

      if (similarUsers.length === 0) {
        return await this.getContentBasedRecommendations(userId, limit);
      }

      // 获取相似用户喜欢的音乐
      const recommendations = await UserBehavior.aggregate([
        {
          $match: {
            userId: { $in: similarUsers.map(u => u.userId) },
            weight: { $gte: 2 }
          }
        },
        {
          $group: {
            _id: '$musicId',
            totalWeight: { $sum: '$weight' },
            userCount: { $sum: 1 }
          }
        },
        {
          $lookup: {
            from: 'music',
            localField: '_id',
            foreignField: '_id',
            as: 'music'
          }
        },
        { $unwind: '$music' },
        {
          $match: {
            'music.status': 'approved'
          }
        },
        {
          $sort: { totalWeight: -1, userCount: -1 }
        },
        { $limit: limit },
        {
          $project: {
            music: '$music',
            score: '$totalWeight',
            reason: 'collaborative_filtering'
          }
        }
      ]);

      return recommendations.map(r => ({
        ...r.music,
        recommendationScore: r.score,
        recommendationReason: r.reason
      }));
    } catch (error) {
      throw new Error(`Failed to get collaborative recommendations: ${error.message}`);
    }
  }

  /**
   * 基于内容的推荐
   * @param {string} userId - 用户ID
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 推荐音乐列表
   */
  static async getContentBasedRecommendations(userId, limit = 20) {
    try {
      // 获取用户偏好
      const userPreferences = await UserBehavior.getUserPreferences(userId, { limit: 5 });

      if (userPreferences.length === 0) {
        return await this.getPopularMusic(limit);
      }

      // 提取用户喜欢的流派和艺术家
      const preferredGenres = userPreferences
        .filter(p => p._id.genre)
        .map(p => p._id.genre);

      const preferredArtists = userPreferences
        .filter(p => p._id.artist)
        .map(p => p._id.artist);

      // 获取用户已经听过的音乐
      const listenedMusic = await UserBehavior.distinct('musicId', { userId });

      // 基于流派和艺术家推荐
      const recommendations = await Music.find({
        status: 'approved',
        _id: { $nin: listenedMusic },
        $or: [
          { genre: { $in: preferredGenres } },
          { artist: { $in: preferredArtists } }
        ]
      })
      .sort({ playCount: -1, favoriteCount: -1 })
      .limit(limit)
      .populate('uploadedBy', 'username profile.displayName');

      return recommendations.map(music => ({
        ...music.toObject(),
        recommendationScore: this.calculateContentScore(music, userPreferences),
        recommendationReason: 'content_based'
      }));
    } catch (error) {
      throw new Error(`Failed to get content-based recommendations: ${error.message}`);
    }
  }

  /**
   * 混合推荐算法
   * @param {string} userId - 用户ID
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 推荐音乐列表
   */
  static async getHybridRecommendations(userId, limit = 20) {
    try {
      const collaborativeLimit = Math.ceil(limit * 0.6);
      const contentLimit = Math.ceil(limit * 0.4);

      const [collaborative, content] = await Promise.all([
        this.getCollaborativeRecommendations(userId, collaborativeLimit),
        this.getContentBasedRecommendations(userId, contentLimit)
      ]);

      // 合并并去重
      const combined = [...collaborative, ...content];
      const unique = combined.filter((music, index, self) =>
        index === self.findIndex(m => m._id.toString() === music._id.toString())
      );

      // 按推荐分数排序
      unique.sort((a, b) => (b.recommendationScore || 0) - (a.recommendationScore || 0));

      return unique.slice(0, limit);
    } catch (error) {
      throw new Error(`Failed to get hybrid recommendations: ${error.message}`);
    }
  }

  /**
   * 找到相似用户
   * @param {string} userId - 用户ID
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 相似用户列表
   */
  static async findSimilarUsers(userId, limit = 10) {
    try {
      // 获取所有有行为记录的用户
      const allUsers = await UserBehavior.distinct('userId', {
        userId: { $ne: userId }
      });

      const similarities = [];

      for (const otherUserId of allUsers.slice(0, 100)) { // 限制计算数量
        const similarity = await UserBehavior.getUserSimilarity(userId, otherUserId);
        if (similarity > 0.1) { // 相似度阈值
          similarities.push({
            userId: otherUserId,
            similarity
          });
        }
      }

      return similarities
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit);
    } catch (error) {
      throw new Error(`Failed to find similar users: ${error.message}`);
    }
  }

  /**
   * 计算内容相似度分数
   * @param {Object} music - 音乐对象
   * @param {Array} userPreferences - 用户偏好
   * @returns {number} 相似度分数
   */
  static calculateContentScore(music, userPreferences) {
    let score = 0;

    userPreferences.forEach(pref => {
      if (pref._id.genre === music.genre) {
        score += pref.totalWeight * 0.6;
      }
      if (pref._id.artist === music.artist) {
        score += pref.totalWeight * 0.4;
      }
    });

    return score;
  }

  /**
   * 获取基于流派的推荐
   * @param {string} genre - 流派
   * @param {string} userId - 用户ID（可选）
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 推荐音乐列表
   */
  static async getGenreBasedRecommendations(genre, userId = null, limit = 10) {
    try {
      let excludeMusic = [];

      if (userId) {
        // 排除用户已经听过的音乐
        excludeMusic = await UserBehavior.distinct('musicId', { userId });
      }

      const recommendations = await Music.find({
        status: 'approved',
        genre: new RegExp(genre, 'i'),
        _id: { $nin: excludeMusic }
      })
      .sort({ playCount: -1, favoriteCount: -1, createdAt: -1 })
      .limit(limit)
      .populate('uploadedBy', 'username profile.displayName');

      return recommendations;
    } catch (error) {
      throw new Error(`Failed to get genre-based recommendations: ${error.message}`);
    }
  }

  /**
   * 获取热门趋势音乐
   * @param {string} period - 时间周期
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 趋势音乐列表
   */
  static async getTrendingMusic(period = '7d', limit = 20) {
    try {
      const now = new Date();
      let startDate;

      switch (period) {
        case '1d':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      }

      // 基于用户行为计算趋势
      const trending = await UserBehavior.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate },
            actionType: { $in: ['play', 'like', 'share'] }
          }
        },
        {
          $group: {
            _id: '$musicId',
            playCount: {
              $sum: { $cond: [{ $eq: ['$actionType', 'play'] }, 1, 0] }
            },
            likeCount: {
              $sum: { $cond: [{ $eq: ['$actionType', 'like'] }, 1, 0] }
            },
            shareCount: {
              $sum: { $cond: [{ $eq: ['$actionType', 'share'] }, 1, 0] }
            },
            totalWeight: { $sum: '$weight' }
          }
        },
        {
          $addFields: {
            trendScore: {
              $add: [
                { $multiply: ['$playCount', 1] },
                { $multiply: ['$likeCount', 3] },
                { $multiply: ['$shareCount', 5] }
              ]
            }
          }
        },
        {
          $lookup: {
            from: 'music',
            localField: '_id',
            foreignField: '_id',
            as: 'music'
          }
        },
        { $unwind: '$music' },
        {
          $match: {
            'music.status': 'approved'
          }
        },
        {
          $sort: { trendScore: -1 }
        },
        { $limit: limit },
        {
          $project: {
            music: '$music',
            trendScore: 1,
            playCount: 1,
            likeCount: 1,
            shareCount: 1
          }
        }
      ]);

      return trending.map(t => ({
        ...t.music,
        trendingScore: t.trendScore,
        periodStats: {
          plays: t.playCount,
          likes: t.likeCount,
          shares: t.shareCount
        }
      }));
    } catch (error) {
      throw new Error(`Failed to get trending music: ${error.message}`);
    }
  }

  /**
   * 获取新发现音乐
   * @param {string} userId - 用户ID
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 新发现音乐列表
   */
  static async getDiscoverMusic(userId, limit = 20) {
    try {
      // 获取用户已经听过的音乐
      const listenedMusic = await UserBehavior.distinct('musicId', { userId });

      // 获取用户偏好
      const userPreferences = await UserBehavior.getUserPreferences(userId, { limit: 3 });

      let query = {
        status: 'approved',
        _id: { $nin: listenedMusic }
      };

      // 如果有偏好，优先推荐相关流派
      if (userPreferences.length > 0) {
        const preferredGenres = userPreferences
          .filter(p => p._id.genre)
          .map(p => p._id.genre);

        if (preferredGenres.length > 0) {
          query.genre = { $in: preferredGenres };
        }
      }

      const discoverMusic = await Music.find(query)
        .sort({ createdAt: -1, playCount: -1 })
        .limit(limit)
        .populate('uploadedBy', 'username profile.displayName');

      return discoverMusic;
    } catch (error) {
      throw new Error(`Failed to get discover music: ${error.message}`);
    }
  }

  /**
   * 记录用户播放行为
   * @param {string} userId - 用户ID
   * @param {string} musicId - 音乐ID
   * @param {Object} behaviorData - 行为数据
   * @returns {Promise<Object>} 行为记录
   */
  static async recordPlayBehavior(userId, musicId, behaviorData = {}) {
    try {
      const { duration = 0, completed = false } = behaviorData;

      // 获取音乐信息计算播放进度
      const music = await Music.findById(musicId);
      if (!music) {
        throw new Error('Music not found');
      }

      const playProgress = music.duration > 0 ?
        Math.min(100, (duration / music.duration) * 100) : 0;

      // 记录播放行为
      const behavior = await UserBehavior.recordBehavior(userId, musicId, 'play', {
        playDuration: duration,
        isCompleted: completed,
        playProgress
      });

      // 如果播放完成，记录完成行为
      if (completed) {
        await UserBehavior.recordBehavior(userId, musicId, 'complete', {
          playDuration: duration,
          isCompleted: true,
          playProgress: 100
        });
      }

      return behavior;
    } catch (error) {
      throw new Error(`Failed to record play behavior: ${error.message}`);
    }
  }

  /**
   * 批量删除音乐
   * @param {Array} musicIds - 音乐ID数组
   * @param {string} userId - 操作用户ID
   * @param {boolean} force - 是否强制删除
   * @returns {Promise<Object>} 批量删除结果
   */
  static async batchDeleteMusic(musicIds, userId, force = false) {
    try {
      // 创建批量操作记录
      const operation = await BatchOperation.createOperation(
        'batch_delete',
        userId,
        musicIds,
        { operationParams: { force } }
      );

      await operation.markAsStarted();

      const results = {
        successCount: 0,
        failedCount: 0,
        results: []
      };

      for (const musicId of musicIds) {
        try {
          const music = await Music.findById(musicId);
          if (!music) {
            const detail = {
              musicId,
              success: false,
              error: 'Music not found'
            };
            results.failedCount++;
            results.results.push(detail);
            operation.addDetail(detail);
            continue;
          }

          // 检查权限（除非强制删除）
          if (!force && music.uploadedBy.toString() !== userId) {
            // 这里应该检查是否为管理员，暂时跳过
          }

          // 删除MinIO中的文件
          try {
            await deleteFile(music.bucket, music.filePath);
            if (music.coverImage.objectName) {
              await deleteFile(music.coverImage.bucket, music.coverImage.objectName);
            }
          } catch (minioError) {
            console.warn('Failed to delete files from MinIO:', minioError.message);
          }

          await Music.findByIdAndDelete(musicId);

          const detail = {
            musicId,
            success: true,
            previousValue: { title: music.title, artist: music.artist }
          };
          results.successCount++;
          results.results.push(detail);
          operation.addDetail(detail);
        } catch (error) {
          const detail = {
            musicId,
            success: false,
            error: error.message
          };
          results.failedCount++;
          results.results.push(detail);
          operation.addDetail(detail);
        }
      }

      await operation.markAsCompleted(results);
      return results;
    } catch (error) {
      throw new Error(`Failed to batch delete music: ${error.message}`);
    }
  }

  /**
   * 批量更新音乐信息
   * @param {Array} musicIds - 音乐ID数组
   * @param {Object} updateData - 更新数据
   * @param {string} userId - 操作用户ID
   * @returns {Promise<Object>} 批量更新结果
   */
  static async batchUpdateMusic(musicIds, updateData, userId) {
    try {
      // 创建批量操作记录
      const operation = await BatchOperation.createOperation(
        'batch_update',
        userId,
        musicIds,
        { operationParams: { updateData } }
      );

      await operation.markAsStarted();

      // 过滤允许更新的字段
      const allowedFields = [
        'title', 'artist', 'album', 'genre', 'year',
        'tags', 'lyrics', 'hasLyrics'
      ];

      const filteredData = {};
      allowedFields.forEach(field => {
        if (updateData[field] !== undefined) {
          filteredData[field] = updateData[field];
        }
      });

      if (Object.keys(filteredData).length === 0) {
        throw new Error('No valid fields to update');
      }

      const results = {
        successCount: 0,
        failedCount: 0,
        results: []
      };

      for (const musicId of musicIds) {
        try {
          const music = await Music.findById(musicId);
          if (!music) {
            const detail = {
              musicId,
              success: false,
              error: 'Music not found'
            };
            results.failedCount++;
            results.results.push(detail);
            operation.addDetail(detail);
            continue;
          }

          const previousValue = {};
          Object.keys(filteredData).forEach(field => {
            previousValue[field] = music[field];
          });

          Object.assign(music, filteredData);
          await music.save();

          const detail = {
            musicId,
            success: true,
            previousValue,
            newValue: filteredData
          };
          results.successCount++;
          results.results.push(detail);
          operation.addDetail(detail);
        } catch (error) {
          const detail = {
            musicId,
            success: false,
            error: error.message
          };
          results.failedCount++;
          results.results.push(detail);
          operation.addDetail(detail);
        }
      }

      await operation.markAsCompleted(results);
      return results;
    } catch (error) {
      throw new Error(`Failed to batch update music: ${error.message}`);
    }
  }

  /**
   * 批量移动音乐状态
   * @param {Array} musicIds - 音乐ID数组
   * @param {string} targetStatus - 目标状态
   * @param {string} userId - 操作用户ID
   * @param {string} reason - 操作原因
   * @returns {Promise<Object>} 批量移动结果
   */
  static async batchMoveMusic(musicIds, targetStatus, userId, reason = null) {
    try {
      // 创建批量操作记录
      const operation = await BatchOperation.createOperation(
        'batch_move',
        userId,
        musicIds,
        {
          operationParams: { targetStatus },
          reason
        }
      );

      await operation.markAsStarted();

      const results = {
        successCount: 0,
        failedCount: 0,
        results: []
      };

      for (const musicId of musicIds) {
        try {
          const music = await Music.findById(musicId);
          if (!music) {
            const detail = {
              musicId,
              success: false,
              error: 'Music not found'
            };
            results.failedCount++;
            results.results.push(detail);
            operation.addDetail(detail);
            continue;
          }

          const previousStatus = music.status;
          music.status = targetStatus;

          if (targetStatus !== 'pending') {
            music.reviewedBy = userId;
            music.reviewedAt = new Date();
            music.reviewNote = reason;
          }

          await music.save();

          const detail = {
            musicId,
            success: true,
            previousValue: { status: previousStatus },
            newValue: { status: targetStatus }
          };
          results.successCount++;
          results.results.push(detail);
          operation.addDetail(detail);
        } catch (error) {
          const detail = {
            musicId,
            success: false,
            error: error.message
          };
          results.failedCount++;
          results.results.push(detail);
          operation.addDetail(detail);
        }
      }

      await operation.markAsCompleted(results);
      return results;
    } catch (error) {
      throw new Error(`Failed to batch move music: ${error.message}`);
    }
  }

  /**
   * 批量导出音乐信息
   * @param {Array} musicIds - 音乐ID数组
   * @param {string} format - 导出格式 (json/csv)
   * @param {Array} fields - 导出字段
   * @returns {Promise<string>} 导出数据
   */
  static async batchExportMusic(musicIds, format = 'json', fields = null) {
    try {
      const defaultFields = [
        '_id', 'title', 'artist', 'album', 'genre', 'year',
        'duration', 'bitrate', 'quality', 'status', 'playCount',
        'favoriteCount', 'downloadCount', 'createdAt'
      ];

      const exportFields = fields || defaultFields;

      const music = await Music.find({
        _id: { $in: musicIds }
      })
      .select(exportFields.join(' '))
      .populate('uploadedBy', 'username')
      .populate('reviewedBy', 'username');

      if (format === 'csv') {
        // 生成CSV格式
        const headers = exportFields.join(',');
        const rows = music.map(item => {
          return exportFields.map(field => {
            let value = item[field];

            // 处理特殊字段
            if (field === 'uploadedBy' && value) {
              value = value.username;
            } else if (field === 'reviewedBy' && value) {
              value = value.username;
            } else if (value instanceof Date) {
              value = value.toISOString();
            } else if (Array.isArray(value)) {
              value = value.join(';');
            } else if (typeof value === 'object' && value !== null) {
              value = JSON.stringify(value);
            }

            // 转义CSV特殊字符
            if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
              value = `"${value.replace(/"/g, '""')}"`;
            }

            return value || '';
          }).join(',');
        });

        return [headers, ...rows].join('\n');
      } else {
        // 生成JSON格式
        return JSON.stringify(music, null, 2);
      }
    } catch (error) {
      throw new Error(`Failed to batch export music: ${error.message}`);
    }
  }

  /**
   * 获取批量操作历史
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 操作历史列表
   */
  static async getBatchOperationHistory(options = {}) {
    try {
      return await BatchOperation.getOperationHistory(options);
    } catch (error) {
      throw new Error(`Failed to get batch operation history: ${error.message}`);
    }
  }

  /**
   * 获取批量操作统计
   * @param {string} period - 统计周期
   * @returns {Promise<Array>} 操作统计
   */
  static async getBatchOperationStats(period = '30d') {
    try {
      return await BatchOperation.getOperationStats(period);
    } catch (error) {
      throw new Error(`Failed to get batch operation stats: ${error.message}`);
    }
  }

  /**
   * 获取详细音乐统计
   * @param {string} period - 统计周期
   * @param {string} groupBy - 分组方式 (day, week, month)
   * @returns {Promise<Object>} 详细统计数据
   */
  static async getDetailedMusicStats(period = '30d', groupBy = 'day') {
    try {
      const now = new Date();
      let startDate, dateFormat, groupFormat;

      switch (period) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }

      // 设置日期格式
      switch (groupBy) {
        case 'day':
          dateFormat = '%Y-%m-%d';
          groupFormat = { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } };
          break;
        case 'week':
          dateFormat = '%Y-W%U';
          groupFormat = { $dateToString: { format: '%Y-W%U', date: '$createdAt' } };
          break;
        case 'month':
          dateFormat = '%Y-%m';
          groupFormat = { $dateToString: { format: '%Y-%m', date: '$createdAt' } };
          break;
        default:
          dateFormat = '%Y-%m-%d';
          groupFormat = { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } };
      }

      const [
        overallStats,
        timeSeriesStats,
        statusDistribution,
        qualityDistribution,
        genreDistribution,
        uploadTrends
      ] = await Promise.all([
        // 总体统计
        Music.aggregate([
          {
            $group: {
              _id: null,
              totalMusic: { $sum: 1 },
              totalPlayCount: { $sum: '$playCount' },
              totalDownloadCount: { $sum: '$downloadCount' },
              totalFavoriteCount: { $sum: '$favoriteCount' },
              avgDuration: { $avg: '$duration' },
              avgBitrate: { $avg: '$bitrate' }
            }
          }
        ]),

        // 时间序列统计
        Music.aggregate([
          {
            $match: {
              createdAt: { $gte: startDate }
            }
          },
          {
            $group: {
              _id: groupFormat,
              count: { $sum: 1 },
              totalPlays: { $sum: '$playCount' },
              totalDownloads: { $sum: '$downloadCount' },
              totalFavorites: { $sum: '$favoriteCount' }
            }
          },
          { $sort: { _id: 1 } }
        ]),

        // 状态分布
        Music.aggregate([
          {
            $group: {
              _id: '$status',
              count: { $sum: 1 },
              percentage: { $sum: 1 }
            }
          }
        ]),

        // 音质分布
        Music.aggregate([
          {
            $group: {
              _id: '$quality',
              count: { $sum: 1 },
              avgBitrate: { $avg: '$bitrate' }
            }
          },
          { $sort: { count: -1 } }
        ]),

        // 流派分布
        Music.aggregate([
          {
            $match: {
              status: 'approved',
              genre: { $ne: null }
            }
          },
          {
            $group: {
              _id: '$genre',
              count: { $sum: 1 },
              totalPlays: { $sum: '$playCount' },
              avgRating: { $avg: '$rating' }
            }
          },
          { $sort: { count: -1 } },
          { $limit: 20 }
        ]),

        // 上传趋势
        Music.aggregate([
          {
            $match: {
              createdAt: { $gte: startDate }
            }
          },
          {
            $group: {
              _id: {
                date: groupFormat,
                status: '$status'
              },
              count: { $sum: 1 }
            }
          },
          {
            $group: {
              _id: '$_id.date',
              statusBreakdown: {
                $push: {
                  status: '$_id.status',
                  count: '$count'
                }
              },
              totalUploads: { $sum: '$count' }
            }
          },
          { $sort: { _id: 1 } }
        ])
      ]);

      return {
        period,
        groupBy,
        overall: overallStats[0] || {},
        timeSeries: timeSeriesStats,
        distributions: {
          status: statusDistribution,
          quality: qualityDistribution,
          genre: genreDistribution
        },
        trends: {
          uploads: uploadTrends
        }
      };
    } catch (error) {
      throw new Error(`Failed to get detailed music stats: ${error.message}`);
    }
  }

  /**
   * 获取用户行为分析
   * @param {string} period - 分析周期
   * @param {string} analysisType - 分析类型
   * @returns {Promise<Object>} 用户行为分析数据
   */
  static async getUserBehaviorAnalysis(period = '30d', analysisType = 'overview') {
    try {
      const now = new Date();
      let startDate;

      switch (period) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }

      const baseMatch = {
        createdAt: { $gte: startDate }
      };

      let analysis = {};

      if (analysisType === 'overview' || analysisType === 'all') {
        // 用户行为概览
        const [
          actionStats,
          userEngagement,
          popularContent,
          behaviorPatterns
        ] = await Promise.all([
          // 行为类型统计
          UserBehavior.aggregate([
            { $match: baseMatch },
            {
              $group: {
                _id: '$actionType',
                count: { $sum: 1 },
                uniqueUsers: { $addToSet: '$userId' },
                avgWeight: { $avg: '$weight' }
              }
            },
            {
              $addFields: {
                uniqueUserCount: { $size: '$uniqueUsers' }
              }
            },
            { $sort: { count: -1 } }
          ]),

          // 用户参与度
          UserBehavior.aggregate([
            { $match: baseMatch },
            {
              $group: {
                _id: '$userId',
                totalActions: { $sum: 1 },
                totalWeight: { $sum: '$weight' },
                actionTypes: { $addToSet: '$actionType' },
                avgPlayDuration: { $avg: '$playDuration' }
              }
            },
            {
              $group: {
                _id: null,
                totalUsers: { $sum: 1 },
                avgActionsPerUser: { $avg: '$totalActions' },
                avgWeightPerUser: { $avg: '$totalWeight' },
                highlyEngagedUsers: {
                  $sum: { $cond: [{ $gte: ['$totalActions', 50] }, 1, 0] }
                }
              }
            }
          ]),

          // 热门内容
          UserBehavior.aggregate([
            {
              $match: {
                ...baseMatch,
                actionType: { $in: ['play', 'like', 'share'] }
              }
            },
            {
              $group: {
                _id: '$musicId',
                totalInteractions: { $sum: 1 },
                totalWeight: { $sum: '$weight' },
                uniqueUsers: { $addToSet: '$userId' }
              }
            },
            {
              $addFields: {
                uniqueUserCount: { $size: '$uniqueUsers' }
              }
            },
            {
              $lookup: {
                from: 'music',
                localField: '_id',
                foreignField: '_id',
                as: 'music'
              }
            },
            { $unwind: '$music' },
            { $sort: { totalWeight: -1 } },
            { $limit: 20 }
          ]),

          // 行为模式
          UserBehavior.aggregate([
            { $match: baseMatch },
            {
              $group: {
                _id: {
                  hour: { $hour: '$createdAt' },
                  dayOfWeek: { $dayOfWeek: '$createdAt' }
                },
                count: { $sum: 1 }
              }
            },
            { $sort: { count: -1 } }
          ])
        ]);

        analysis.overview = {
          actionStats,
          userEngagement: userEngagement[0] || {},
          popularContent,
          behaviorPatterns
        };
      }

      return {
        period,
        analysisType,
        ...analysis
      };
    } catch (error) {
      throw new Error(`Failed to get user behavior analysis: ${error.message}`);
    }
  }

  /**
   * 获取音乐趋势分析
   * @param {string} period - 分析周期
   * @param {string} trendType - 趋势类型
   * @returns {Promise<Object>} 趋势分析数据
   */
  static async getMusicTrendAnalysis(period = '90d', trendType = 'popularity') {
    try {
      const now = new Date();
      let startDate;

      switch (period) {
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      }

      let trends = {};

      if (trendType === 'popularity' || trendType === 'all') {
        // 流行度趋势
        trends.popularity = await UserBehavior.aggregate([
          {
            $match: {
              createdAt: { $gte: startDate },
              actionType: { $in: ['play', 'like', 'share'] }
            }
          },
          {
            $group: {
              _id: {
                musicId: '$musicId',
                week: { $week: '$createdAt' }
              },
              weeklyScore: { $sum: '$weight' }
            }
          },
          {
            $group: {
              _id: '$_id.musicId',
              weeklyScores: {
                $push: {
                  week: '$_id.week',
                  score: '$weeklyScore'
                }
              },
              totalScore: { $sum: '$weeklyScore' }
            }
          },
          {
            $lookup: {
              from: 'music',
              localField: '_id',
              foreignField: '_id',
              as: 'music'
            }
          },
          { $unwind: '$music' },
          { $sort: { totalScore: -1 } },
          { $limit: 50 }
        ]);
      }

      if (trendType === 'genre' || trendType === 'all') {
        // 流派趋势
        trends.genre = await UserBehavior.aggregate([
          {
            $match: {
              createdAt: { $gte: startDate }
            }
          },
          {
            $lookup: {
              from: 'music',
              localField: 'musicId',
              foreignField: '_id',
              as: 'music'
            }
          },
          { $unwind: '$music' },
          {
            $group: {
              _id: {
                genre: '$music.genre',
                month: { $month: '$createdAt' }
              },
              monthlyInteractions: { $sum: 1 },
              monthlyWeight: { $sum: '$weight' }
            }
          },
          {
            $group: {
              _id: '$_id.genre',
              monthlyData: {
                $push: {
                  month: '$_id.month',
                  interactions: '$monthlyInteractions',
                  weight: '$monthlyWeight'
                }
              },
              totalInteractions: { $sum: '$monthlyInteractions' }
            }
          },
          { $sort: { totalInteractions: -1 } },
          { $limit: 20 }
        ]);
      }

      return {
        period,
        trendType,
        ...trends
      };
    } catch (error) {
      throw new Error(`Failed to get music trend analysis: ${error.message}`);
    }
  }

  /**
   * 获取流派分析
   * @param {string} period - 分析周期
   * @param {number} limit - 限制数量
   * @returns {Promise<Object>} 流派分析数据
   */
  static async getGenreAnalysis(period = '30d', limit = 20) {
    try {
      const now = new Date();
      let startDate;

      switch (period) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }

      const [
        genreStats,
        genreGrowth,
        genreEngagement
      ] = await Promise.all([
        // 流派统计
        Music.aggregate([
          {
            $match: {
              status: 'approved',
              genre: { $ne: null }
            }
          },
          {
            $group: {
              _id: '$genre',
              totalMusic: { $sum: 1 },
              totalPlays: { $sum: '$playCount' },
              totalFavorites: { $sum: '$favoriteCount' },
              totalDownloads: { $sum: '$downloadCount' },
              avgDuration: { $avg: '$duration' },
              avgBitrate: { $avg: '$bitrate' }
            }
          },
          {
            $addFields: {
              avgPlaysPerMusic: { $divide: ['$totalPlays', '$totalMusic'] }
            }
          },
          { $sort: { totalMusic: -1 } },
          { $limit: limit }
        ]),

        // 流派增长趋势
        Music.aggregate([
          {
            $match: {
              createdAt: { $gte: startDate },
              status: 'approved',
              genre: { $ne: null }
            }
          },
          {
            $group: {
              _id: {
                genre: '$genre',
                month: { $month: '$createdAt' }
              },
              newMusic: { $sum: 1 }
            }
          },
          {
            $group: {
              _id: '$_id.genre',
              monthlyGrowth: {
                $push: {
                  month: '$_id.month',
                  count: '$newMusic'
                }
              },
              totalNewMusic: { $sum: '$newMusic' }
            }
          },
          { $sort: { totalNewMusic: -1 } },
          { $limit: limit }
        ]),

        // 流派用户参与度
        UserBehavior.aggregate([
          {
            $match: {
              createdAt: { $gte: startDate }
            }
          },
          {
            $lookup: {
              from: 'music',
              localField: 'musicId',
              foreignField: '_id',
              as: 'music'
            }
          },
          { $unwind: '$music' },
          {
            $match: {
              'music.genre': { $ne: null }
            }
          },
          {
            $group: {
              _id: '$music.genre',
              totalInteractions: { $sum: 1 },
              uniqueUsers: { $addToSet: '$userId' },
              totalWeight: { $sum: '$weight' },
              avgPlayDuration: { $avg: '$playDuration' }
            }
          },
          {
            $addFields: {
              uniqueUserCount: { $size: '$uniqueUsers' },
              avgWeightPerInteraction: { $divide: ['$totalWeight', '$totalInteractions'] }
            }
          },
          { $sort: { totalWeight: -1 } },
          { $limit: limit }
        ])
      ]);

      return {
        period,
        stats: genreStats,
        growth: genreGrowth,
        engagement: genreEngagement
      };
    } catch (error) {
      throw new Error(`Failed to get genre analysis: ${error.message}`);
    }
  }

  /**
   * 获取艺术家排行榜
   * @param {string} period - 排行周期
   * @param {string} rankBy - 排行依据
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 艺术家排行榜
   */
  static async getArtistRanking(period = '30d', rankBy = 'playCount', limit = 50) {
    try {
      const now = new Date();
      let startDate;

      switch (period) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        case 'all':
          startDate = new Date(0);
          break;
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }

      let sortField;
      switch (rankBy) {
        case 'playCount':
          sortField = { totalPlays: -1 };
          break;
        case 'favoriteCount':
          sortField = { totalFavorites: -1 };
          break;
        case 'downloadCount':
          sortField = { totalDownloads: -1 };
          break;
        case 'musicCount':
          sortField = { totalMusic: -1 };
          break;
        default:
          sortField = { totalPlays: -1 };
      }

      const ranking = await Music.aggregate([
        {
          $match: {
            status: 'approved',
            artist: { $ne: null },
            createdAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: '$artist',
            totalMusic: { $sum: 1 },
            totalPlays: { $sum: '$playCount' },
            totalFavorites: { $sum: '$favoriteCount' },
            totalDownloads: { $sum: '$downloadCount' },
            avgDuration: { $avg: '$duration' },
            genres: { $addToSet: '$genre' },
            latestRelease: { $max: '$createdAt' }
          }
        },
        {
          $addFields: {
            avgPlaysPerMusic: { $divide: ['$totalPlays', '$totalMusic'] },
            genreCount: { $size: '$genres' }
          }
        },
        { $sort: sortField },
        { $limit: limit }
      ]);

      return ranking.map((artist, index) => ({
        rank: index + 1,
        artist: artist._id,
        ...artist
      }));
    } catch (error) {
      throw new Error(`Failed to get artist ranking: ${error.message}`);
    }
  }

  /**
   * 获取系统性能指标
   * @param {string} period - 指标周期
   * @returns {Promise<Object>} 系统指标数据
   */
  static async getSystemMetrics(period = '24h') {
    try {
      const now = new Date();
      let startDate;

      switch (period) {
        case '1h':
          startDate = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case '24h':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      }

      const [
        apiMetrics,
        storageMetrics,
        userMetrics,
        errorMetrics
      ] = await Promise.all([
        // API使用指标
        UserBehavior.aggregate([
          {
            $match: {
              createdAt: { $gte: startDate }
            }
          },
          {
            $group: {
              _id: {
                hour: { $hour: '$createdAt' },
                actionType: '$actionType'
              },
              count: { $sum: 1 }
            }
          },
          {
            $group: {
              _id: '$_id.hour',
              actions: {
                $push: {
                  type: '$_id.actionType',
                  count: '$count'
                }
              },
              totalRequests: { $sum: '$count' }
            }
          },
          { $sort: { _id: 1 } }
        ]),

        // 存储指标
        Music.aggregate([
          {
            $group: {
              _id: null,
              totalFiles: { $sum: 1 },
              totalSize: { $sum: '$fileSize' },
              avgFileSize: { $avg: '$fileSize' },
              qualityDistribution: {
                $push: {
                  quality: '$quality',
                  size: '$fileSize'
                }
              }
            }
          }
        ]),

        // 用户活跃度指标
        UserBehavior.aggregate([
          {
            $match: {
              createdAt: { $gte: startDate }
            }
          },
          {
            $group: {
              _id: '$userId',
              actionCount: { $sum: 1 },
              lastActivity: { $max: '$createdAt' }
            }
          },
          {
            $group: {
              _id: null,
              activeUsers: { $sum: 1 },
              avgActionsPerUser: { $avg: '$actionCount' },
              highActivityUsers: {
                $sum: { $cond: [{ $gte: ['$actionCount', 10] }, 1, 0] }
              }
            }
          }
        ]),

        // 错误指标（这里模拟，实际应该从日志系统获取）
        Promise.resolve({
          errorRate: 0.02,
          commonErrors: [
            { type: 'FileNotFound', count: 5 },
            { type: 'ValidationError', count: 3 },
            { type: 'AuthenticationError', count: 2 }
          ]
        })
      ]);

      return {
        period,
        timestamp: now,
        api: {
          hourlyRequests: apiMetrics,
          totalRequests: apiMetrics.reduce((sum, hour) => sum + hour.totalRequests, 0)
        },
        storage: storageMetrics[0] || {},
        users: userMetrics[0] || {},
        errors: errorMetrics
      };
    } catch (error) {
      throw new Error(`Failed to get system metrics: ${error.message}`);
    }
  }

  /**
   * 生成统计报告
   * @param {Object} options - 报告选项
   * @returns {Promise<Object>} 统计报告
   */
  static async generateStatisticsReport(options = {}) {
    try {
      const {
        reportType = 'comprehensive',
        period = '30d',
        format = 'json',
        includeCharts = false
      } = options;

      let report = {
        metadata: {
          reportType,
          period,
          generatedAt: new Date(),
          format
        },
        sections: {}
      };

      // 根据报告类型包含不同的部分
      if (reportType === 'comprehensive' || reportType === 'overview') {
        report.sections.overview = await this.getDetailedMusicStats(period, 'day');
      }

      if (reportType === 'comprehensive' || reportType === 'user-behavior') {
        report.sections.userBehavior = await this.getUserBehaviorAnalysis(period, 'overview');
      }

      if (reportType === 'comprehensive' || reportType === 'trends') {
        report.sections.trends = await this.getMusicTrendAnalysis(period, 'all');
      }

      if (reportType === 'comprehensive' || reportType === 'genres') {
        report.sections.genres = await this.getGenreAnalysis(period, 20);
      }

      if (reportType === 'comprehensive' || reportType === 'artists') {
        report.sections.artists = await this.getArtistRanking(period, 'playCount', 30);
      }

      if (reportType === 'comprehensive' || reportType === 'system') {
        report.sections.system = await this.getSystemMetrics('24h');
      }

      // 添加摘要
      report.summary = this.generateReportSummary(report.sections);

      // 如果需要图表数据
      if (includeCharts) {
        report.charts = this.generateChartData(report.sections);
      }

      return report;
    } catch (error) {
      throw new Error(`Failed to generate statistics report: ${error.message}`);
    }
  }

  /**
   * 生成报告摘要
   * @param {Object} sections - 报告各部分
   * @returns {Object} 报告摘要
   */
  static generateReportSummary(sections) {
    const summary = {
      keyMetrics: {},
      insights: [],
      recommendations: []
    };

    // 提取关键指标
    if (sections.overview) {
      const overview = sections.overview.overall;
      summary.keyMetrics = {
        totalMusic: overview.totalMusic || 0,
        totalPlays: overview.totalPlayCount || 0,
        totalDownloads: overview.totalDownloadCount || 0,
        avgDuration: Math.round(overview.avgDuration || 0),
        avgBitrate: Math.round(overview.avgBitrate || 0)
      };
    }

    // 生成洞察
    if (sections.genres && sections.genres.stats.length > 0) {
      const topGenre = sections.genres.stats[0];
      summary.insights.push(`最受欢迎的音乐流派是 ${topGenre._id}，共有 ${topGenre.totalMusic} 首音乐`);
    }

    if (sections.artists && sections.artists.length > 0) {
      const topArtist = sections.artists[0];
      summary.insights.push(`最受欢迎的艺术家是 ${topArtist.artist}，总播放量达到 ${topArtist.totalPlays}`);
    }

    // 生成建议
    if (sections.userBehavior && sections.userBehavior.overview) {
      const engagement = sections.userBehavior.overview.userEngagement;
      if (engagement.avgActionsPerUser < 10) {
        summary.recommendations.push('用户参与度较低，建议优化推荐算法和用户体验');
      }
    }

    return summary;
  }

  /**
   * 生成图表数据
   * @param {Object} sections - 报告各部分
   * @returns {Object} 图表数据
   */
  static generateChartData(sections) {
    const charts = {};

    // 时间序列图表
    if (sections.overview && sections.overview.timeSeries) {
      charts.timeSeriesChart = {
        type: 'line',
        data: sections.overview.timeSeries.map(item => ({
          date: item._id,
          uploads: item.count,
          plays: item.totalPlays
        }))
      };
    }

    // 流派分布饼图
    if (sections.genres && sections.genres.stats) {
      charts.genreDistributionChart = {
        type: 'pie',
        data: sections.genres.stats.slice(0, 10).map(item => ({
          label: item._id,
          value: item.totalMusic
        }))
      };
    }

    // 艺术家排行柱状图
    if (sections.artists) {
      charts.artistRankingChart = {
        type: 'bar',
        data: sections.artists.slice(0, 15).map(item => ({
          label: item.artist,
          value: item.totalPlays
        }))
      };
    }

    return charts;
  }
}

module.exports = MusicService;
