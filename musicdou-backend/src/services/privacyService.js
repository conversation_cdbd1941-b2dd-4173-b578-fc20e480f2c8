const Activity = require('../models/Activity');
const Follow = require('../models/Follow');
const User = require('../models/User');

/**
 * 隐私控制服务
 * 处理动态的隐私权限控制和访问验证
 */
class PrivacyService {

  /**
   * 检查用户是否可以查看动态
   */
  static async canViewActivity(activityId, viewerId = null) {
    try {
      const activity = await Activity.findById(activityId)
        .populate('user', '_id username')
        .lean();

      if (!activity) {
        return { canView: false, reason: 'Activity not found' };
      }

      return await this.checkActivityAccess(activity, viewerId);
    } catch (error) {
      console.error('Error checking activity view permission:', error);
      return { canView: false, reason: 'Permission check failed' };
    }
  }

  /**
   * 检查动态访问权限
   */
  static async checkActivityAccess(activity, viewerId = null) {
    try {
      // 如果动态已删除或隐藏，只有作者可以查看
      if (activity.status !== 'active') {
        if (!viewerId || activity.user._id.toString() !== viewerId) {
          return { canView: false, reason: 'Activity not available' };
        }
        return { canView: true, reason: 'Owner access' };
      }

      // 根据隐私级别检查权限
      switch (activity.privacy) {
        case 'public':
          return { canView: true, reason: 'Public activity' };

        case 'private':
          if (!viewerId || activity.user._id.toString() !== viewerId) {
            return { canView: false, reason: 'Private activity' };
          }
          return { canView: true, reason: 'Owner access' };

        case 'followers':
          if (!viewerId) {
            return { canView: false, reason: 'Login required' };
          }
          
          if (activity.user._id.toString() === viewerId) {
            return { canView: true, reason: 'Owner access' };
          }

          // 检查是否为关注者
          const isFollowing = await Follow.findOne({
            follower: viewerId,
            following: activity.user._id,
            status: 'active'
          });

          if (isFollowing) {
            return { canView: true, reason: 'Follower access' };
          }

          return { canView: false, reason: 'Followers only' };

        default:
          return { canView: false, reason: 'Invalid privacy setting' };
      }
    } catch (error) {
      console.error('Error checking activity access:', error);
      return { canView: false, reason: 'Access check failed' };
    }
  }

  /**
   * 批量检查动态访问权限
   */
  static async batchCheckActivityAccess(activities, viewerId = null) {
    try {
      const results = [];
      
      // 如果有查看者，预先获取其关注列表以优化性能
      let followingIds = [];
      if (viewerId) {
        const follows = await Follow.find({
          follower: viewerId,
          status: 'active'
        }).distinct('following');
        followingIds = follows.map(id => id.toString());
      }

      for (const activity of activities) {
        const result = await this.checkActivityAccessOptimized(
          activity, 
          viewerId, 
          followingIds
        );
        results.push({
          activityId: activity._id,
          canView: result.canView,
          reason: result.reason
        });
      }

      return results;
    } catch (error) {
      console.error('Error batch checking activity access:', error);
      return activities.map(activity => ({
        activityId: activity._id,
        canView: false,
        reason: 'Batch check failed'
      }));
    }
  }

  /**
   * 优化的动态访问权限检查（用于批量操作）
   */
  static async checkActivityAccessOptimized(activity, viewerId = null, followingIds = []) {
    try {
      // 如果动态已删除或隐藏，只有作者可以查看
      if (activity.status !== 'active') {
        if (!viewerId || activity.user._id.toString() !== viewerId) {
          return { canView: false, reason: 'Activity not available' };
        }
        return { canView: true, reason: 'Owner access' };
      }

      // 根据隐私级别检查权限
      switch (activity.privacy) {
        case 'public':
          return { canView: true, reason: 'Public activity' };

        case 'private':
          if (!viewerId || activity.user._id.toString() !== viewerId) {
            return { canView: false, reason: 'Private activity' };
          }
          return { canView: true, reason: 'Owner access' };

        case 'followers':
          if (!viewerId) {
            return { canView: false, reason: 'Login required' };
          }
          
          if (activity.user._id.toString() === viewerId) {
            return { canView: true, reason: 'Owner access' };
          }

          // 使用预先获取的关注列表检查
          if (followingIds.includes(activity.user._id.toString())) {
            return { canView: true, reason: 'Follower access' };
          }

          return { canView: false, reason: 'Followers only' };

        default:
          return { canView: false, reason: 'Invalid privacy setting' };
      }
    } catch (error) {
      console.error('Error checking optimized activity access:', error);
      return { canView: false, reason: 'Access check failed' };
    }
  }

  /**
   * 过滤用户可见的动态列表
   */
  static async filterVisibleActivities(activities, viewerId = null) {
    try {
      const accessResults = await this.batchCheckActivityAccess(activities, viewerId);
      
      return activities.filter((activity, index) => {
        return accessResults[index]?.canView || false;
      });
    } catch (error) {
      console.error('Error filtering visible activities:', error);
      return [];
    }
  }

  /**
   * 获取用户隐私设置
   */
  static async getUserPrivacySettings(userId) {
    try {
      const user = await User.findById(userId).lean();
      if (!user) {
        throw new Error('User not found');
      }

      // 默认隐私设置
      const defaultSettings = {
        defaultActivityPrivacy: 'public',
        allowFollowersToSeeActivities: true,
        allowPublicToSeeProfile: true,
        allowFollowersToSeeFollowing: true,
        allowFollowersToSeeFollowers: true,
        autoGenerateActivities: {
          uploadMusic: true,
          createPlaylist: true,
          followUser: true,
          likeMusic: false,
          commentMusic: true,
          shareMusic: true,
          favoritePlaylist: false
        }
      };

      // 如果用户有自定义隐私设置，合并设置
      return {
        ...defaultSettings,
        ...(user.privacySettings || {})
      };
    } catch (error) {
      console.error('Error getting user privacy settings:', error);
      throw error;
    }
  }

  /**
   * 更新用户隐私设置
   */
  static async updateUserPrivacySettings(userId, settings) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // 验证隐私设置
      const validatedSettings = this.validatePrivacySettings(settings);

      // 更新用户隐私设置
      user.privacySettings = {
        ...user.privacySettings,
        ...validatedSettings
      };

      await user.save();

      return user.privacySettings;
    } catch (error) {
      console.error('Error updating user privacy settings:', error);
      throw error;
    }
  }

  /**
   * 验证隐私设置
   */
  static validatePrivacySettings(settings) {
    const validatedSettings = {};

    // 验证默认动态隐私级别
    if (settings.defaultActivityPrivacy) {
      if (['public', 'followers', 'private'].includes(settings.defaultActivityPrivacy)) {
        validatedSettings.defaultActivityPrivacy = settings.defaultActivityPrivacy;
      }
    }

    // 验证布尔值设置
    const booleanSettings = [
      'allowFollowersToSeeActivities',
      'allowPublicToSeeProfile',
      'allowFollowersToSeeFollowing',
      'allowFollowersToSeeFollowers'
    ];

    booleanSettings.forEach(setting => {
      if (typeof settings[setting] === 'boolean') {
        validatedSettings[setting] = settings[setting];
      }
    });

    // 验证自动生成动态设置
    if (settings.autoGenerateActivities && typeof settings.autoGenerateActivities === 'object') {
      validatedSettings.autoGenerateActivities = {};
      
      const activityTypes = [
        'uploadMusic',
        'createPlaylist',
        'followUser',
        'likeMusic',
        'commentMusic',
        'shareMusic',
        'favoritePlaylist'
      ];

      activityTypes.forEach(type => {
        if (typeof settings.autoGenerateActivities[type] === 'boolean') {
          validatedSettings.autoGenerateActivities[type] = settings.autoGenerateActivities[type];
        }
      });
    }

    return validatedSettings;
  }

  /**
   * 检查是否应该自动生成动态
   */
  static async shouldAutoGenerateActivity(userId, activityType) {
    try {
      const privacySettings = await this.getUserPrivacySettings(userId);
      
      return privacySettings.autoGenerateActivities?.[activityType] !== false;
    } catch (error) {
      console.error('Error checking auto generate activity:', error);
      return true; // 默认允许生成
    }
  }

  /**
   * 获取用户默认动态隐私级别
   */
  static async getUserDefaultActivityPrivacy(userId) {
    try {
      const privacySettings = await this.getUserPrivacySettings(userId);
      return privacySettings.defaultActivityPrivacy || 'public';
    } catch (error) {
      console.error('Error getting user default activity privacy:', error);
      return 'public';
    }
  }

  /**
   * 批量更新动态隐私级别
   */
  static async batchUpdateActivityPrivacy(userId, activityIds, privacy) {
    try {
      if (!['public', 'followers', 'private'].includes(privacy)) {
        throw new Error('Invalid privacy level');
      }

      const result = await Activity.updateMany(
        {
          _id: { $in: activityIds },
          user: userId,
          status: 'active'
        },
        {
          $set: { privacy }
        }
      );

      return {
        matchedCount: result.matchedCount,
        modifiedCount: result.modifiedCount,
        privacy
      };
    } catch (error) {
      console.error('Error batch updating activity privacy:', error);
      throw error;
    }
  }

  /**
   * 获取隐私级别说明
   */
  static getPrivacyLevelDescriptions() {
    return {
      public: {
        name: '公开',
        description: '所有人都可以查看此动态',
        icon: '🌍'
      },
      followers: {
        name: '仅关注者',
        description: '只有关注你的用户可以查看此动态',
        icon: '👥'
      },
      private: {
        name: '私密',
        description: '只有你自己可以查看此动态',
        icon: '🔒'
      }
    };
  }
}

module.exports = PrivacyService;
