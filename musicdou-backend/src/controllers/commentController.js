const Comment = require('../models/Comment');
const Music = require('../models/Music');
const User = require('../models/User');

/**
 * 发布评论
 * POST /api/v1/comments
 */
const createComment = async (req, res) => {
  try {
    const {
      content,
      musicId,
      parentId = null,
      replyToUserId = null
    } = req.body;

    const userId = req.user.userId;

    // 验证音乐是否存在
    const music = await Music.findById(musicId);
    if (!music) {
      return res.status(404).json({
        success: false,
        message: 'Music not found'
      });
    }

    // 如果是回复，验证父评论是否存在
    let parentComment = null;
    let level = 0;
    let rootId = null;

    if (parentId) {
      parentComment = await Comment.findById(parentId);
      if (!parentComment) {
        return res.status(404).json({
          success: false,
          message: 'Parent comment not found'
        });
      }

      // 检查回复层级限制
      if (parentComment.level >= 5) {
        return res.status(400).json({
          success: false,
          message: 'Maximum reply depth exceeded'
        });
      }

      level = parentComment.level + 1;
      rootId = parentComment.rootId || parentComment._id;
    }

    // 验证回复目标用户
    if (replyToUserId) {
      const replyToUser = await User.findById(replyToUserId);
      if (!replyToUser) {
        return res.status(404).json({
          success: false,
          message: 'Reply target user not found'
        });
      }
    }

    // 创建评论
    const comment = new Comment({
      content,
      author: userId,
      musicId,
      parentId,
      rootId,
      level,
      replyToUser: replyToUserId,
      metadata: {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        isMobile: /Mobile|Android|iPhone|iPad/.test(req.get('User-Agent'))
      }
    });

    await comment.save();

    // 如果是回复，更新父评论的回复数
    if (parentComment) {
      await parentComment.incrementReplies();
    }

    // 填充关联数据
    await comment.populate([
      { path: 'author', select: 'username avatar userGroup' },
      { path: 'replyToUser', select: 'username' }
    ]);

    res.status(201).json({
      success: true,
      message: 'Comment created successfully',
      data: comment
    });
  } catch (error) {
    console.error('Create comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create comment',
      error: error.message
    });
  }
};

/**
 * 获取音乐评论列表
 * GET /api/v1/comments/music/:musicId
 */
const getMusicComments = async (req, res) => {
  try {
    const { musicId } = req.params;
    const {
      page = 1,
      limit = 20,
      sortBy = 'newest',
      includeReplies = false
    } = req.query;

    // 验证音乐是否存在
    const music = await Music.findById(musicId);
    if (!music) {
      return res.status(404).json({
        success: false,
        message: 'Music not found'
      });
    }

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      includeReplies: includeReplies === 'true'
    };

    const comments = await Comment.getMusicComments(musicId, options);
    
    // 获取总数
    const total = await Comment.countDocuments({
      musicId,
      status: 'approved',
      level: includeReplies ? { $gte: 0 } : 0
    });

    res.status(200).json({
      success: true,
      message: 'Comments retrieved successfully',
      data: {
        comments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get music comments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get comments',
      error: error.message
    });
  }
};

/**
 * 获取评论回复列表
 * GET /api/v1/comments/:commentId/replies
 */
const getCommentReplies = async (req, res) => {
  try {
    const { commentId } = req.params;
    const {
      page = 1,
      limit = 10,
      sortBy = 'oldest'
    } = req.query;

    // 验证评论是否存在
    const comment = await Comment.findById(commentId);
    if (!comment) {
      return res.status(404).json({
        success: false,
        message: 'Comment not found'
      });
    }

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy
    };

    const replies = await Comment.getCommentReplies(commentId, options);
    
    // 获取回复总数
    const total = await Comment.countDocuments({
      $or: [
        { parentId: commentId },
        { rootId: commentId }
      ],
      status: 'approved'
    });

    res.status(200).json({
      success: true,
      message: 'Replies retrieved successfully',
      data: {
        replies,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get comment replies error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get replies',
      error: error.message
    });
  }
};

/**
 * 获取评论详情
 * GET /api/v1/comments/:id
 */
const getCommentById = async (req, res) => {
  try {
    const { id } = req.params;

    const comment = await Comment.findById(id)
      .populate('author', 'username avatar userGroup')
      .populate('replyToUser', 'username')
      .populate('musicId', 'title artist');

    if (!comment) {
      return res.status(404).json({
        success: false,
        message: 'Comment not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Comment retrieved successfully',
      data: comment
    });
  } catch (error) {
    console.error('Get comment by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get comment',
      error: error.message
    });
  }
};

/**
 * 编辑评论
 * PUT /api/v1/comments/:id
 */
const updateComment = async (req, res) => {
  try {
    const { id } = req.params;
    const { content, reason = '' } = req.body;
    const userId = req.user.userId;

    const comment = await Comment.findById(id);
    if (!comment) {
      return res.status(404).json({
        success: false,
        message: 'Comment not found'
      });
    }

    // 检查权限：只有评论作者可以编辑
    if (comment.author.toString() !== userId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only edit your own comments'
      });
    }

    // 检查是否可以编辑
    if (!comment.canEdit) {
      return res.status(400).json({
        success: false,
        message: 'Comment can no longer be edited'
      });
    }

    await comment.editContent(content, reason);
    
    // 重新填充关联数据
    await comment.populate([
      { path: 'author', select: 'username avatar userGroup' },
      { path: 'replyToUser', select: 'username' }
    ]);

    res.status(200).json({
      success: true,
      message: 'Comment updated successfully',
      data: comment
    });
  } catch (error) {
    console.error('Update comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update comment',
      error: error.message
    });
  }
};

/**
 * 删除评论
 * DELETE /api/v1/comments/:id
 */
const deleteComment = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.userId;
    const userGroup = req.user.userGroup;

    const comment = await Comment.findById(id);
    if (!comment) {
      return res.status(404).json({
        success: false,
        message: 'Comment not found'
      });
    }

    // 检查权限：评论作者或管理员可以删除
    if (comment.author.toString() !== userId.toString() && userGroup !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'You can only delete your own comments'
      });
    }

    // 软删除评论
    await comment.softDelete();

    res.status(200).json({
      success: true,
      message: 'Comment deleted successfully'
    });
  } catch (error) {
    console.error('Delete comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete comment',
      error: error.message
    });
  }
};

/**
 * 点赞评论
 * POST /api/v1/comments/:id/like
 */
const likeComment = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.userId;

    const comment = await Comment.findById(id);
    if (!comment) {
      return res.status(404).json({
        success: false,
        message: 'Comment not found'
      });
    }

    // 这里应该检查用户是否已经点赞过
    // 为了简化，我们先直接增加点赞数
    // 实际项目中需要创建一个Like模型来记录点赞关系
    await comment.incrementLikes();

    res.status(200).json({
      success: true,
      message: 'Comment liked successfully',
      data: {
        likesCount: comment.stats.likesCount
      }
    });
  } catch (error) {
    console.error('Like comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to like comment',
      error: error.message
    });
  }
};

/**
 * 取消点赞评论
 * DELETE /api/v1/comments/:id/like
 */
const unlikeComment = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.userId;

    const comment = await Comment.findById(id);
    if (!comment) {
      return res.status(404).json({
        success: false,
        message: 'Comment not found'
      });
    }

    await comment.decrementLikes();

    res.status(200).json({
      success: true,
      message: 'Comment unliked successfully',
      data: {
        likesCount: comment.stats.likesCount
      }
    });
  } catch (error) {
    console.error('Unlike comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to unlike comment',
      error: error.message
    });
  }
};

/**
 * 举报评论
 * POST /api/v1/comments/:id/report
 */
const reportComment = async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const userId = req.user.userId;

    const comment = await Comment.findById(id);
    if (!comment) {
      return res.status(404).json({
        success: false,
        message: 'Comment not found'
      });
    }

    // 不能举报自己的评论
    if (comment.author.toString() === userId.toString()) {
      return res.status(400).json({
        success: false,
        message: 'You cannot report your own comment'
      });
    }

    await comment.incrementReports();

    // 这里应该创建举报记录
    // 实际项目中需要创建一个Report模型来记录举报详情

    res.status(200).json({
      success: true,
      message: 'Comment reported successfully'
    });
  } catch (error) {
    console.error('Report comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to report comment',
      error: error.message
    });
  }
};

/**
 * 获取用户评论历史
 * GET /api/v1/comments/user/:userId
 */
const getUserComments = async (req, res) => {
  try {
    const { userId } = req.params;
    const {
      page = 1,
      limit = 20
    } = req.query;

    // 验证用户是否存在
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const options = {
      page: parseInt(page),
      limit: parseInt(limit)
    };

    const comments = await Comment.getUserComments(userId, options);

    // 获取总数
    const total = await Comment.countDocuments({
      author: userId,
      status: 'approved'
    });

    res.status(200).json({
      success: true,
      message: 'User comments retrieved successfully',
      data: {
        comments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get user comments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user comments',
      error: error.message
    });
  }
};

/**
 * 获取热门评论
 * GET /api/v1/comments/hot
 */
const getHotComments = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      musicId = null,
      timeRange = '7d'
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      musicId,
      timeRange
    };

    const comments = await Comment.getHotComments(options);

    // 获取总数
    let countQuery = { status: 'approved', isHot: true };
    if (musicId) {
      countQuery.musicId = musicId;
    }
    if (timeRange !== 'all') {
      const days = parseInt(timeRange);
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      countQuery.createdAt = { $gte: startDate };
    }

    const total = await Comment.countDocuments(countQuery);

    res.status(200).json({
      success: true,
      message: 'Hot comments retrieved successfully',
      data: {
        comments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get hot comments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get hot comments',
      error: error.message
    });
  }
};

/**
 * 搜索评论
 * GET /api/v1/comments/search
 */
const searchComments = async (req, res) => {
  try {
    const {
      keyword,
      page = 1,
      limit = 20,
      musicId = null
    } = req.query;

    if (!keyword) {
      return res.status(400).json({
        success: false,
        message: 'Search keyword is required'
      });
    }

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      musicId
    };

    const comments = await Comment.searchComments(keyword, options);

    // 获取总数
    let countQuery = {
      content: { $regex: keyword, $options: 'i' },
      status: 'approved'
    };
    if (musicId) {
      countQuery.musicId = musicId;
    }

    const total = await Comment.countDocuments(countQuery);

    res.status(200).json({
      success: true,
      message: 'Comments search completed',
      data: {
        comments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Search comments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search comments',
      error: error.message
    });
  }
};

/**
 * 获取待审核评论列表（管理员功能）
 * GET /api/v1/comments/admin/pending
 */
const getPendingComments = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      sortBy = 'newest'
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy
    };

    const comments = await Comment.getPendingComments(options);

    // 获取总数
    const total = await Comment.countDocuments({ status: 'pending' });

    res.status(200).json({
      success: true,
      message: 'Pending comments retrieved successfully',
      data: {
        comments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get pending comments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get pending comments',
      error: error.message
    });
  }
};

/**
 * 审核评论（管理员功能）
 * PUT /api/v1/comments/:id/moderate
 */
const moderateComment = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, reason = '' } = req.body;
    const reviewerId = req.user.userId;

    if (!['approved', 'rejected', 'hidden'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be approved, rejected, or hidden'
      });
    }

    const comment = await Comment.findById(id);
    if (!comment) {
      return res.status(404).json({
        success: false,
        message: 'Comment not found'
      });
    }

    await comment.moderate(status, reviewerId, reason);

    res.status(200).json({
      success: true,
      message: 'Comment moderated successfully',
      data: comment
    });
  } catch (error) {
    console.error('Moderate comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to moderate comment',
      error: error.message
    });
  }
};

/**
 * 批量审核评论（管理员功能）
 * PUT /api/v1/comments/admin/batch-moderate
 */
const batchModerateComments = async (req, res) => {
  try {
    const { commentIds, status, reason = '' } = req.body;
    const reviewerId = req.user.userId;

    if (!Array.isArray(commentIds) || commentIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Comment IDs array is required'
      });
    }

    if (!['approved', 'rejected', 'hidden'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be approved, rejected, or hidden'
      });
    }

    const result = await Comment.batchModerate(commentIds, status, reviewerId, reason);

    res.status(200).json({
      success: true,
      message: 'Comments moderated successfully',
      data: {
        modifiedCount: result.modifiedCount
      }
    });
  } catch (error) {
    console.error('Batch moderate comments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to batch moderate comments',
      error: error.message
    });
  }
};

/**
 * 获取评论统计信息
 * GET /api/v1/comments/stats
 */
const getCommentStats = async (req, res) => {
  try {
    const { musicId = null } = req.query;

    const stats = await Comment.getCommentStats(musicId);

    res.status(200).json({
      success: true,
      message: 'Comment statistics retrieved successfully',
      data: stats[0] || {
        totalComments: 0,
        approvedComments: 0,
        pendingComments: 0,
        rejectedComments: 0,
        totalLikes: 0,
        totalReplies: 0,
        totalReports: 0,
        avgLikesPerComment: 0,
        avgRepliesPerComment: 0
      }
    });
  } catch (error) {
    console.error('Get comment stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get comment statistics',
      error: error.message
    });
  }
};

module.exports = {
  createComment,
  getMusicComments,
  getCommentReplies,
  getCommentById,
  updateComment,
  deleteComment,
  likeComment,
  unlikeComment,
  reportComment,
  getUserComments,
  getHotComments,
  searchComments,
  getPendingComments,
  moderateComment,
  batchModerateComments,
  getCommentStats
};
