const Music = require('../models/Music');
const Playlist = require('../models/Playlist');
const Comment = require('../models/Comment');

/**
 * 分享控制器
 * 处理音乐、歌单、评论的分享功能
 */

/**
 * 生成分享链接
 * POST /api/v1/shares/generate
 */
const generateShareLink = async (req, res) => {
  try {
    const { targetType, targetId, platform = 'web' } = req.body;
    const userId = req.user._id;

    // 验证目标类型
    if (!['music', 'playlist', 'comment'].includes(targetType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid target type'
      });
    }

    // 验证目标是否存在
    let target;
    switch (targetType) {
      case 'music':
        target = await Music.findById(targetId).select('title artist album coverImage');
        break;
      case 'playlist':
        target = await Playlist.findById(targetId).select('name description coverImage isPublic');
        // 检查歌单是否公开
        if (!target.isPublic) {
          return res.status(403).json({
            success: false,
            message: 'Cannot share private playlist'
          });
        }
        break;
      case 'comment':
        target = await Comment.findById(targetId)
          .select('content author musicId')
          .populate('author', 'username')
          .populate('musicId', 'title artist');
        break;
    }

    if (!target) {
      return res.status(404).json({
        success: false,
        message: `${targetType} not found`
      });
    }

    // 生成分享链接
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const shareUrl = `${baseUrl}/share/${targetType}/${targetId}`;

    // 生成分享内容
    const shareContent = generateShareContent(targetType, target, shareUrl);

    // 记录分享统计
    await recordShareStats(targetType, targetId, userId, platform);

    res.json({
      success: true,
      data: {
        shareUrl,
        shareContent,
        target
      }
    });

  } catch (error) {
    console.error('Generate share link error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate share link',
      error: error.message
    });
  }
};

/**
 * 获取分享内容详情
 * GET /api/v1/shares/:targetType/:targetId
 */
const getShareContent = async (req, res) => {
  try {
    const { targetType, targetId } = req.params;

    // 验证目标类型
    if (!['music', 'playlist', 'comment'].includes(targetType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid target type'
      });
    }

    let target;
    let shareData = {};

    switch (targetType) {
      case 'music':
        target = await Music.findById(targetId)
          .select('title artist album coverImage duration genre year stats')
          .populate('uploadedBy', 'username avatar');
        
        if (target) {
          shareData = {
            type: 'music',
            title: target.title,
            description: `${target.artist} - ${target.album || 'Unknown Album'}`,
            image: target.coverImage,
            url: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/music/${targetId}`,
            metadata: {
              artist: target.artist,
              album: target.album,
              duration: target.duration,
              genre: target.genre,
              year: target.year,
              playCount: target.stats?.playCount || 0,
              likesCount: target.stats?.likesCount || 0
            }
          };
        }
        break;

      case 'playlist':
        target = await Playlist.findById(targetId)
          .select('name description coverImage isPublic songsCount stats createdBy')
          .populate('createdBy', 'username avatar');
        
        if (target && target.isPublic) {
          shareData = {
            type: 'playlist',
            title: target.name,
            description: target.description || `Playlist by ${target.createdBy?.username}`,
            image: target.coverImage,
            url: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/playlist/${targetId}`,
            metadata: {
              songsCount: target.songsCount || 0,
              creator: target.createdBy?.username,
              playCount: target.stats?.playCount || 0,
              likesCount: target.stats?.likesCount || 0
            }
          };
        }
        break;

      case 'comment':
        target = await Comment.findById(targetId)
          .select('content author musicId createdAt stats')
          .populate('author', 'username avatar')
          .populate('musicId', 'title artist');
        
        if (target) {
          shareData = {
            type: 'comment',
            title: `Comment on ${target.musicId?.title}`,
            description: target.content.substring(0, 100) + (target.content.length > 100 ? '...' : ''),
            image: target.author?.avatar,
            url: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/music/${target.musicId?._id}#comment-${targetId}`,
            metadata: {
              author: target.author?.username,
              musicTitle: target.musicId?.title,
              musicArtist: target.musicId?.artist,
              createdAt: target.createdAt,
              likesCount: target.stats?.likesCount || 0
            }
          };
        }
        break;
    }

    if (!target) {
      return res.status(404).json({
        success: false,
        message: `${targetType} not found`
      });
    }

    // 如果是歌单且不公开，返回错误
    if (targetType === 'playlist' && !target.isPublic) {
      return res.status(403).json({
        success: false,
        message: 'This playlist is private'
      });
    }

    res.json({
      success: true,
      data: shareData
    });

  } catch (error) {
    console.error('Get share content error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get share content',
      error: error.message
    });
  }
};

/**
 * 获取分享统计
 * GET /api/v1/shares/stats/:targetType/:targetId
 */
const getShareStats = async (req, res) => {
  try {
    const { targetType, targetId } = req.params;

    let target;
    let shareStats = {};

    switch (targetType) {
      case 'music':
        target = await Music.findById(targetId).select('stats');
        shareStats = {
          shareCount: target?.stats?.shareCount || 0
        };
        break;
      case 'playlist':
        target = await Playlist.findById(targetId).select('stats');
        shareStats = {
          shareCount: target?.stats?.shareCount || 0
        };
        break;
      case 'comment':
        target = await Comment.findById(targetId).select('stats');
        shareStats = {
          shareCount: target?.stats?.shareCount || 0
        };
        break;
    }

    if (!target) {
      return res.status(404).json({
        success: false,
        message: `${targetType} not found`
      });
    }

    res.json({
      success: true,
      data: shareStats
    });

  } catch (error) {
    console.error('Get share stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get share stats',
      error: error.message
    });
  }
};

/**
 * 获取用户分享历史
 * GET /api/v1/shares/user/:userId
 */
const getUserShareHistory = async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    // 这里应该从分享记录表中查询，但为了简化，我们返回模拟数据
    // 在实际项目中，应该创建一个ShareRecord模型来记录分享历史
    
    res.json({
      success: true,
      data: {
        shares: [],
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: 0,
          pages: 0
        }
      },
      message: 'Share history feature will be implemented with ShareRecord model'
    });

  } catch (error) {
    console.error('Get user share history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user share history',
      error: error.message
    });
  }
};

/**
 * 生成分享内容
 * @param {String} targetType - 目标类型
 * @param {Object} target - 目标对象
 * @param {String} shareUrl - 分享链接
 */
const generateShareContent = (targetType, target, shareUrl) => {
  let content = {};

  switch (targetType) {
    case 'music':
      content = {
        title: `🎵 ${target.title}`,
        description: `来听听 ${target.artist} 的这首歌吧！`,
        text: `🎵 ${target.title} - ${target.artist}\n\n${shareUrl}`,
        hashtags: ['音乐', 'MusicDou', target.artist.replace(/\s+/g, '')],
        url: shareUrl
      };
      break;

    case 'playlist':
      content = {
        title: `🎶 歌单：${target.name}`,
        description: target.description || `发现一个不错的歌单，快来听听吧！`,
        text: `🎶 歌单：${target.name}\n${target.description || ''}\n\n${shareUrl}`,
        hashtags: ['歌单', 'MusicDou', '音乐分享'],
        url: shareUrl
      };
      break;

    case 'comment':
      content = {
        title: `💬 ${target.author.username} 的评论`,
        description: target.content.substring(0, 100) + (target.content.length > 100 ? '...' : ''),
        text: `💬 ${target.author.username} 对《${target.musicId.title}》的评论：\n"${target.content}"\n\n${shareUrl}`,
        hashtags: ['音乐评论', 'MusicDou'],
        url: shareUrl
      };
      break;
  }

  return content;
};

/**
 * 记录分享统计
 * @param {String} targetType - 目标类型
 * @param {ObjectId} targetId - 目标ID
 * @param {ObjectId} userId - 用户ID
 * @param {String} platform - 分享平台
 */
const recordShareStats = async (targetType, targetId, userId, platform) => {
  try {
    // 更新目标的分享计数
    switch (targetType) {
      case 'music':
        await Music.findByIdAndUpdate(targetId, {
          $inc: { 'stats.shareCount': 1 }
        });
        break;
      case 'playlist':
        await Playlist.findByIdAndUpdate(targetId, {
          $inc: { 'stats.shareCount': 1 }
        });
        break;
      case 'comment':
        await Comment.findByIdAndUpdate(targetId, {
          $inc: { 'stats.shareCount': 1 }
        });
        break;
    }

    // 这里可以创建分享记录到ShareRecord模型
    // const shareRecord = new ShareRecord({
    //   user: userId,
    //   targetType,
    //   targetId,
    //   platform,
    //   createdAt: new Date()
    // });
    // await shareRecord.save();

  } catch (error) {
    console.error('Record share stats error:', error);
  }
};

module.exports = {
  generateShareLink,
  getShareContent,
  getShareStats,
  getUserShareHistory
};
