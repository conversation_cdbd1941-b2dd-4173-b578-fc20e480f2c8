const PlayQueue = require('../models/PlayQueue');
const PlayHistory = require('../models/PlayHistory');
const PlaySession = require('../models/PlaySession');
const Music = require('../models/Music');
const Playlist = require('../models/Playlist');
const UserBehavior = require('../models/UserBehavior');
const mongoose = require('mongoose');

/**
 * 开始播放音乐
 */
const startPlay = async (req, res) => {
  try {
    const { musicId, playlistId, playSource = 'manual', playQuality = 'standard' } = req.body;
    const userId = req.user.userId;
    const sessionId = req.headers['x-session-id'] || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 验证音乐是否存在
    const music = await Music.findById(musicId);
    if (!music) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Music not found'
      });
    }
    
    // 验证音乐状态
    if (music.status !== 'approved') {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Music is not available for playback'
      });
    }
    
    // 获取或创建播放队列
    let playQueue = await PlayQueue.findOne({ userId });
    if (!playQueue) {
      playQueue = new PlayQueue({ userId });
      await playQueue.save();
    }
    
    // 获取或创建播放会话
    let playSession = await PlaySession.getActiveSession(userId);
    if (!playSession) {
      const deviceInfo = req.headers['user-agent'] || 'Unknown Device';
      playSession = await PlaySession.createSession(userId, sessionId, deviceInfo, {
        userAgent: req.headers['user-agent'],
        ipAddress: req.ip
      });
    }
    
    // 创建播放历史记录
    const playHistory = new PlayHistory({
      userId,
      musicId,
      playlistId,
      totalDuration: music.duration || 0,
      playSource,
      playQuality,
      deviceInfo: req.headers['user-agent'],
      ipAddress: req.ip,
      sessionId: playSession.sessionId
    });
    await playHistory.save();
    
    // 更新播放会话
    await playSession.setCurrentMusic(musicId, playlistId);
    await playSession.updatePlayState('playing');
    
    // 更新音乐播放次数
    await Music.findByIdAndUpdate(musicId, { $inc: { playCount: 1 } });
    
    // 记录用户行为
    await UserBehavior.recordBehavior(userId, musicId, 'play', {
      playSource,
      sessionId: playSession.sessionId,
      deviceInfo: req.headers['user-agent'],
      ipAddress: req.ip
    });
    
    // 返回播放信息
    res.json({
      success: true,
      message: 'Playback started successfully',
      data: {
        music,
        playHistory: {
          id: playHistory._id,
          startTime: playHistory.startTime
        },
        session: {
          sessionId: playSession.sessionId,
          playState: playSession.playState
        }
      }
    });
    
  } catch (error) {
    console.error('Start play error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to start playback'
    });
  }
};

/**
 * 暂停播放
 */
const pausePlay = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { currentProgress = 0 } = req.body;
    
    // 获取活跃会话
    const playSession = await PlaySession.getActiveSession(userId);
    if (!playSession) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'No active play session found'
      });
    }
    
    // 更新播放状态
    await playSession.updatePlayState('paused', currentProgress);
    
    res.json({
      success: true,
      message: 'Playback paused successfully',
      data: {
        sessionId: playSession.sessionId,
        playState: 'paused',
        currentProgress
      }
    });
    
  } catch (error) {
    console.error('Pause play error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to pause playback'
    });
  }
};

/**
 * 恢复播放
 */
const resumePlay = async (req, res) => {
  try {
    const userId = req.user.userId;
    
    // 获取活跃会话
    const playSession = await PlaySession.getActiveSession(userId);
    if (!playSession) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'No active play session found'
      });
    }
    
    // 更新播放状态
    await playSession.updatePlayState('playing');
    
    res.json({
      success: true,
      message: 'Playback resumed successfully',
      data: {
        sessionId: playSession.sessionId,
        playState: 'playing',
        currentProgress: playSession.currentProgress
      }
    });
    
  } catch (error) {
    console.error('Resume play error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to resume playback'
    });
  }
};

/**
 * 停止播放
 */
const stopPlay = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { playDuration = 0, playProgress = 0 } = req.body;
    
    // 获取活跃会话
    const playSession = await PlaySession.getActiveSession(userId);
    if (!playSession) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'No active play session found'
      });
    }
    
    // 更新播放状态
    await playSession.updatePlayState('stopped', 0);
    
    // 查找最近的播放历史记录并更新
    if (playSession.currentMusicId) {
      const recentHistory = await PlayHistory.findOne({
        userId,
        musicId: playSession.currentMusicId,
        endTime: null
      }).sort({ startTime: -1 });
      
      if (recentHistory) {
        recentHistory.playDuration = playDuration;
        recentHistory.playProgress = playProgress;
        recentHistory.isCompleted = playProgress >= 80;
        await recentHistory.markCompleted();
        
        // 记录完整播放行为
        if (recentHistory.isCompleted) {
          await UserBehavior.recordBehavior(userId, playSession.currentMusicId, 'complete', {
            playDuration,
            playProgress,
            sessionId: playSession.sessionId
          });
        }
      }
    }
    
    res.json({
      success: true,
      message: 'Playback stopped successfully',
      data: {
        sessionId: playSession.sessionId,
        playState: 'stopped'
      }
    });
    
  } catch (error) {
    console.error('Stop play error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to stop playback'
    });
  }
};

/**
 * 跳过当前歌曲
 */
const skipSong = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { skipTime = 0, playDuration = 0 } = req.body;
    
    // 获取活跃会话
    const playSession = await PlaySession.getActiveSession(userId);
    if (!playSession) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'No active play session found'
      });
    }
    
    // 获取播放队列
    const playQueue = await PlayQueue.findOne({ userId });
    if (!playQueue) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'No play queue found'
      });
    }
    
    // 记录跳过行为
    if (playSession.currentMusicId) {
      const recentHistory = await PlayHistory.findOne({
        userId,
        musicId: playSession.currentMusicId,
        endTime: null
      }).sort({ startTime: -1 });
      
      if (recentHistory) {
        await recentHistory.markSkipped(skipTime);
        
        // 记录跳过行为
        await UserBehavior.recordBehavior(userId, playSession.currentMusicId, 'skip', {
          playDuration,
          skipTime,
          sessionId: playSession.sessionId
        });
      }
    }
    
    // 移动到下一首
    const hasNext = await playQueue.next();
    if (!hasNext) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'No next song available'
      });
    }
    
    // 获取下一首歌曲信息
    const nextSong = playQueue.currentSong;
    if (nextSong) {
      const nextMusic = await Music.findById(nextSong.musicId);
      await playSession.setCurrentMusic(nextSong.musicId);
      
      res.json({
        success: true,
        message: 'Song skipped successfully',
        data: {
          nextMusic,
          currentPosition: playQueue.currentPosition,
          sessionId: playSession.sessionId
        }
      });
    } else {
      res.json({
        success: true,
        message: 'Song skipped, no next song available',
        data: {
          sessionId: playSession.sessionId
        }
      });
    }
    
  } catch (error) {
    console.error('Skip song error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to skip song'
    });
  }
};

/**
 * 播放上一首
 */
const previousSong = async (req, res) => {
  try {
    const userId = req.user.userId;

    // 获取活跃会话
    const playSession = await PlaySession.getActiveSession(userId);
    if (!playSession) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'No active play session found'
      });
    }

    // 获取播放队列
    const playQueue = await PlayQueue.findOne({ userId });
    if (!playQueue) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'No play queue found'
      });
    }

    // 移动到上一首
    const hasPrevious = await playQueue.previous();
    if (!hasPrevious) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'No previous song available'
      });
    }

    // 获取上一首歌曲信息
    const previousSong = playQueue.currentSong;
    if (previousSong) {
      const previousMusic = await Music.findById(previousSong.musicId);
      await playSession.setCurrentMusic(previousSong.musicId);

      res.json({
        success: true,
        message: 'Moved to previous song successfully',
        data: {
          previousMusic,
          currentPosition: playQueue.currentPosition,
          sessionId: playSession.sessionId
        }
      });
    } else {
      res.json({
        success: true,
        message: 'Moved to previous song, no song available',
        data: {
          sessionId: playSession.sessionId
        }
      });
    }

  } catch (error) {
    console.error('Previous song error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to move to previous song'
    });
  }
};

/**
 * 播放下一首
 */
const nextSong = async (req, res) => {
  try {
    const userId = req.user.userId;

    // 获取活跃会话
    const playSession = await PlaySession.getActiveSession(userId);
    if (!playSession) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'No active play session found'
      });
    }

    // 获取播放队列
    const playQueue = await PlayQueue.findOne({ userId });
    if (!playQueue) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'No play queue found'
      });
    }

    // 移动到下一首
    const hasNext = await playQueue.next();
    if (!hasNext) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'No next song available'
      });
    }

    // 获取下一首歌曲信息
    const nextSong = playQueue.currentSong;
    if (nextSong) {
      const nextMusic = await Music.findById(nextSong.musicId);
      await playSession.setCurrentMusic(nextSong.musicId);

      res.json({
        success: true,
        message: 'Moved to next song successfully',
        data: {
          nextMusic,
          currentPosition: playQueue.currentPosition,
          sessionId: playSession.sessionId
        }
      });
    } else {
      res.json({
        success: true,
        message: 'Moved to next song, no song available',
        data: {
          sessionId: playSession.sessionId
        }
      });
    }

  } catch (error) {
    console.error('Next song error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to move to next song'
    });
  }
};

/**
 * 设置播放模式
 */
const setPlayMode = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { playMode } = req.body;

    // 验证播放模式
    const validModes = ['sequential', 'shuffle', 'repeat_one', 'repeat_all'];
    if (!validModes.includes(playMode)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Invalid play mode'
      });
    }

    // 获取播放队列
    const playQueue = await PlayQueue.findOne({ userId });
    if (!playQueue) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'No play queue found'
      });
    }

    // 更新播放模式
    playQueue.playMode = playMode;
    await playQueue.save();

    // 更新会话播放模式
    const playSession = await PlaySession.getActiveSession(userId);
    if (playSession) {
      await playSession.updateSettings({ playMode });
    }

    res.json({
      success: true,
      message: 'Play mode updated successfully',
      data: {
        playMode,
        sessionId: playSession ? playSession.sessionId : null
      }
    });

  } catch (error) {
    console.error('Set play mode error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to set play mode'
    });
  }
};

/**
 * 设置音量
 */
const setVolume = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { volume } = req.body;

    // 验证音量范围
    if (typeof volume !== 'number' || volume < 0 || volume > 100) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Volume must be a number between 0 and 100'
      });
    }

    // 获取播放队列
    const playQueue = await PlayQueue.findOne({ userId });
    if (!playQueue) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'No play queue found'
      });
    }

    // 更新音量
    playQueue.volume = volume;
    await playQueue.save();

    // 更新会话音量
    const playSession = await PlaySession.getActiveSession(userId);
    if (playSession) {
      await playSession.updateSettings({ volume });
    }

    res.json({
      success: true,
      message: 'Volume updated successfully',
      data: {
        volume,
        sessionId: playSession ? playSession.sessionId : null
      }
    });

  } catch (error) {
    console.error('Set volume error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to set volume'
    });
  }
};

/**
 * 获取当前播放状态
 */
const getPlayStatus = async (req, res) => {
  try {
    const userId = req.user.userId;

    // 获取活跃会话
    const playSession = await PlaySession.getActiveSession(userId);
    if (!playSession) {
      return res.json({
        success: true,
        message: 'No active play session',
        data: {
          isPlaying: false,
          currentMusic: null,
          currentPlaylist: null,
          playState: 'stopped'
        }
      });
    }

    // 获取播放队列
    const playQueue = await PlayQueue.findOne({ userId });

    res.json({
      success: true,
      message: 'Play status retrieved successfully',
      data: {
        session: {
          sessionId: playSession.sessionId,
          playState: playSession.playState,
          currentProgress: playSession.currentProgress,
          volume: playSession.volume,
          playMode: playSession.playMode,
          playQuality: playSession.playQuality,
          isMuted: playSession.isMuted
        },
        currentMusic: playSession.currentMusicId,
        currentPlaylist: playSession.currentPlaylistId,
        queue: playQueue ? {
          totalSongs: playQueue.totalSongs,
          currentPosition: playQueue.currentPosition,
          hasNext: playQueue.hasNext,
          hasPrevious: playQueue.hasPrevious
        } : null
      }
    });

  } catch (error) {
    console.error('Get play status error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get play status'
    });
  }
};

/**
 * 更新播放进度（心跳）
 */
const updateProgress = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { currentProgress, isPlaying = true } = req.body;

    // 验证进度值
    if (typeof currentProgress !== 'number' || currentProgress < 0) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Invalid progress value'
      });
    }

    // 获取活跃会话
    const playSession = await PlaySession.getActiveSession(userId);
    if (!playSession) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'No active play session found'
      });
    }

    // 更新播放进度和心跳
    playSession.currentProgress = currentProgress;
    if (isPlaying) {
      playSession.playState = 'playing';
    }
    await playSession.updateHeartbeat();

    res.json({
      success: true,
      message: 'Progress updated successfully',
      data: {
        currentProgress,
        sessionId: playSession.sessionId,
        lastHeartbeat: playSession.lastHeartbeat
      }
    });

  } catch (error) {
    console.error('Update progress error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update progress'
    });
  }
};

/**
 * 跳转到指定位置
 */
const seekTo = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { position } = req.body;

    // 验证位置值
    if (typeof position !== 'number' || position < 0) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Invalid position value'
      });
    }

    // 获取活跃会话
    const playSession = await PlaySession.getActiveSession(userId);
    if (!playSession) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'No active play session found'
      });
    }

    // 更新播放位置
    playSession.currentProgress = position;
    await playSession.updateHeartbeat();

    res.json({
      success: true,
      message: 'Seek completed successfully',
      data: {
        position,
        sessionId: playSession.sessionId
      }
    });

  } catch (error) {
    console.error('Seek to error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to seek to position'
    });
  }
};

/**
 * 设置播放质量
 */
const setPlayQuality = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { playQuality } = req.body;

    // 验证播放质量
    const validQualities = ['standard', 'high', 'super', 'lossless'];
    if (!validQualities.includes(playQuality)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Invalid play quality'
      });
    }

    // 获取播放队列
    const playQueue = await PlayQueue.findOne({ userId });
    if (!playQueue) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'No play queue found'
      });
    }

    // 更新播放质量偏好
    playQueue.preferredQuality = playQuality;
    await playQueue.save();

    // 更新会话播放质量
    const playSession = await PlaySession.getActiveSession(userId);
    if (playSession) {
      await playSession.updateSettings({ playQuality });
    }

    res.json({
      success: true,
      message: 'Play quality updated successfully',
      data: {
        playQuality,
        sessionId: playSession ? playSession.sessionId : null
      }
    });

  } catch (error) {
    console.error('Set play quality error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to set play quality'
    });
  }
};

module.exports = {
  startPlay,
  pausePlay,
  resumePlay,
  stopPlay,
  skipSong,
  previousSong,
  nextSong,
  setPlayMode,
  setVolume,
  getPlayStatus,
  updateProgress,
  seekTo,
  setPlayQuality
};
