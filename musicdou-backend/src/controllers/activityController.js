const Activity = require('../models/Activity');
const ActivityService = require('../services/activityService');
const TimelineService = require('../services/timelineService');
const PrivacyService = require('../services/privacyService');
const PushService = require('../services/pushService');
const { validationResult } = require('express-validator');

/**
 * 动态控制器
 * 处理用户动态相关的API请求
 */
class ActivityController {

  /**
   * 获取用户时间线
   * GET /api/v1/activities/timeline
   */
  static async getTimeline(req, res) {
    try {
      const userId = req.user.id;
      const {
        page = 1,
        limit = 20,
        algorithm = 'hybrid',
        timeRange = 7 * 24,
        includeTypes,
        excludeTypes
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), 50), // 限制最大50条
        algorithm,
        timeRange: parseInt(timeRange),
        includeTypes: includeTypes ? includeTypes.split(',') : null,
        excludeTypes: excludeTypes ? excludeTypes.split(',') : null
      };

      const result = await TimelineService.getPersonalizedTimeline(userId, options);

      res.json({
        success: true,
        message: 'Timeline retrieved successfully',
        data: result
      });
    } catch (error) {
      console.error('Error getting timeline:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get timeline',
        error: error.message
      });
    }
  }

  /**
   * 获取发现时间线
   * GET /api/v1/activities/discover
   */
  static async getDiscoverTimeline(req, res) {
    try {
      const userId = req.user.id;
      const {
        page = 1,
        limit = 20,
        timeRange = 24 * 7
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), 50),
        timeRange: parseInt(timeRange)
      };

      const result = await TimelineService.getDiscoverTimeline(userId, options);

      res.json({
        success: true,
        message: 'Discover timeline retrieved successfully',
        data: result
      });
    } catch (error) {
      console.error('Error getting discover timeline:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get discover timeline',
        error: error.message
      });
    }
  }

  /**
   * 获取话题时间线
   * GET /api/v1/activities/topic/:tag
   */
  static async getTopicTimeline(req, res) {
    try {
      const { tag } = req.params;
      const {
        page = 1,
        limit = 20,
        timeRange = 24 * 30
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), 50),
        timeRange: parseInt(timeRange)
      };

      const result = await TimelineService.getTopicTimeline(tag, options);

      res.json({
        success: true,
        message: 'Topic timeline retrieved successfully',
        data: result
      });
    } catch (error) {
      console.error('Error getting topic timeline:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get topic timeline',
        error: error.message
      });
    }
  }

  /**
   * 获取用户个人时间线
   * GET /api/v1/activities/user/:userId
   */
  static async getUserTimeline(req, res) {
    try {
      const { userId: targetUserId } = req.params;
      const viewerUserId = req.user?.id;
      const {
        page = 1,
        limit = 20
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), 50)
      };

      const result = await TimelineService.getUserTimeline(targetUserId, viewerUserId, options);

      res.json({
        success: true,
        message: 'User timeline retrieved successfully',
        data: result
      });
    } catch (error) {
      console.error('Error getting user timeline:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get user timeline',
        error: error.message
      });
    }
  }

  /**
   * 发布自定义动态
   * POST /api/v1/activities
   */
  static async createActivity(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const userId = req.user.id;
      const {
        title,
        description,
        privacy = 'public',
        tags = [],
        location,
        images = []
      } = req.body;

      const metadata = {
        source: 'web',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        deviceInfo: req.get('User-Agent'),
        isMobile: /Mobile|Android|iPhone|iPad/.test(req.get('User-Agent'))
      };

      const customData = {
        title,
        description,
        privacy,
        tags,
        location,
        images
      };

      const activity = await ActivityService.createCustomActivity(userId, customData, metadata);

      res.status(201).json({
        success: true,
        message: 'Activity created successfully',
        data: activity
      });
    } catch (error) {
      console.error('Error creating activity:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create activity',
        error: error.message
      });
    }
  }

  /**
   * 获取动态详情
   * GET /api/v1/activities/:id
   */
  static async getActivityDetail(req, res) {
    try {
      const { id } = req.params;
      const viewerUserId = req.user?.id;

      const activity = await Activity.findById(id)
        .populate('user', 'username avatar profile.displayName')
        .populate('target.id')
        .lean();

      if (!activity) {
        return res.status(404).json({
          success: false,
          message: 'Activity not found'
        });
      }

      // 检查隐私权限
      if (activity.privacy === 'private' && activity.user._id.toString() !== viewerUserId) {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }

      if (activity.privacy === 'followers' && viewerUserId) {
        const Follow = require('../models/Follow');
        const isFollowing = await Follow.findOne({
          follower: viewerUserId,
          following: activity.user._id,
          status: 'active'
        });

        if (!isFollowing && activity.user._id.toString() !== viewerUserId) {
          return res.status(403).json({
            success: false,
            message: 'Access denied'
          });
        }
      }

      // 增加查看次数
      if (viewerUserId && activity.user._id.toString() !== viewerUserId) {
        await Activity.findByIdAndUpdate(id, {
          $inc: { 'stats.viewCount': 1 }
        });
        activity.stats.viewCount += 1;
      }

      res.json({
        success: true,
        message: 'Activity detail retrieved successfully',
        data: activity
      });
    } catch (error) {
      console.error('Error getting activity detail:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get activity detail',
        error: error.message
      });
    }
  }

  /**
   * 删除动态
   * DELETE /api/v1/activities/:id
   */
  static async deleteActivity(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const activity = await ActivityService.deleteUserActivity(userId, id);

      res.json({
        success: true,
        message: 'Activity deleted successfully',
        data: activity
      });
    } catch (error) {
      console.error('Error deleting activity:', error);
      if (error.message === 'Activity not found or not authorized') {
        return res.status(404).json({
          success: false,
          message: error.message
        });
      }
      res.status(500).json({
        success: false,
        message: 'Failed to delete activity',
        error: error.message
      });
    }
  }

  /**
   * 更新动态隐私设置
   * PUT /api/v1/activities/:id/privacy
   */
  static async updateActivityPrivacy(req, res) {
    try {
      const { id } = req.params;
      const { privacy } = req.body;
      const userId = req.user.id;

      if (!['public', 'followers', 'private'].includes(privacy)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid privacy setting'
        });
      }

      const activity = await ActivityService.updateActivityPrivacy(userId, id, privacy);

      res.json({
        success: true,
        message: 'Activity privacy updated successfully',
        data: activity
      });
    } catch (error) {
      console.error('Error updating activity privacy:', error);
      if (error.message === 'Activity not found or not authorized') {
        return res.status(404).json({
          success: false,
          message: error.message
        });
      }
      res.status(500).json({
        success: false,
        message: 'Failed to update activity privacy',
        error: error.message
      });
    }
  }

  /**
   * 置顶动态
   * PUT /api/v1/activities/:id/pin
   */
  static async pinActivity(req, res) {
    try {
      const { id } = req.params;
      const { duration } = req.body; // 置顶时长（毫秒）
      const userId = req.user.id;

      const activity = await Activity.findOne({
        _id: id,
        user: userId
      });

      if (!activity) {
        return res.status(404).json({
          success: false,
          message: 'Activity not found or not authorized'
        });
      }

      await activity.pin(duration);

      res.json({
        success: true,
        message: 'Activity pinned successfully',
        data: activity
      });
    } catch (error) {
      console.error('Error pinning activity:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to pin activity',
        error: error.message
      });
    }
  }

  /**
   * 取消置顶动态
   * DELETE /api/v1/activities/:id/pin
   */
  static async unpinActivity(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const activity = await Activity.findOne({
        _id: id,
        user: userId
      });

      if (!activity) {
        return res.status(404).json({
          success: false,
          message: 'Activity not found or not authorized'
        });
      }

      await activity.unpin();

      res.json({
        success: true,
        message: 'Activity unpinned successfully',
        data: activity
      });
    } catch (error) {
      console.error('Error unpinning activity:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to unpin activity',
        error: error.message
      });
    }
  }

  /**
   * 点赞动态
   * POST /api/v1/activities/:id/like
   */
  static async likeActivity(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const activity = await Activity.findById(id);
      if (!activity) {
        return res.status(404).json({
          success: false,
          message: 'Activity not found'
        });
      }

      // 检查是否已经点赞
      const Like = require('../models/Like');
      const existingLike = await Like.findOne({
        user: userId,
        targetType: 'activity',
        targetId: id,
        status: 'active'
      });

      if (existingLike) {
        return res.status(400).json({
          success: false,
          message: 'Activity already liked'
        });
      }

      // 创建点赞记录
      const like = new Like({
        user: userId,
        targetType: 'activity',
        targetId: id,
        status: 'active',
        source: 'web',
        metadata: {
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          isMobile: /Mobile|Android|iPhone|iPad/.test(req.get('User-Agent'))
        }
      });

      await like.save();
      await activity.incrementLikeCount();

      res.json({
        success: true,
        message: 'Activity liked successfully',
        data: {
          activityId: id,
          likeCount: activity.stats.likeCount
        }
      });
    } catch (error) {
      console.error('Error liking activity:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to like activity',
        error: error.message
      });
    }
  }

  /**
   * 取消点赞动态
   * DELETE /api/v1/activities/:id/like
   */
  static async unlikeActivity(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const Like = require('../models/Like');
      const like = await Like.findOne({
        user: userId,
        targetType: 'activity',
        targetId: id,
        status: 'active'
      });

      if (!like) {
        return res.status(400).json({
          success: false,
          message: 'Activity not liked'
        });
      }

      await like.cancel();

      const activity = await Activity.findById(id);
      if (activity) {
        await activity.decrementLikeCount();
      }

      res.json({
        success: true,
        message: 'Activity unliked successfully',
        data: {
          activityId: id,
          likeCount: activity ? activity.stats.likeCount : 0
        }
      });
    } catch (error) {
      console.error('Error unliking activity:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to unlike activity',
        error: error.message
      });
    }
  }

  /**
   * 获取时间线统计
   * GET /api/v1/activities/stats
   */
  static async getTimelineStats(req, res) {
    try {
      const userId = req.user.id;
      const { timeRange = 24 * 7 } = req.query;

      const stats = await TimelineService.getTimelineStats(userId, parseInt(timeRange));

      res.json({
        success: true,
        message: 'Timeline stats retrieved successfully',
        data: stats
      });
    } catch (error) {
      console.error('Error getting timeline stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get timeline stats',
        error: error.message
      });
    }
  }

  /**
   * 获取用户动态统计
   * GET /api/v1/activities/user/:userId/stats
   */
  static async getUserActivityStats(req, res) {
    try {
      const { userId } = req.params;
      const { timeRange = 30 } = req.query;

      const stats = await Activity.getUserActivityStats(userId, parseInt(timeRange));

      res.json({
        success: true,
        message: 'User activity stats retrieved successfully',
        data: stats
      });
    } catch (error) {
      console.error('Error getting user activity stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get user activity stats',
        error: error.message
      });
    }
  }

  /**
   * 刷新时间线缓存
   * POST /api/v1/activities/refresh-cache
   */
  static async refreshTimelineCache(req, res) {
    try {
      const userId = req.user.id;

      const result = await TimelineService.refreshTimelineCache(userId);

      res.json({
        success: true,
        message: 'Timeline cache refreshed successfully',
        data: result
      });
    } catch (error) {
      console.error('Error refreshing timeline cache:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to refresh timeline cache',
        error: error.message
      });
    }
  }

  /**
   * 获取推荐算法
   * GET /api/v1/activities/recommended-algorithm
   */
  static async getRecommendedAlgorithm(req, res) {
    try {
      const userId = req.user.id;

      // 获取用户统计信息
      const Follow = require('../models/Follow');
      const followingCount = await Follow.countDocuments({
        follower: userId,
        status: 'active'
      });

      const userStats = {
        followingCount,
        activityLevel: 'medium', // 这里可以根据实际用户行为计算
        interactionRate: 0.1 // 这里可以根据实际互动数据计算
      };

      const recommendedAlgorithm = TimelineService.getRecommendedAlgorithm(userId, userStats);

      res.json({
        success: true,
        message: 'Recommended algorithm retrieved successfully',
        data: {
          algorithm: recommendedAlgorithm,
          userStats,
          availableAlgorithms: [
            {
              name: 'hybrid',
              description: '混合算法：结合个性化、热门和时间顺序'
            },
            {
              name: 'personalized',
              description: '个性化算法：基于关注关系和互动历史'
            },
            {
              name: 'hot',
              description: '热门算法：基于动态热度排序'
            },
            {
              name: 'chronological',
              description: '时间顺序：按发布时间倒序排列'
            }
          ]
        }
      });
    } catch (error) {
      console.error('Error getting recommended algorithm:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get recommended algorithm',
        error: error.message
      });
    }
  }

  /**
   * 获取用户隐私设置
   * GET /api/v1/activities/privacy/settings
   */
  static async getPrivacySettings(req, res) {
    try {
      const userId = req.user.id;

      const settings = await PrivacyService.getUserPrivacySettings(userId);

      res.json({
        success: true,
        message: 'Privacy settings retrieved successfully',
        data: settings
      });
    } catch (error) {
      console.error('Error getting privacy settings:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get privacy settings',
        error: error.message
      });
    }
  }

  /**
   * 更新用户隐私设置
   * PUT /api/v1/activities/privacy/settings
   */
  static async updatePrivacySettings(req, res) {
    try {
      const userId = req.user.id;
      const settings = req.body;

      const updatedSettings = await PrivacyService.updateUserPrivacySettings(userId, settings);

      res.json({
        success: true,
        message: 'Privacy settings updated successfully',
        data: updatedSettings
      });
    } catch (error) {
      console.error('Error updating privacy settings:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update privacy settings',
        error: error.message
      });
    }
  }

  /**
   * 批量更新动态隐私级别
   * PUT /api/v1/activities/privacy/batch
   */
  static async batchUpdateActivityPrivacy(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const userId = req.user.id;
      const { activityIds, privacy } = req.body;

      const result = await PrivacyService.batchUpdateActivityPrivacy(userId, activityIds, privacy);

      res.json({
        success: true,
        message: 'Activity privacy updated successfully',
        data: result
      });
    } catch (error) {
      console.error('Error batch updating activity privacy:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update activity privacy',
        error: error.message
      });
    }
  }

  /**
   * 检查动态访问权限
   * GET /api/v1/activities/:id/access
   */
  static async checkActivityAccess(req, res) {
    try {
      const { id } = req.params;
      const viewerId = req.user?.id;

      const accessResult = await PrivacyService.canViewActivity(id, viewerId);

      res.json({
        success: true,
        message: 'Activity access checked successfully',
        data: accessResult
      });
    } catch (error) {
      console.error('Error checking activity access:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to check activity access',
        error: error.message
      });
    }
  }

  /**
   * 获取隐私级别说明
   * GET /api/v1/activities/privacy/levels
   */
  static async getPrivacyLevels(req, res) {
    try {
      const privacyLevels = PrivacyService.getPrivacyLevelDescriptions();

      res.json({
        success: true,
        message: 'Privacy levels retrieved successfully',
        data: privacyLevels
      });
    } catch (error) {
      console.error('Error getting privacy levels:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get privacy levels',
        error: error.message
      });
    }
  }

  /**
   * 手动推送动态给关注者
   * POST /api/v1/activities/:id/push
   */
  static async pushActivityToFollowers(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      // 验证动态是否属于当前用户
      const activity = await Activity.findOne({
        _id: id,
        user: userId
      });

      if (!activity) {
        return res.status(404).json({
          success: false,
          message: 'Activity not found or not authorized'
        });
      }

      const result = await PushService.pushActivityToFollowers(id);

      res.json({
        success: true,
        message: 'Activity pushed to followers successfully',
        data: result
      });
    } catch (error) {
      console.error('Error pushing activity to followers:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to push activity to followers',
        error: error.message
      });
    }
  }

  /**
   * 获取用户推送统计
   * GET /api/v1/activities/push/stats
   */
  static async getUserPushStats(req, res) {
    try {
      const userId = req.user.id;
      const { timeRange = 7 } = req.query;

      const stats = await PushService.getUserPushStats(userId, parseInt(timeRange));

      res.json({
        success: true,
        message: 'Push stats retrieved successfully',
        data: stats
      });
    } catch (error) {
      console.error('Error getting push stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get push stats',
        error: error.message
      });
    }
  }

  /**
   * 更新用户推送设置
   * PUT /api/v1/activities/push/settings
   */
  static async updateUserPushSettings(req, res) {
    try {
      const userId = req.user.id;
      const settings = req.body;

      const result = await PushService.updateUserPushSettings(userId, settings);

      res.json({
        success: true,
        message: 'Push settings updated successfully',
        data: result
      });
    } catch (error) {
      console.error('Error updating push settings:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update push settings',
        error: error.message
      });
    }
  }
}

module.exports = ActivityController;
