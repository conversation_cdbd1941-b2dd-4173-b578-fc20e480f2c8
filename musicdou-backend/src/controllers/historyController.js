const PlayHistory = require('../models/PlayHistory');
const Music = require('../models/Music');
const Playlist = require('../models/Playlist');
const mongoose = require('mongoose');

/**
 * 获取用户播放历史
 */
const getPlayHistory = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { 
      page = 1, 
      limit = 20, 
      startDate, 
      endDate,
      playSource,
      isCompleted
    } = req.query;
    
    // 构建查询条件
    const options = {
      page: parseInt(page),
      limit: Math.min(parseInt(limit), 100) // 限制最大100条
    };
    
    if (startDate) options.startDate = startDate;
    if (endDate) options.endDate = endDate;
    
    // 获取播放历史
    const result = await PlayHistory.getUserHistory(userId, options);
    
    // 如果有额外过滤条件，进行过滤
    let filteredHistories = result.histories;
    if (playSource) {
      filteredHistories = filteredHistories.filter(h => h.playSource === playSource);
    }
    if (isCompleted !== undefined) {
      const completed = isCompleted === 'true';
      filteredHistories = filteredHistories.filter(h => h.isCompleted === completed);
    }
    
    res.json({
      success: true,
      message: 'Play history retrieved successfully',
      data: {
        histories: filteredHistories,
        pagination: result.pagination,
        filters: {
          startDate,
          endDate,
          playSource,
          isCompleted
        }
      }
    });
    
  } catch (error) {
    console.error('Get play history error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get play history'
    });
  }
};

/**
 * 获取最近播放的音乐
 */
const getRecentlyPlayed = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { limit = 10 } = req.query;
    
    const recentlyPlayed = await PlayHistory.getRecentlyPlayed(userId, parseInt(limit));
    
    res.json({
      success: true,
      message: 'Recently played music retrieved successfully',
      data: {
        recentlyPlayed,
        count: recentlyPlayed.length
      }
    });
    
  } catch (error) {
    console.error('Get recently played error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get recently played music'
    });
  }
};

/**
 * 获取用户播放统计
 */
const getPlayStats = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { period = '30d' } = req.query;
    
    // 验证时间段参数
    const validPeriods = ['7d', '30d', '90d', '1y'];
    if (!validPeriods.includes(period)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Invalid period parameter'
      });
    }
    
    const stats = await PlayHistory.getUserStats(userId, period);
    
    res.json({
      success: true,
      message: 'Play statistics retrieved successfully',
      data: {
        period,
        stats
      }
    });
    
  } catch (error) {
    console.error('Get play stats error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get play statistics'
    });
  }
};

/**
 * 删除播放历史记录
 */
const deletePlayHistory = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { historyId } = req.params;
    
    // 验证历史记录是否存在且属于当前用户
    const history = await PlayHistory.findOne({
      _id: historyId,
      userId
    });
    
    if (!history) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Play history record not found'
      });
    }
    
    // 删除记录
    await PlayHistory.findByIdAndDelete(historyId);
    
    res.json({
      success: true,
      message: 'Play history record deleted successfully',
      data: {
        deletedId: historyId
      }
    });
    
  } catch (error) {
    console.error('Delete play history error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to delete play history record'
    });
  }
};

/**
 * 批量删除播放历史记录
 */
const batchDeletePlayHistory = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { historyIds, deleteAll = false, beforeDate } = req.body;
    
    let deleteQuery = { userId };
    
    if (deleteAll) {
      // 删除所有历史记录
      if (beforeDate) {
        deleteQuery.startTime = { $lt: new Date(beforeDate) };
      }
    } else if (Array.isArray(historyIds) && historyIds.length > 0) {
      // 删除指定的历史记录
      deleteQuery._id = { $in: historyIds };
    } else {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Either historyIds array or deleteAll flag is required'
      });
    }
    
    const result = await PlayHistory.deleteMany(deleteQuery);
    
    res.json({
      success: true,
      message: `${result.deletedCount} play history records deleted successfully`,
      data: {
        deletedCount: result.deletedCount,
        deleteAll,
        beforeDate
      }
    });
    
  } catch (error) {
    console.error('Batch delete play history error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to batch delete play history records'
    });
  }
};

/**
 * 获取播放历史详情
 */
const getPlayHistoryDetail = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { historyId } = req.params;
    
    // 获取历史记录详情
    const history = await PlayHistory.findOne({
      _id: historyId,
      userId
    })
    .populate('musicId', 'title artist album duration coverImage fileFormat quality')
    .populate('playlistId', 'name coverImage');
    
    if (!history) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Play history record not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Play history detail retrieved successfully',
      data: {
        history
      }
    });
    
  } catch (error) {
    console.error('Get play history detail error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get play history detail'
    });
  }
};

/**
 * 获取播放历史统计图表数据
 */
const getPlayHistoryChart = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { period = '30d', chartType = 'daily' } = req.query;
    
    // 计算时间范围
    const now = new Date();
    let startDate;
    let groupFormat;
    
    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        groupFormat = '%Y-%m-%d';
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        groupFormat = '%Y-%m-%d';
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        groupFormat = '%Y-%m-%d';
        break;
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        groupFormat = '%Y-%m';
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        groupFormat = '%Y-%m-%d';
    }
    
    // 聚合查询
    const chartData = await PlayHistory.aggregate([
      {
        $match: {
          userId: new mongoose.Types.ObjectId(userId),
          startTime: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: groupFormat,
              date: '$startTime'
            }
          },
          playCount: { $sum: 1 },
          completedCount: { $sum: { $cond: ['$isCompleted', 1, 0] } },
          skippedCount: { $sum: { $cond: ['$isSkipped', 1, 0] } },
          totalPlayTime: { $sum: '$playDuration' },
          uniqueSongs: { $addToSet: '$musicId' }
        }
      },
      {
        $addFields: {
          uniqueSongsCount: { $size: '$uniqueSongs' },
          completionRate: {
            $cond: [
              { $gt: ['$playCount', 0] },
              { $multiply: [{ $divide: ['$completedCount', '$playCount'] }, 100] },
              0
            ]
          }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);
    
    res.json({
      success: true,
      message: 'Play history chart data retrieved successfully',
      data: {
        period,
        chartType,
        chartData,
        summary: {
          totalDays: chartData.length,
          totalPlays: chartData.reduce((sum, day) => sum + day.playCount, 0),
          totalCompletedPlays: chartData.reduce((sum, day) => sum + day.completedCount, 0),
          totalPlayTime: chartData.reduce((sum, day) => sum + day.totalPlayTime, 0)
        }
      }
    });
    
  } catch (error) {
    console.error('Get play history chart error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get play history chart data'
    });
  }
};

module.exports = {
  getPlayHistory,
  getRecentlyPlayed,
  getPlayStats,
  deletePlayHistory,
  batchDeletePlayHistory,
  getPlayHistoryDetail,
  getPlayHistoryChart
};
