const Like = require('../models/Like');
const Music = require('../models/Music');
const Comment = require('../models/Comment');
const Playlist = require('../models/Playlist');

/**
 * 点赞控制器
 * 处理音乐、评论、歌单的点赞功能
 */

/**
 * 点赞目标
 * POST /api/v1/likes
 */
const likeTarget = async (req, res) => {
  try {
    const { targetType, targetId } = req.body;
    const userId = req.user._id;

    // 验证目标类型
    if (!['music', 'comment', 'playlist'].includes(targetType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid target type'
      });
    }

    // 验证目标是否存在
    let target;
    switch (targetType) {
      case 'music':
        target = await Music.findById(targetId);
        break;
      case 'comment':
        target = await Comment.findById(targetId);
        break;
      case 'playlist':
        target = await Playlist.findById(targetId);
        break;
    }

    if (!target) {
      return res.status(404).json({
        success: false,
        message: `${targetType} not found`
      });
    }

    // 检查是否已经点赞
    const existingLike = await Like.findOne({
      user: userId,
      targetType,
      targetId
    });

    if (existingLike) {
      if (existingLike.status === 'active') {
        return res.status(400).json({
          success: false,
          message: 'Already liked'
        });
      } else {
        // 重新激活点赞
        await existingLike.reactivate();
        
        // 更新目标的点赞数
        await updateTargetLikeCount(targetType, targetId, 1);
        
        return res.json({
          success: true,
          message: 'Like reactivated successfully',
          data: existingLike
        });
      }
    }

    // 创建新的点赞记录
    const likeData = {
      user: userId,
      targetType,
      targetId,
      metadata: {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        isMobile: /Mobile|Android|iPhone|iPad/.test(req.get('User-Agent'))
      }
    };

    const like = new Like(likeData);
    await like.save();

    // 更新目标的点赞数
    await updateTargetLikeCount(targetType, targetId, 1);

    res.status(201).json({
      success: true,
      message: 'Liked successfully',
      data: like
    });

  } catch (error) {
    console.error('Like target error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to like target',
      error: error.message
    });
  }
};

/**
 * 取消点赞
 * DELETE /api/v1/likes
 */
const unlikeTarget = async (req, res) => {
  try {
    const { targetType, targetId } = req.body;
    const userId = req.user._id;

    // 查找点赞记录
    const like = await Like.findOne({
      user: userId,
      targetType,
      targetId,
      status: 'active'
    });

    if (!like) {
      return res.status(404).json({
        success: false,
        message: 'Like not found'
      });
    }

    // 取消点赞
    await like.cancel();

    // 更新目标的点赞数
    await updateTargetLikeCount(targetType, targetId, -1);

    res.json({
      success: true,
      message: 'Unliked successfully',
      data: like
    });

  } catch (error) {
    console.error('Unlike target error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to unlike target',
      error: error.message
    });
  }
};

/**
 * 获取用户点赞列表
 * GET /api/v1/likes/user/:userId
 */
const getUserLikes = async (req, res) => {
  try {
    const { userId } = req.params;
    const {
      targetType,
      page = 1,
      limit = 20,
      sort = 'createdAt'
    } = req.query;

    const options = {
      targetType,
      page: parseInt(page),
      limit: parseInt(limit),
      sort: sort === 'createdAt' ? { createdAt: -1 } : { createdAt: 1 }
    };

    const likes = await Like.getUserLikes(userId, options);

    // 根据目标类型填充相关信息
    const populatedLikes = await Promise.all(likes.map(async (like) => {
      const likeObj = like.toObject();
      
      switch (like.targetType) {
        case 'music':
          const music = await Music.findById(like.targetId)
            .select('title artist album coverImage duration');
          likeObj.target = music;
          break;
        case 'comment':
          const comment = await Comment.findById(like.targetId)
            .select('content author musicId createdAt')
            .populate('author', 'username avatar')
            .populate('musicId', 'title artist');
          likeObj.target = comment;
          break;
        case 'playlist':
          const playlist = await Playlist.findById(like.targetId)
            .select('name description coverImage createdBy songsCount');
          likeObj.target = playlist;
          break;
      }
      
      return likeObj;
    }));

    // 获取总数
    const total = await Like.countDocuments({
      user: userId,
      targetType: targetType || { $exists: true },
      status: 'active'
    });

    res.json({
      success: true,
      data: {
        likes: populatedLikes,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get user likes error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user likes',
      error: error.message
    });
  }
};

/**
 * 获取目标点赞列表
 * GET /api/v1/likes/:targetType/:targetId
 */
const getTargetLikes = async (req, res) => {
  try {
    const { targetType, targetId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    const likes = await Like.find({
      targetType,
      targetId,
      status: 'active'
    })
    .populate('user', 'username avatar')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));

    const total = await Like.countDocuments({
      targetType,
      targetId,
      status: 'active'
    });

    res.json({
      success: true,
      data: {
        likes,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get target likes error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get target likes',
      error: error.message
    });
  }
};

/**
 * 检查用户点赞状态
 * GET /api/v1/likes/check
 */
const checkLikeStatus = async (req, res) => {
  try {
    const { targetType, targetId } = req.query;
    const userId = req.user._id;

    const like = await Like.checkUserLike(userId, targetType, targetId);

    res.json({
      success: true,
      data: {
        isLiked: !!like,
        like: like || null
      }
    });

  } catch (error) {
    console.error('Check like status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check like status',
      error: error.message
    });
  }
};

/**
 * 批量检查点赞状态
 * POST /api/v1/likes/batch-check
 */
const batchCheckLikeStatus = async (req, res) => {
  try {
    const { targetType, targetIds } = req.body;
    const userId = req.user._id;

    const likes = await Like.batchCheckUserLikes(userId, targetType, targetIds);
    const likedIds = likes.map(like => like.targetId.toString());

    const result = targetIds.map(id => ({
      targetId: id,
      isLiked: likedIds.includes(id.toString())
    }));

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Batch check like status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to batch check like status',
      error: error.message
    });
  }
};

/**
 * 获取用户点赞统计
 * GET /api/v1/likes/stats/user/:userId
 */
const getUserLikeStats = async (req, res) => {
  try {
    const { userId } = req.params;

    const stats = await Like.getUserLikeStats(userId);
    
    // 格式化统计结果
    const formattedStats = {
      total: 0,
      music: 0,
      comment: 0,
      playlist: 0
    };

    stats.forEach(stat => {
      formattedStats[stat._id] = stat.count;
      formattedStats.total += stat.count;
    });

    res.json({
      success: true,
      data: formattedStats
    });

  } catch (error) {
    console.error('Get user like stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user like stats',
      error: error.message
    });
  }
};

/**
 * 获取热门点赞目标
 * GET /api/v1/likes/popular/:targetType
 */
const getPopularTargets = async (req, res) => {
  try {
    const { targetType } = req.params;
    const { timeRange = 7, limit = 10 } = req.query;

    const popularTargets = await Like.getPopularTargets(targetType, {
      timeRange: parseInt(timeRange),
      limit: parseInt(limit)
    });

    // 填充目标详细信息
    const populatedTargets = await Promise.all(popularTargets.map(async (item) => {
      let target;
      switch (targetType) {
        case 'music':
          target = await Music.findById(item._id)
            .select('title artist album coverImage duration');
          break;
        case 'comment':
          target = await Comment.findById(item._id)
            .select('content author musicId createdAt')
            .populate('author', 'username avatar')
            .populate('musicId', 'title artist');
          break;
        case 'playlist':
          target = await Playlist.findById(item._id)
            .select('name description coverImage createdBy songsCount');
          break;
      }

      return {
        target,
        likeCount: item.likeCount,
        latestLike: item.latestLike
      };
    }));

    res.json({
      success: true,
      data: populatedTargets
    });

  } catch (error) {
    console.error('Get popular targets error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get popular targets',
      error: error.message
    });
  }
};

/**
 * 更新目标的点赞数
 * @param {String} targetType - 目标类型
 * @param {ObjectId} targetId - 目标ID
 * @param {Number} increment - 增量（1或-1）
 */
const updateTargetLikeCount = async (targetType, targetId, increment) => {
  try {
    switch (targetType) {
      case 'music':
        await Music.findByIdAndUpdate(targetId, {
          $inc: { 'stats.likesCount': increment }
        });
        break;
      case 'comment':
        await Comment.findByIdAndUpdate(targetId, {
          $inc: { 'stats.likesCount': increment }
        });
        break;
      case 'playlist':
        await Playlist.findByIdAndUpdate(targetId, {
          $inc: { 'stats.likesCount': increment }
        });
        break;
    }
  } catch (error) {
    console.error('Update target like count error:', error);
  }
};

module.exports = {
  likeTarget,
  unlikeTarget,
  getUserLikes,
  getTargetLikes,
  checkLikeStatus,
  batchCheckLikeStatus,
  getUserLikeStats,
  getPopularTargets
};
