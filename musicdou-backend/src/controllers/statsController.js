const PlayHistory = require('../models/PlayHistory');
const PlayStats = require('../models/PlayStats');
const Music = require('../models/Music');
const User = require('../models/User');
const UserBehavior = require('../models/UserBehavior');
const mongoose = require('mongoose');

/**
 * 获取热门音乐排行榜
 */
const getPopularMusic = async (req, res) => {
  try {
    const { period = '30d', limit = 20 } = req.query;
    
    // 验证参数
    const validPeriods = ['7d', '30d', '90d'];
    if (!validPeriods.includes(period)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Invalid period parameter'
      });
    }
    
    const popularMusic = await PlayHistory.getPopularMusic({
      period,
      limit: Math.min(parseInt(limit), 100)
    });
    
    res.json({
      success: true,
      message: 'Popular music retrieved successfully',
      data: {
        period,
        popularMusic,
        count: popularMusic.length
      }
    });
    
  } catch (error) {
    console.error('Get popular music error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get popular music'
    });
  }
};

/**
 * 获取用户播放行为分析
 */
const getUserBehaviorAnalysis = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { period = '30d' } = req.query;
    
    // 计算时间范围
    const now = new Date();
    let startDate;
    
    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
    
    // 获取用户行为统计
    const behaviorStats = await UserBehavior.aggregate([
      {
        $match: {
          userId: new mongoose.Types.ObjectId(userId),
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$actionType',
          count: { $sum: 1 },
          totalWeight: { $sum: '$weight' },
          avgWeight: { $avg: '$weight' }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);
    
    // 获取用户偏好分析
    const preferences = await UserBehavior.getUserPreferences(userId, { limit: 20 });
    
    // 获取播放时段分析
    const hourlyAnalysis = await PlayHistory.aggregate([
      {
        $match: {
          userId: new mongoose.Types.ObjectId(userId),
          startTime: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: { $hour: '$startTime' },
          playCount: { $sum: 1 },
          completedCount: { $sum: { $cond: ['$isCompleted', 1, 0] } }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);
    
    res.json({
      success: true,
      message: 'User behavior analysis retrieved successfully',
      data: {
        period,
        behaviorStats,
        preferences,
        hourlyAnalysis,
        summary: {
          totalActions: behaviorStats.reduce((sum, stat) => sum + stat.count, 0),
          preferredGenres: preferences.slice(0, 5).map(p => p._id.genre).filter(Boolean),
          preferredArtists: preferences.slice(0, 5).map(p => p._id.artist).filter(Boolean),
          mostActiveHour: hourlyAnalysis.length > 0 ? 
            hourlyAnalysis.reduce((max, hour) => hour.playCount > max.playCount ? hour : max)._id : null
        }
      }
    });
    
  } catch (error) {
    console.error('Get user behavior analysis error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get user behavior analysis'
    });
  }
};

/**
 * 获取系统播放统计
 */
const getSystemStats = async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    
    // 计算时间范围
    const now = new Date();
    let startDate;
    
    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
    
    // 获取系统播放统计
    const systemStats = await PlayHistory.aggregate([
      {
        $match: {
          startTime: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: null,
          totalPlays: { $sum: 1 },
          completedPlays: { $sum: { $cond: ['$isCompleted', 1, 0] } },
          skippedPlays: { $sum: { $cond: ['$isSkipped', 1, 0] } },
          totalPlayTime: { $sum: '$playDuration' },
          uniqueUsers: { $addToSet: '$userId' },
          uniqueSongs: { $addToSet: '$musicId' }
        }
      },
      {
        $addFields: {
          uniqueUsersCount: { $size: '$uniqueUsers' },
          uniqueSongsCount: { $size: '$uniqueSongs' },
          completionRate: {
            $cond: [
              { $gt: ['$totalPlays', 0] },
              { $multiply: [{ $divide: ['$completedPlays', '$totalPlays'] }, 100] },
              0
            ]
          },
          skipRate: {
            $cond: [
              { $gt: ['$totalPlays', 0] },
              { $multiply: [{ $divide: ['$skippedPlays', '$totalPlays'] }, 100] },
              0
            ]
          },
          avgPlayTime: {
            $cond: [
              { $gt: ['$totalPlays', 0] },
              { $divide: ['$totalPlayTime', '$totalPlays'] },
              0
            ]
          }
        }
      }
    ]);
    
    // 获取播放来源分布
    const sourceDistribution = await PlayHistory.aggregate([
      {
        $match: {
          startTime: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$playSource',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);
    
    // 获取播放质量分布
    const qualityDistribution = await PlayHistory.aggregate([
      {
        $match: {
          startTime: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$playQuality',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);
    
    res.json({
      success: true,
      message: 'System statistics retrieved successfully',
      data: {
        period,
        stats: systemStats[0] || {
          totalPlays: 0,
          completedPlays: 0,
          skippedPlays: 0,
          totalPlayTime: 0,
          uniqueUsersCount: 0,
          uniqueSongsCount: 0,
          completionRate: 0,
          skipRate: 0,
          avgPlayTime: 0
        },
        sourceDistribution,
        qualityDistribution
      }
    });
    
  } catch (error) {
    console.error('Get system stats error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get system statistics'
    });
  }
};

/**
 * 获取音乐详细统计
 */
const getMusicStats = async (req, res) => {
  try {
    const { musicId } = req.params;
    const { period = '30d' } = req.query;
    
    // 验证音乐是否存在
    const music = await Music.findById(musicId);
    if (!music) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Music not found'
      });
    }
    
    // 计算时间范围
    const now = new Date();
    let startDate;
    
    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
    
    // 获取音乐播放统计
    const musicStats = await PlayHistory.aggregate([
      {
        $match: {
          musicId: new mongoose.Types.ObjectId(musicId),
          startTime: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: null,
          totalPlays: { $sum: 1 },
          completedPlays: { $sum: { $cond: ['$isCompleted', 1, 0] } },
          skippedPlays: { $sum: { $cond: ['$isSkipped', 1, 0] } },
          totalPlayTime: { $sum: '$playDuration' },
          uniqueUsers: { $addToSet: '$userId' },
          avgPlayTime: { $avg: '$playDuration' }
        }
      },
      {
        $addFields: {
          uniqueUsersCount: { $size: '$uniqueUsers' },
          completionRate: {
            $cond: [
              { $gt: ['$totalPlays', 0] },
              { $multiply: [{ $divide: ['$completedPlays', '$totalPlays'] }, 100] },
              0
            ]
          }
        }
      }
    ]);
    
    // 获取每日播放趋势
    const dailyTrend = await PlayHistory.aggregate([
      {
        $match: {
          musicId: new mongoose.Types.ObjectId(musicId),
          startTime: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$startTime'
            }
          },
          playCount: { $sum: 1 },
          uniqueUsers: { $addToSet: '$userId' }
        }
      },
      {
        $addFields: {
          uniqueUsersCount: { $size: '$uniqueUsers' }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);
    
    res.json({
      success: true,
      message: 'Music statistics retrieved successfully',
      data: {
        music: {
          id: music._id,
          title: music.title,
          artist: music.artist,
          album: music.album
        },
        period,
        stats: musicStats[0] || {
          totalPlays: 0,
          completedPlays: 0,
          skippedPlays: 0,
          totalPlayTime: 0,
          uniqueUsersCount: 0,
          avgPlayTime: 0,
          completionRate: 0
        },
        dailyTrend
      }
    });
    
  } catch (error) {
    console.error('Get music stats error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get music statistics'
    });
  }
};

/**
 * 获取用户排行榜
 */
const getUserLeaderboard = async (req, res) => {
  try {
    const { period = '30d', limit = 20, sortBy = 'playTime' } = req.query;

    // 计算时间范围
    const now = new Date();
    let startDate;

    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // 构建排序字段
    let sortField;
    switch (sortBy) {
      case 'playCount':
        sortField = { totalPlays: -1 };
        break;
      case 'playTime':
        sortField = { totalPlayTime: -1 };
        break;
      case 'uniqueSongs':
        sortField = { uniqueSongsCount: -1 };
        break;
      default:
        sortField = { totalPlayTime: -1 };
    }

    // 获取用户排行榜
    const leaderboard = await PlayHistory.aggregate([
      {
        $match: {
          startTime: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$userId',
          totalPlays: { $sum: 1 },
          completedPlays: { $sum: { $cond: ['$isCompleted', 1, 0] } },
          totalPlayTime: { $sum: '$playDuration' },
          uniqueSongs: { $addToSet: '$musicId' }
        }
      },
      {
        $addFields: {
          uniqueSongsCount: { $size: '$uniqueSongs' },
          completionRate: {
            $cond: [
              { $gt: ['$totalPlays', 0] },
              { $multiply: [{ $divide: ['$completedPlays', '$totalPlays'] }, 100] },
              0
            ]
          }
        }
      },
      {
        $sort: sortField
      },
      {
        $limit: parseInt(limit)
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $unwind: '$user'
      },
      {
        $project: {
          userId: '$_id',
          username: '$user.username',
          displayName: '$user.profile.displayName',
          avatar: '$user.avatar',
          totalPlays: 1,
          completedPlays: 1,
          totalPlayTime: 1,
          uniqueSongsCount: 1,
          completionRate: 1
        }
      }
    ]);

    res.json({
      success: true,
      message: 'User leaderboard retrieved successfully',
      data: {
        period,
        sortBy,
        leaderboard,
        count: leaderboard.length
      }
    });

  } catch (error) {
    console.error('Get user leaderboard error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get user leaderboard'
    });
  }
};

/**
 * 获取艺术家统计排行
 */
const getArtistStats = async (req, res) => {
  try {
    const { period = '30d', limit = 20 } = req.query;

    // 计算时间范围
    const now = new Date();
    let startDate;

    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // 获取艺术家统计
    const artistStats = await PlayHistory.aggregate([
      {
        $match: {
          startTime: { $gte: startDate }
        }
      },
      {
        $lookup: {
          from: 'music',
          localField: 'musicId',
          foreignField: '_id',
          as: 'music'
        }
      },
      {
        $unwind: '$music'
      },
      {
        $group: {
          _id: '$music.artist',
          totalPlays: { $sum: 1 },
          completedPlays: { $sum: { $cond: ['$isCompleted', 1, 0] } },
          totalPlayTime: { $sum: '$playDuration' },
          uniqueUsers: { $addToSet: '$userId' },
          uniqueSongs: { $addToSet: '$musicId' }
        }
      },
      {
        $addFields: {
          uniqueUsersCount: { $size: '$uniqueUsers' },
          uniqueSongsCount: { $size: '$uniqueSongs' },
          completionRate: {
            $cond: [
              { $gt: ['$totalPlays', 0] },
              { $multiply: [{ $divide: ['$completedPlays', '$totalPlays'] }, 100] },
              0
            ]
          },
          avgPlayTime: {
            $cond: [
              { $gt: ['$totalPlays', 0] },
              { $divide: ['$totalPlayTime', '$totalPlays'] },
              0
            ]
          }
        }
      },
      {
        $sort: { totalPlays: -1 }
      },
      {
        $limit: parseInt(limit)
      },
      {
        $project: {
          artist: '$_id',
          totalPlays: 1,
          completedPlays: 1,
          totalPlayTime: 1,
          uniqueUsersCount: 1,
          uniqueSongsCount: 1,
          completionRate: 1,
          avgPlayTime: 1
        }
      }
    ]);

    res.json({
      success: true,
      message: 'Artist statistics retrieved successfully',
      data: {
        period,
        artistStats,
        count: artistStats.length
      }
    });

  } catch (error) {
    console.error('Get artist stats error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get artist statistics'
    });
  }
};

/**
 * 生成播放报告
 */
const generatePlayReport = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { period = '30d', includeCharts = true } = req.query;

    // 计算时间范围
    const now = new Date();
    let startDate;

    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // 获取用户基本统计
    const userStats = await PlayHistory.getUserStats(userId, period);

    // 获取最近播放
    const recentlyPlayed = await PlayHistory.getRecentlyPlayed(userId, 10);

    // 获取用户偏好
    const preferences = await UserBehavior.getUserPreferences(userId, { limit: 10 });

    // 获取播放时段分析
    const hourlyAnalysis = await PlayHistory.aggregate([
      {
        $match: {
          userId: new mongoose.Types.ObjectId(userId),
          startTime: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: { $hour: '$startTime' },
          playCount: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    // 获取每日播放趋势（如果需要图表）
    let dailyTrend = [];
    if (includeCharts === 'true') {
      dailyTrend = await PlayHistory.aggregate([
        {
          $match: {
            userId: new mongoose.Types.ObjectId(userId),
            startTime: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$startTime'
              }
            },
            playCount: { $sum: 1 },
            completedCount: { $sum: { $cond: ['$isCompleted', 1, 0] } },
            playTime: { $sum: '$playDuration' }
          }
        },
        {
          $sort: { _id: 1 }
        }
      ]);
    }

    // 生成报告
    const report = {
      period,
      generatedAt: new Date(),
      summary: userStats,
      recentlyPlayed: recentlyPlayed.slice(0, 5),
      topPreferences: {
        genres: preferences.filter(p => p._id.genre).slice(0, 5),
        artists: preferences.filter(p => p._id.artist).slice(0, 5)
      },
      listeningPatterns: {
        hourlyDistribution: hourlyAnalysis,
        mostActiveHour: hourlyAnalysis.length > 0 ?
          hourlyAnalysis.reduce((max, hour) => hour.playCount > max.playCount ? hour : max)._id : null,
        dailyTrend: includeCharts === 'true' ? dailyTrend : []
      },
      insights: {
        totalHours: Math.round((userStats.totalPlayTime || 0) / 3600 * 100) / 100,
        averageSessionLength: userStats.totalPlays > 0 ?
          Math.round((userStats.totalPlayTime || 0) / userStats.totalPlays / 60 * 100) / 100 : 0,
        discoveryRate: userStats.totalPlays > 0 ?
          Math.round((userStats.uniqueSongsCount || 0) / userStats.totalPlays * 100) : 0
      }
    };

    res.json({
      success: true,
      message: 'Play report generated successfully',
      data: {
        report
      }
    });

  } catch (error) {
    console.error('Generate play report error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to generate play report'
    });
  }
};

module.exports = {
  getPopularMusic,
  getUserBehaviorAnalysis,
  getSystemStats,
  getMusicStats,
  getUserLeaderboard,
  getArtistStats,
  generatePlayReport
};
