const recommendationService = require('../services/recommendationService');
const userBehaviorAnalysisService = require('../services/userBehaviorAnalysisService');
const RecommendationResult = require('../models/RecommendationResult');
const RecommendationLog = require('../models/RecommendationLog');
const UserPreference = require('../models/UserPreference');

/**
 * 推荐系统控制器
 * 处理推荐相关的API请求
 */
class RecommendationController {

  /**
   * 获取个性化推荐
   * GET /api/v1/recommendations/personalized
   */
  async getPersonalizedRecommendations(req, res) {
    try {
      const userId = req.user.id;
      const {
        limit = 20,
        algorithm,
        forceRefresh = false,
        diversityWeight = 0.3,
        noveltyWeight = 0.2,
        popularityWeight = 0.1,
        genreFilter,
        artistFilter,
        qualityFilter,
        timeRange = 'all'
      } = req.query;

      const options = {
        limit: parseInt(limit),
        algorithm,
        forceRefresh: forceRefresh === 'true',
        diversityWeight: parseFloat(diversityWeight),
        noveltyWeight: parseFloat(noveltyWeight),
        popularityWeight: parseFloat(popularityWeight),
        genreFilter: genreFilter ? genreFilter.split(',') : [],
        artistFilter: artistFilter ? artistFilter.split(',') : [],
        qualityFilter: qualityFilter ? qualityFilter.split(',') : [],
        timeRange,
        context: {
          userAgent: req.get('User-Agent'),
          ipAddress: req.ip,
          referrer: req.get('Referer')
        }
      };

      const recommendations = await recommendationService.generatePersonalizedRecommendations(userId, options);

      res.json({
        success: true,
        data: {
          recommendations: recommendations.recommendations,
          metadata: {
            userId,
            recommendationType: recommendations.recommendationType,
            algorithm: recommendations.algorithmInfo.primaryAlgorithm,
            confidence: recommendations.algorithmInfo.confidence,
            processingTime: recommendations.algorithmInfo.processingTime,
            generatedAt: recommendations.generatedAt,
            expiresAt: recommendations.expiresAt,
            parameters: recommendations.parameters
          }
        }
      });

    } catch (error) {
      console.error('Error getting personalized recommendations:', error);
      res.status(500).json({
        success: false,
        message: '获取个性化推荐失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 获取相似音乐推荐
   * GET /api/v1/recommendations/similar/:musicId
   */
  async getSimilarMusicRecommendations(req, res) {
    try {
      const userId = req.user.id;
      const { musicId } = req.params;
      const { limit = 10 } = req.query;

      const recommendations = await recommendationService.getSimilarMusicRecommendations(
        musicId,
        userId,
        parseInt(limit)
      );

      res.json({
        success: true,
        data: {
          musicId,
          recommendations,
          metadata: {
            userId,
            recommendationType: 'similar',
            algorithm: 'content_based',
            generatedAt: new Date()
          }
        }
      });

    } catch (error) {
      console.error('Error getting similar music recommendations:', error);
      res.status(500).json({
        success: false,
        message: '获取相似音乐推荐失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 获取热门推荐
   * GET /api/v1/recommendations/popular
   */
  async getPopularRecommendations(req, res) {
    try {
      const userId = req.user.id;
      const { limit = 20, timeRange = '7d' } = req.query;

      // 使用流行度推荐算法
      const userPreference = await recommendationService.getUserPreference(userId);
      const recommendations = await recommendationService.popularityBasedRecommendation(
        userId,
        userPreference,
        { limit: parseInt(limit) }
      );

      res.json({
        success: true,
        data: {
          recommendations,
          metadata: {
            userId,
            recommendationType: 'popular',
            algorithm: 'popularity',
            timeRange,
            generatedAt: new Date()
          }
        }
      });

    } catch (error) {
      console.error('Error getting popular recommendations:', error);
      res.status(500).json({
        success: false,
        message: '获取热门推荐失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 获取新音乐发现推荐
   * GET /api/v1/recommendations/discover
   */
  async getDiscoveryRecommendations(req, res) {
    try {
      const userId = req.user.id;
      const { limit = 20 } = req.query;

      // 使用随机推荐算法发现新音乐
      const userPreference = await recommendationService.getUserPreference(userId);
      const recommendations = await recommendationService.randomRecommendation(
        userId,
        userPreference,
        { limit: parseInt(limit) }
      );

      res.json({
        success: true,
        data: {
          recommendations,
          metadata: {
            userId,
            recommendationType: 'new_discovery',
            algorithm: 'random',
            generatedAt: new Date()
          }
        }
      });

    } catch (error) {
      console.error('Error getting discovery recommendations:', error);
      res.status(500).json({
        success: false,
        message: '获取发现推荐失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 记录推荐反馈
   * POST /api/v1/recommendations/feedback
   */
  async recordFeedback(req, res) {
    try {
      const userId = req.user.id;
      const {
        musicId,
        feedbackType, // 'click', 'play', 'complete', 'skip', 'favorite', 'share'
        playDuration,
        isCompleted,
        isSkipped,
        dwellTime,
        explicitFeedback // 'like', 'dislike', 'not_interested', 'already_known'
      } = req.body;

      await recommendationService.recordRecommendationFeedback(userId, musicId, feedbackType, {
        playDuration,
        isCompleted,
        isSkipped,
        dwellTime,
        explicitFeedback
      });

      res.json({
        success: true,
        message: '反馈记录成功'
      });

    } catch (error) {
      console.error('Error recording recommendation feedback:', error);
      res.status(500).json({
        success: false,
        message: '记录反馈失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 获取用户偏好信息
   * GET /api/v1/recommendations/preferences
   */
  async getUserPreferences(req, res) {
    try {
      const userId = req.user.id;
      
      const preference = await UserPreference.findOne({ userId });
      
      if (!preference) {
        return res.json({
          success: true,
          data: {
            message: '用户偏好数据不存在，请先播放一些音乐来建立偏好模型'
          }
        });
      }

      res.json({
        success: true,
        data: {
          userId,
          genrePreferences: preference.genrePreferences.slice(0, 10), // 前10个流派
          artistPreferences: preference.artistPreferences.slice(0, 10), // 前10个艺术家
          qualityPreferences: preference.qualityPreferences,
          behaviorProfile: preference.behaviorProfile,
          updateStats: preference.updateStats
        }
      });

    } catch (error) {
      console.error('Error getting user preferences:', error);
      res.status(500).json({
        success: false,
        message: '获取用户偏好失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 刷新用户行为分析
   * POST /api/v1/recommendations/analyze-behavior
   */
  async refreshBehaviorAnalysis(req, res) {
    try {
      const userId = req.user.id;
      const { timeRange = '30d' } = req.body;

      const analysis = await userBehaviorAnalysisService.analyzeUserBehavior(userId, timeRange);

      if (!analysis) {
        return res.json({
          success: true,
          message: '没有足够的播放历史数据进行分析'
        });
      }

      res.json({
        success: true,
        data: analysis,
        message: '用户行为分析完成'
      });

    } catch (error) {
      console.error('Error refreshing behavior analysis:', error);
      res.status(500).json({
        success: false,
        message: '刷新行为分析失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 获取推荐统计信息
   * GET /api/v1/recommendations/stats
   */
  async getRecommendationStats(req, res) {
    try {
      const userId = req.user.id;
      const { timeRange = '7d' } = req.query;

      const stats = await RecommendationLog.getUserStats(userId, timeRange);

      res.json({
        success: true,
        data: {
          userId,
          timeRange,
          stats,
          generatedAt: new Date()
        }
      });

    } catch (error) {
      console.error('Error getting recommendation stats:', error);
      res.status(500).json({
        success: false,
        message: '获取推荐统计失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
}

module.exports = new RecommendationController();
