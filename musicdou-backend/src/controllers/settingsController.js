const User = require('../models/User');

// 获取用户设置
const getUserSettings = async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User not found'
      });
    }

    // 构建设置响应数据
    const settings = {
      notifications: {
        email: user.privacySettings.notificationSettings.comment.email,
        push: user.privacySettings.notificationSettings.comment.push,
        newMusic: user.privacySettings.notificationSettings.newMusic.inApp,
        comments: user.privacySettings.notificationSettings.comment.inApp,
        follows: user.privacySettings.notificationSettings.follow.inApp
      },
      privacy: {
        profileVisibility: user.privacySettings.allowPublicToSeeProfile ? 'public' : 
                          user.privacySettings.allowFollowersToSeeActivities ? 'followers' : 'private',
        publicPlaylists: user.privacySettings.allowFollowersToSeeActivities,
        showActivity: user.privacySettings.allowFollowersToSeeActivities
      },
      playback: {
        quality: user.playbackSettings?.quality || 'medium',
        autoplay: user.playbackSettings?.autoplay !== false,
        crossfade: user.playbackSettings?.crossfade || false,
        volume: user.playbackSettings?.volume || 80
      },
      appearance: {
        theme: user.appearanceSettings?.theme || 'system',
        language: user.appearanceSettings?.language || 'zh-CN'
      }
    };

    res.status(200).json({
      success: true,
      data: settings
    });

  } catch (error) {
    console.error('Get user settings error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get user settings'
    });
  }
};

// 更新用户设置
const updateUserSettings = async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User not found'
      });
    }

    const { notifications, privacy, playback, appearance } = req.body;

    // 更新通知设置
    if (notifications) {
      if (notifications.email !== undefined) {
        user.privacySettings.notificationSettings.comment.email = notifications.email;
        user.privacySettings.notificationSettings.reply.email = notifications.email;
        user.privacySettings.notificationSettings.system.email = notifications.email;
      }
      if (notifications.push !== undefined) {
        user.privacySettings.notificationSettings.comment.push = notifications.push;
        user.privacySettings.notificationSettings.reply.push = notifications.push;
        user.privacySettings.notificationSettings.follow.push = notifications.push;
      }
      if (notifications.newMusic !== undefined) {
        user.privacySettings.notificationSettings.newMusic.inApp = notifications.newMusic;
        user.privacySettings.notificationSettings.newMusic.push = notifications.newMusic;
      }
      if (notifications.comments !== undefined) {
        user.privacySettings.notificationSettings.comment.inApp = notifications.comments;
      }
      if (notifications.follows !== undefined) {
        user.privacySettings.notificationSettings.follow.inApp = notifications.follows;
      }
    }

    // 更新隐私设置
    if (privacy) {
      if (privacy.profileVisibility !== undefined) {
        switch (privacy.profileVisibility) {
          case 'public':
            user.privacySettings.allowPublicToSeeProfile = true;
            user.privacySettings.allowFollowersToSeeActivities = true;
            break;
          case 'followers':
            user.privacySettings.allowPublicToSeeProfile = false;
            user.privacySettings.allowFollowersToSeeActivities = true;
            break;
          case 'private':
            user.privacySettings.allowPublicToSeeProfile = false;
            user.privacySettings.allowFollowersToSeeActivities = false;
            break;
        }
      }
      if (privacy.publicPlaylists !== undefined) {
        user.privacySettings.allowFollowersToSeeActivities = privacy.publicPlaylists;
      }
      if (privacy.showActivity !== undefined) {
        user.privacySettings.allowFollowersToSeeActivities = privacy.showActivity;
      }
    }

    // 更新播放设置
    if (playback) {
      if (!user.playbackSettings) {
        user.playbackSettings = {};
      }
      if (playback.quality !== undefined) {
        user.playbackSettings.quality = playback.quality;
      }
      if (playback.autoplay !== undefined) {
        user.playbackSettings.autoplay = playback.autoplay;
      }
      if (playback.crossfade !== undefined) {
        user.playbackSettings.crossfade = playback.crossfade;
      }
      if (playback.volume !== undefined) {
        user.playbackSettings.volume = Math.max(0, Math.min(100, playback.volume));
      }
    }

    // 更新外观设置
    if (appearance) {
      if (!user.appearanceSettings) {
        user.appearanceSettings = {};
      }
      if (appearance.theme !== undefined) {
        user.appearanceSettings.theme = appearance.theme;
      }
      if (appearance.language !== undefined) {
        user.appearanceSettings.language = appearance.language;
      }
    }

    await user.save();

    // 返回更新后的设置
    const updatedSettings = {
      notifications: {
        email: user.privacySettings.notificationSettings.comment.email,
        push: user.privacySettings.notificationSettings.comment.push,
        newMusic: user.privacySettings.notificationSettings.newMusic.inApp,
        comments: user.privacySettings.notificationSettings.comment.inApp,
        follows: user.privacySettings.notificationSettings.follow.inApp
      },
      privacy: {
        profileVisibility: user.privacySettings.allowPublicToSeeProfile ? 'public' : 
                          user.privacySettings.allowFollowersToSeeActivities ? 'followers' : 'private',
        publicPlaylists: user.privacySettings.allowFollowersToSeeActivities,
        showActivity: user.privacySettings.allowFollowersToSeeActivities
      },
      playback: {
        quality: user.playbackSettings?.quality || 'medium',
        autoplay: user.playbackSettings?.autoplay !== false,
        crossfade: user.playbackSettings?.crossfade || false,
        volume: user.playbackSettings?.volume || 80
      },
      appearance: {
        theme: user.appearanceSettings?.theme || 'system',
        language: user.appearanceSettings?.language || 'zh-CN'
      }
    };

    res.status(200).json({
      success: true,
      message: 'Settings updated successfully',
      data: updatedSettings
    });

  } catch (error) {
    console.error('Update user settings error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to update user settings'
    });
  }
};

// 重置设置为默认值
const resetUserSettings = async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User not found'
      });
    }

    // 重置为默认设置
    user.playbackSettings = {
      quality: 'medium',
      autoplay: true,
      crossfade: false,
      volume: 80
    };

    user.appearanceSettings = {
      theme: 'system',
      language: 'zh-CN'
    };

    await user.save();

    res.status(200).json({
      success: true,
      message: 'Settings reset to default successfully'
    });

  } catch (error) {
    console.error('Reset user settings error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to reset user settings'
    });
  }
};

module.exports = {
  getUserSettings,
  updateUserSettings,
  resetUserSettings
};
