const PlayQueue = require('../models/PlayQueue');
const Music = require('../models/Music');
const Playlist = require('../models/Playlist');
const mongoose = require('mongoose');

/**
 * 获取播放队列
 */
const getQueue = async (req, res) => {
  try {
    const userId = req.user.userId;
    
    // 获取播放队列
    let playQueue = await PlayQueue.findOne({ userId })
      .populate({
        path: 'songs.musicId',
        select: 'title artist album duration coverImage fileFormat quality status'
      });
    
    if (!playQueue) {
      // 创建空队列
      playQueue = new PlayQueue({ userId });
      await playQueue.save();
    }
    
    res.json({
      success: true,
      message: 'Queue retrieved successfully',
      data: {
        queue: {
          id: playQueue._id,
          totalSongs: playQueue.totalSongs,
          currentPosition: playQueue.currentPosition,
          playMode: playQueue.playMode,
          volume: playQueue.volume,
          preferredQuality: playQueue.preferredQuality,
          queueSource: playQueue.queueSource,
          queueSourceId: playQueue.queueSourceId,
          queueName: playQueue.queueName,
          hasNext: playQueue.hasNext,
          hasPrevious: playQueue.hasPrevious,
          songs: playQueue.songs,
          lastActiveAt: playQueue.lastActiveAt
        }
      }
    });
    
  } catch (error) {
    console.error('Get queue error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get queue'
    });
  }
};

/**
 * 添加歌曲到队列
 */
const addToQueue = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { musicId, position, source = 'manual', sourceId = null } = req.body;
    
    // 验证音乐是否存在
    const music = await Music.findById(musicId);
    if (!music) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Music not found'
      });
    }
    
    // 验证音乐状态
    if (music.status !== 'approved') {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Music is not available'
      });
    }
    
    // 获取或创建播放队列
    let playQueue = await PlayQueue.findOne({ userId });
    if (!playQueue) {
      playQueue = new PlayQueue({ userId });
      await playQueue.save();
    }
    
    // 检查歌曲是否已在队列中
    const existingSong = playQueue.songs.find(song => 
      song.musicId.toString() === musicId.toString()
    );
    
    if (existingSong) {
      return res.status(409).json({
        error: 'Conflict',
        message: 'Song already exists in queue'
      });
    }
    
    // 添加歌曲到队列
    await playQueue.addSong(musicId, { position, source, sourceId });
    
    // 重新获取更新后的队列
    await playQueue.populate({
      path: 'songs.musicId',
      select: 'title artist album duration coverImage'
    });
    
    res.json({
      success: true,
      message: 'Song added to queue successfully',
      data: {
        addedSong: {
          musicId,
          music,
          position: position !== undefined ? position : playQueue.songs.length - 1
        },
        queue: {
          totalSongs: playQueue.totalSongs,
          currentPosition: playQueue.currentPosition
        }
      }
    });
    
  } catch (error) {
    console.error('Add to queue error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to add song to queue'
    });
  }
};

/**
 * 批量添加歌曲到队列
 */
const addMultipleToQueue = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { musicIds, source = 'manual', sourceId = null, replace = false } = req.body;
    
    // 验证输入
    if (!Array.isArray(musicIds) || musicIds.length === 0) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'musicIds must be a non-empty array'
      });
    }
    
    // 验证所有音乐是否存在且可用
    const musicList = await Music.find({
      _id: { $in: musicIds },
      status: 'approved'
    });
    
    if (musicList.length !== musicIds.length) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Some music items are not found or not available'
      });
    }
    
    // 获取或创建播放队列
    let playQueue = await PlayQueue.findOne({ userId });
    if (!playQueue) {
      playQueue = new PlayQueue({ userId });
      await playQueue.save();
    }
    
    // 批量添加歌曲
    await playQueue.addSongs(musicIds, { source, sourceId, replace });
    
    // 重新获取更新后的队列
    await playQueue.populate({
      path: 'songs.musicId',
      select: 'title artist album duration coverImage'
    });
    
    res.json({
      success: true,
      message: `${musicIds.length} songs ${replace ? 'replaced' : 'added to'} queue successfully`,
      data: {
        addedCount: musicIds.length,
        replaced: replace,
        queue: {
          totalSongs: playQueue.totalSongs,
          currentPosition: playQueue.currentPosition,
          songs: playQueue.songs
        }
      }
    });
    
  } catch (error) {
    console.error('Add multiple to queue error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to add songs to queue'
    });
  }
};

/**
 * 从队列移除歌曲
 */
const removeFromQueue = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { position } = req.params;
    
    // 验证位置参数
    const pos = parseInt(position);
    if (isNaN(pos) || pos < 0) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Invalid position parameter'
      });
    }
    
    // 获取播放队列
    const playQueue = await PlayQueue.findOne({ userId });
    if (!playQueue) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Queue not found'
      });
    }
    
    // 验证位置是否有效
    if (pos >= playQueue.songs.length) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Position out of range'
      });
    }
    
    // 获取要移除的歌曲信息
    const removedSong = playQueue.songs[pos];
    
    // 移除歌曲
    await playQueue.removeSong(pos);
    
    res.json({
      success: true,
      message: 'Song removed from queue successfully',
      data: {
        removedSong: {
          musicId: removedSong.musicId,
          position: pos
        },
        queue: {
          totalSongs: playQueue.totalSongs,
          currentPosition: playQueue.currentPosition
        }
      }
    });
    
  } catch (error) {
    console.error('Remove from queue error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to remove song from queue'
    });
  }
};

/**
 * 清空队列
 */
const clearQueue = async (req, res) => {
  try {
    const userId = req.user.userId;
    
    // 获取播放队列
    const playQueue = await PlayQueue.findOne({ userId });
    if (!playQueue) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Queue not found'
      });
    }
    
    // 清空队列
    await playQueue.clear();
    
    res.json({
      success: true,
      message: 'Queue cleared successfully',
      data: {
        queue: {
          totalSongs: 0,
          currentPosition: 0,
          songs: []
        }
      }
    });
    
  } catch (error) {
    console.error('Clear queue error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to clear queue'
    });
  }
};

/**
 * 重新排序队列
 */
const reorderQueue = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { fromPosition, toPosition } = req.body;
    
    // 验证参数
    if (typeof fromPosition !== 'number' || typeof toPosition !== 'number' ||
        fromPosition < 0 || toPosition < 0) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Invalid position parameters'
      });
    }
    
    // 获取播放队列
    const playQueue = await PlayQueue.findOne({ userId });
    if (!playQueue) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Queue not found'
      });
    }
    
    // 验证位置是否有效
    if (fromPosition >= playQueue.songs.length || toPosition >= playQueue.songs.length) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Position out of range'
      });
    }
    
    // 如果位置相同，无需操作
    if (fromPosition === toPosition) {
      return res.json({
        success: true,
        message: 'No change needed',
        data: {
          queue: {
            totalSongs: playQueue.totalSongs,
            currentPosition: playQueue.currentPosition
          }
        }
      });
    }
    
    // 移动歌曲
    const [movedSong] = playQueue.songs.splice(fromPosition, 1);
    playQueue.songs.splice(toPosition, 0, movedSong);
    
    // 更新所有歌曲的位置
    playQueue.songs.forEach((song, index) => {
      song.position = index;
    });
    
    // 调整当前播放位置
    if (playQueue.currentPosition === fromPosition) {
      playQueue.currentPosition = toPosition;
    } else if (fromPosition < playQueue.currentPosition && toPosition >= playQueue.currentPosition) {
      playQueue.currentPosition--;
    } else if (fromPosition > playQueue.currentPosition && toPosition <= playQueue.currentPosition) {
      playQueue.currentPosition++;
    }
    
    playQueue.lastActiveAt = new Date();
    await playQueue.save();
    
    res.json({
      success: true,
      message: 'Queue reordered successfully',
      data: {
        movedSong: {
          musicId: movedSong.musicId,
          fromPosition,
          toPosition
        },
        queue: {
          totalSongs: playQueue.totalSongs,
          currentPosition: playQueue.currentPosition
        }
      }
    });
    
  } catch (error) {
    console.error('Reorder queue error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to reorder queue'
    });
  }
};

/**
 * 从歌单创建队列
 */
const createQueueFromPlaylist = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { playlistId, shuffle = false, replace = true } = req.body;

    // 验证歌单是否存在
    const playlist = await Playlist.findById(playlistId)
      .populate({
        path: 'songs.musicId',
        match: { status: 'approved' },
        select: 'title artist album duration coverImage'
      });

    if (!playlist) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Playlist not found'
      });
    }

    // 检查歌单访问权限
    if (!playlist.isPublic && playlist.createdBy.toString() !== userId.toString()) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Access denied to private playlist'
      });
    }

    // 获取可用的歌曲ID
    const availableSongs = playlist.songs.filter(song => song.musicId);
    const musicIds = availableSongs.map(song => song.musicId._id);

    if (musicIds.length === 0) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Playlist has no available songs'
      });
    }

    // 如果需要随机播放，打乱顺序
    if (shuffle) {
      for (let i = musicIds.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [musicIds[i], musicIds[j]] = [musicIds[j], musicIds[i]];
      }
    }

    // 获取或创建播放队列
    let playQueue = await PlayQueue.findOne({ userId });
    if (!playQueue) {
      playQueue = new PlayQueue({ userId });
      await playQueue.save();
    }

    // 添加歌曲到队列
    await playQueue.addSongs(musicIds, {
      source: 'playlist',
      sourceId: playlistId,
      replace
    });

    // 更新队列信息
    playQueue.queueSource = 'playlist';
    playQueue.queueSourceId = playlistId;
    playQueue.queueName = playlist.name;
    if (shuffle) {
      playQueue.playMode = 'shuffle';
    }
    await playQueue.save();

    // 重新获取更新后的队列
    await playQueue.populate({
      path: 'songs.musicId',
      select: 'title artist album duration coverImage'
    });

    res.json({
      success: true,
      message: 'Queue created from playlist successfully',
      data: {
        playlist: {
          id: playlist._id,
          name: playlist.name,
          totalSongs: availableSongs.length
        },
        queue: {
          totalSongs: playQueue.totalSongs,
          currentPosition: playQueue.currentPosition,
          playMode: playQueue.playMode,
          queueSource: playQueue.queueSource,
          queueName: playQueue.queueName,
          shuffle,
          replace
        }
      }
    });

  } catch (error) {
    console.error('Create queue from playlist error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create queue from playlist'
    });
  }
};

/**
 * 跳转到队列中的指定位置
 */
const jumpToPosition = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { position } = req.body;

    // 验证位置参数
    if (typeof position !== 'number' || position < 0) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Invalid position parameter'
      });
    }

    // 获取播放队列
    const playQueue = await PlayQueue.findOne({ userId });
    if (!playQueue) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Queue not found'
      });
    }

    // 验证位置是否有效
    if (position >= playQueue.songs.length) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Position out of range'
      });
    }

    // 更新当前位置
    playQueue.currentPosition = position;
    playQueue.lastActiveAt = new Date();
    await playQueue.save();

    // 获取当前歌曲信息
    const currentSong = playQueue.songs[position];
    const currentMusic = await Music.findById(currentSong.musicId);

    res.json({
      success: true,
      message: 'Jumped to position successfully',
      data: {
        position,
        currentSong: {
          musicId: currentSong.musicId,
          music: currentMusic
        },
        queue: {
          totalSongs: playQueue.totalSongs,
          currentPosition: playQueue.currentPosition,
          hasNext: playQueue.hasNext,
          hasPrevious: playQueue.hasPrevious
        }
      }
    });

  } catch (error) {
    console.error('Jump to position error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to jump to position'
    });
  }
};

/**
 * 获取队列统计信息
 */
const getQueueStats = async (req, res) => {
  try {
    const userId = req.user.userId;

    // 获取播放队列
    const playQueue = await PlayQueue.findOne({ userId })
      .populate({
        path: 'songs.musicId',
        select: 'title artist album duration fileFormat quality'
      });

    if (!playQueue) {
      return res.json({
        success: true,
        message: 'No queue found',
        data: {
          stats: {
            totalSongs: 0,
            totalDuration: 0,
            formatDistribution: {},
            qualityDistribution: {},
            artistDistribution: {}
          }
        }
      });
    }

    // 计算统计信息
    let totalDuration = 0;
    const formatDistribution = {};
    const qualityDistribution = {};
    const artistDistribution = {};

    playQueue.songs.forEach(song => {
      if (song.musicId) {
        const music = song.musicId;

        // 总时长
        totalDuration += music.duration || 0;

        // 格式分布
        const format = music.fileFormat || 'unknown';
        formatDistribution[format] = (formatDistribution[format] || 0) + 1;

        // 质量分布
        const quality = music.quality || 'unknown';
        qualityDistribution[quality] = (qualityDistribution[quality] || 0) + 1;

        // 艺术家分布
        const artist = music.artist || 'Unknown Artist';
        artistDistribution[artist] = (artistDistribution[artist] || 0) + 1;
      }
    });

    res.json({
      success: true,
      message: 'Queue stats retrieved successfully',
      data: {
        stats: {
          totalSongs: playQueue.totalSongs,
          totalDuration,
          currentPosition: playQueue.currentPosition,
          playMode: playQueue.playMode,
          queueSource: playQueue.queueSource,
          queueName: playQueue.queueName,
          formatDistribution,
          qualityDistribution,
          artistDistribution: Object.entries(artistDistribution)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10) // 只返回前10个艺术家
            .reduce((obj, [artist, count]) => {
              obj[artist] = count;
              return obj;
            }, {}),
          lastActiveAt: playQueue.lastActiveAt
        }
      }
    });

  } catch (error) {
    console.error('Get queue stats error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get queue stats'
    });
  }
};

module.exports = {
  getQueue,
  addToQueue,
  addMultipleToQueue,
  removeFromQueue,
  clearQueue,
  reorderQueue,
  createQueueFromPlaylist,
  jumpToPosition,
  getQueueStats
};
