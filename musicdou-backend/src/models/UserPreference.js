const mongoose = require('mongoose');

/**
 * 用户偏好模型
 * 存储用户的音乐偏好数据，用于个性化推荐
 */
const userPreferenceSchema = new mongoose.Schema({
  // 用户ID
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
    unique: true
  },
  
  // 流派偏好 (权重分布)
  genrePreferences: [{
    genre: {
      type: String,
      required: true,
      trim: true
    },
    weight: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    playCount: {
      type: Number,
      default: 0,
      min: 0
    },
    lastPlayed: {
      type: Date,
      default: null
    }
  }],
  
  // 艺术家偏好
  artistPreferences: [{
    artist: {
      type: String,
      required: true,
      trim: true
    },
    weight: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    playCount: {
      type: Number,
      default: 0,
      min: 0
    },
    lastPlayed: {
      type: Date,
      default: null
    }
  }],
  
  // 音质偏好
  qualityPreferences: {
    standard: { type: Number, default: 0, min: 0 },
    high: { type: Number, default: 0, min: 0 },
    super: { type: Number, default: 0, min: 0 },
    lossless: { type: Number, default: 0, min: 0 }
  },
  
  // 播放时段偏好 (24小时)
  timePreferences: [{
    hour: {
      type: Number,
      min: 0,
      max: 23,
      required: true
    },
    playCount: {
      type: Number,
      default: 0,
      min: 0
    },
    weight: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    }
  }],
  
  // 播放来源偏好
  sourcePreferences: {
    playlist: { type: Number, default: 0, min: 0 },
    search: { type: Number, default: 0, min: 0 },
    recommendation: { type: Number, default: 0, min: 0 },
    album: { type: Number, default: 0, min: 0 },
    artist: { type: Number, default: 0, min: 0 },
    random: { type: Number, default: 0, min: 0 },
    queue: { type: Number, default: 0, min: 0 }
  },
  
  // 播放模式偏好
  modePreferences: {
    sequential: { type: Number, default: 0, min: 0 },
    shuffle: { type: Number, default: 0, min: 0 },
    repeat_one: { type: Number, default: 0, min: 0 },
    repeat_all: { type: Number, default: 0, min: 0 }
  },
  
  // 用户行为特征
  behaviorProfile: {
    // 平均播放时长
    avgPlayDuration: {
      type: Number,
      default: 0,
      min: 0
    },
    
    // 完成率
    completionRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    
    // 跳过率
    skipRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    
    // 活跃度分数
    activityScore: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    
    // 探索性分数 (喜欢尝试新音乐的程度)
    explorationScore: {
      type: Number,
      default: 50,
      min: 0,
      max: 100
    },
    
    // 多样性分数 (音乐品味的多样性)
    diversityScore: {
      type: Number,
      default: 50,
      min: 0,
      max: 100
    }
  },
  
  // 相似用户列表 (协同过滤用)
  similarUsers: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    similarity: {
      type: Number,
      min: 0,
      max: 1,
      required: true
    },
    lastCalculated: {
      type: Date,
      default: Date.now
    }
  }],
  
  // 偏好更新统计
  updateStats: {
    totalPlays: {
      type: Number,
      default: 0,
      min: 0
    },
    lastPlayTime: {
      type: Date,
      default: null
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    },
    updateCount: {
      type: Number,
      default: 0,
      min: 0
    }
  }
}, {
  timestamps: true,
  collection: 'user_preferences'
});

// 索引
userPreferenceSchema.index({ 'genrePreferences.genre': 1 });
userPreferenceSchema.index({ 'artistPreferences.artist': 1 });
userPreferenceSchema.index({ 'similarUsers.userId': 1 });
userPreferenceSchema.index({ 'updateStats.lastUpdated': -1 });

// 实例方法

/**
 * 更新流派偏好
 */
userPreferenceSchema.methods.updateGenrePreference = function(genre, playDuration, isCompleted) {
  if (!genre) return;

  const existingGenre = this.genrePreferences.find(g => g.genre === genre);
  const weight = this.calculatePlayWeight(playDuration, isCompleted);

  if (existingGenre) {
    existingGenre.playCount += 1;
    existingGenre.weight = Math.min(100, existingGenre.weight + weight);
    existingGenre.lastPlayed = new Date();
  } else {
    this.genrePreferences.push({
      genre,
      weight,
      playCount: 1,
      lastPlayed: new Date()
    });
  }

  // 保持最多50个流派偏好
  if (this.genrePreferences.length > 50) {
    this.genrePreferences.sort((a, b) => b.weight - a.weight);
    this.genrePreferences = this.genrePreferences.slice(0, 50);
  }
};

/**
 * 更新艺术家偏好
 */
userPreferenceSchema.methods.updateArtistPreference = function(artist, playDuration, isCompleted) {
  if (!artist) return;

  const existingArtist = this.artistPreferences.find(a => a.artist === artist);
  const weight = this.calculatePlayWeight(playDuration, isCompleted);

  if (existingArtist) {
    existingArtist.playCount += 1;
    existingArtist.weight = Math.min(100, existingArtist.weight + weight);
    existingArtist.lastPlayed = new Date();
  } else {
    this.artistPreferences.push({
      artist,
      weight,
      playCount: 1,
      lastPlayed: new Date()
    });
  }

  // 保持最多100个艺术家偏好
  if (this.artistPreferences.length > 100) {
    this.artistPreferences.sort((a, b) => b.weight - a.weight);
    this.artistPreferences = this.artistPreferences.slice(0, 100);
  }
};

/**
 * 计算播放权重
 */
userPreferenceSchema.methods.calculatePlayWeight = function(playDuration, isCompleted) {
  let weight = 1;

  if (isCompleted) {
    weight = 5; // 完整播放权重最高
  } else if (playDuration > 120) { // 播放超过2分钟
    weight = 3;
  } else if (playDuration > 60) { // 播放超过1分钟
    weight = 2;
  } else if (playDuration < 10) { // 播放少于10秒
    weight = 0.1;
  }

  return weight;
};

/**
 * 更新行为特征
 */
userPreferenceSchema.methods.updateBehaviorProfile = function(playData) {
  const { playDuration, isCompleted, isSkipped, totalDuration } = playData;

  // 更新平均播放时长
  const currentAvg = this.behaviorProfile.avgPlayDuration || 0;
  const totalPlays = this.updateStats.totalPlays || 0;
  this.behaviorProfile.avgPlayDuration = (currentAvg * totalPlays + playDuration) / (totalPlays + 1);

  // 更新完成率和跳过率
  const completedPlays = Math.floor((this.behaviorProfile.completionRate / 100) * totalPlays);
  const skippedPlays = Math.floor((this.behaviorProfile.skipRate / 100) * totalPlays);

  const newCompletedPlays = completedPlays + (isCompleted ? 1 : 0);
  const newSkippedPlays = skippedPlays + (isSkipped ? 1 : 0);

  this.behaviorProfile.completionRate = (newCompletedPlays / (totalPlays + 1)) * 100;
  this.behaviorProfile.skipRate = (newSkippedPlays / (totalPlays + 1)) * 100;

  // 更新活跃度分数
  this.updateActivityScore();

  // 更新探索性和多样性分数
  this.updateExplorationScore();
  this.updateDiversityScore();
};

/**
 * 更新活跃度分数
 */
userPreferenceSchema.methods.updateActivityScore = function() {
  const now = new Date();
  const lastPlay = this.updateStats.lastPlayTime;

  if (!lastPlay) {
    this.behaviorProfile.activityScore = 50;
    return;
  }

  const daysSinceLastPlay = (now - lastPlay) / (1000 * 60 * 60 * 24);
  const totalPlays = this.updateStats.totalPlays || 0;

  // 基于播放频率和最近活跃度计算
  let score = Math.min(100, totalPlays * 2); // 基础分数

  if (daysSinceLastPlay < 1) {
    score += 20; // 最近活跃奖励
  } else if (daysSinceLastPlay < 7) {
    score += 10;
  } else if (daysSinceLastPlay > 30) {
    score -= 30; // 长期不活跃惩罚
  }

  this.behaviorProfile.activityScore = Math.max(0, Math.min(100, score));
};

/**
 * 更新探索性分数
 */
userPreferenceSchema.methods.updateExplorationScore = function() {
  const totalGenres = this.genrePreferences.length;
  const totalArtists = this.artistPreferences.length;
  const totalPlays = this.updateStats.totalPlays || 1;

  // 基于流派和艺术家多样性计算探索性
  const genreScore = Math.min(50, totalGenres * 5);
  const artistScore = Math.min(50, totalArtists * 2);

  this.behaviorProfile.explorationScore = Math.min(100, genreScore + artistScore);
};

/**
 * 更新多样性分数
 */
userPreferenceSchema.methods.updateDiversityScore = function() {
  // 计算流派权重的分布均匀度
  const genreWeights = this.genrePreferences.map(g => g.weight);
  const artistWeights = this.artistPreferences.map(a => a.weight);

  const genreDiversity = this.calculateDiversity(genreWeights);
  const artistDiversity = this.calculateDiversity(artistWeights);

  this.behaviorProfile.diversityScore = Math.min(100, (genreDiversity + artistDiversity) * 50);
};

/**
 * 计算多样性指数 (基于香农熵)
 */
userPreferenceSchema.methods.calculateDiversity = function(weights) {
  if (weights.length === 0) return 0;

  const total = weights.reduce((sum, w) => sum + w, 0);
  if (total === 0) return 0;

  const probabilities = weights.map(w => w / total);
  const entropy = -probabilities.reduce((sum, p) => {
    return p > 0 ? sum + p * Math.log2(p) : sum;
  }, 0);

  // 标准化到0-1范围
  const maxEntropy = Math.log2(weights.length);
  return maxEntropy > 0 ? entropy / maxEntropy : 0;
};

module.exports = mongoose.model('UserPreference', userPreferenceSchema);
