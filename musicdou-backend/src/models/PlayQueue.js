const mongoose = require('mongoose');

/**
 * 播放队列模型
 * 管理用户的播放队列，支持队列操作和播放控制
 */
const playQueueSchema = new mongoose.Schema({
  // 用户ID
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
    unique: true // 每个用户只有一个播放队列
  },
  
  // 队列中的歌曲列表
  songs: [{
    musicId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Music',
      required: true
    },
    // 在队列中的位置
    position: {
      type: Number,
      required: true,
      min: 0
    },
    // 添加到队列的时间
    addedAt: {
      type: Date,
      default: Date.now
    },
    // 来源信息
    source: {
      type: String,
      enum: ['playlist', 'search', 'recommendation', 'album', 'artist', 'manual'],
      default: 'manual'
    },
    // 来源ID（如歌单ID）
    sourceId: {
      type: mongoose.Schema.Types.ObjectId,
      default: null
    }
  }],
  
  // 当前播放位置
  currentPosition: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // 播放模式
  playMode: {
    type: String,
    enum: ['sequential', 'shuffle', 'repeat_one', 'repeat_all'],
    default: 'sequential'
  },
  
  // 是否正在播放
  isPlaying: {
    type: Boolean,
    default: false
  },
  
  // 当前播放进度（秒）
  currentProgress: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // 音量设置
  volume: {
    type: Number,
    default: 80,
    min: 0,
    max: 100
  },
  
  // 播放质量偏好
  preferredQuality: {
    type: String,
    enum: ['standard', 'high', 'super', 'lossless'],
    default: 'standard'
  },
  
  // 随机播放的种子（用于保持随机顺序一致性）
  shuffleSeed: {
    type: String,
    default: null
  },
  
  // 队列创建来源
  queueSource: {
    type: String,
    enum: ['playlist', 'album', 'artist', 'search', 'recommendation', 'manual'],
    default: 'manual'
  },
  
  // 队列来源ID
  queueSourceId: {
    type: mongoose.Schema.Types.ObjectId,
    default: null
  },
  
  // 队列名称
  queueName: {
    type: String,
    maxlength: 100,
    default: null
  },
  
  // 最后活动时间
  lastActiveAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  collection: 'play_queues'
});

// 索引
playQueueSchema.index({ lastActiveAt: -1 });

// 虚拟字段
playQueueSchema.virtual('totalSongs').get(function() {
  return this.songs ? this.songs.length : 0;
});

playQueueSchema.virtual('currentSong').get(function() {
  if (this.songs && this.songs.length > 0 && this.currentPosition < this.songs.length) {
    return this.songs[this.currentPosition];
  }
  return null;
});

playQueueSchema.virtual('hasNext').get(function() {
  if (this.playMode === 'repeat_all' || this.playMode === 'repeat_one') {
    return this.songs && this.songs.length > 0;
  }
  return this.currentPosition < (this.songs ? this.songs.length - 1 : 0);
});

playQueueSchema.virtual('hasPrevious').get(function() {
  if (this.playMode === 'repeat_all') {
    return this.songs && this.songs.length > 0;
  }
  return this.currentPosition > 0;
});

// 实例方法

/**
 * 添加歌曲到队列
 */
playQueueSchema.methods.addSong = function(musicId, options = {}) {
  const { position, source = 'manual', sourceId = null } = options;
  
  const newSong = {
    musicId,
    position: position !== undefined ? position : this.songs.length,
    source,
    sourceId,
    addedAt: new Date()
  };
  
  if (position !== undefined && position < this.songs.length) {
    // 插入到指定位置
    this.songs.splice(position, 0, newSong);
    // 重新排序后续歌曲的位置
    for (let i = position + 1; i < this.songs.length; i++) {
      this.songs[i].position = i;
    }
    // 如果插入位置在当前播放位置之前，调整当前位置
    if (position <= this.currentPosition) {
      this.currentPosition++;
    }
  } else {
    // 添加到队列末尾
    this.songs.push(newSong);
  }
  
  this.lastActiveAt = new Date();
  return this.save();
};

/**
 * 批量添加歌曲
 */
playQueueSchema.methods.addSongs = function(musicIds, options = {}) {
  const { source = 'manual', sourceId = null, replace = false } = options;
  
  if (replace) {
    this.songs = [];
    this.currentPosition = 0;
  }
  
  const startPosition = this.songs.length;
  const newSongs = musicIds.map((musicId, index) => ({
    musicId,
    position: startPosition + index,
    source,
    sourceId,
    addedAt: new Date()
  }));
  
  this.songs.push(...newSongs);
  this.lastActiveAt = new Date();
  return this.save();
};

/**
 * 移除歌曲
 */
playQueueSchema.methods.removeSong = function(position) {
  if (position < 0 || position >= this.songs.length) {
    throw new Error('Invalid position');
  }
  
  this.songs.splice(position, 1);
  
  // 重新排序后续歌曲的位置
  for (let i = position; i < this.songs.length; i++) {
    this.songs[i].position = i;
  }
  
  // 调整当前播放位置
  if (position < this.currentPosition) {
    this.currentPosition--;
  } else if (position === this.currentPosition && this.currentPosition >= this.songs.length) {
    this.currentPosition = Math.max(0, this.songs.length - 1);
  }
  
  this.lastActiveAt = new Date();
  return this.save();
};

/**
 * 清空队列
 */
playQueueSchema.methods.clear = function() {
  this.songs = [];
  this.currentPosition = 0;
  this.isPlaying = false;
  this.currentProgress = 0;
  this.lastActiveAt = new Date();
  return this.save();
};

/**
 * 移动到下一首
 */
playQueueSchema.methods.next = function() {
  if (!this.hasNext) {
    return false;
  }
  
  switch (this.playMode) {
    case 'sequential':
      if (this.currentPosition < this.songs.length - 1) {
        this.currentPosition++;
      } else {
        return false;
      }
      break;
    case 'repeat_all':
      this.currentPosition = (this.currentPosition + 1) % this.songs.length;
      break;
    case 'repeat_one':
      // 保持当前位置不变
      break;
    case 'shuffle':
      // 随机选择下一首（避免重复当前歌曲）
      if (this.songs.length > 1) {
        let nextPosition;
        do {
          nextPosition = Math.floor(Math.random() * this.songs.length);
        } while (nextPosition === this.currentPosition);
        this.currentPosition = nextPosition;
      }
      break;
  }
  
  this.currentProgress = 0;
  this.lastActiveAt = new Date();
  return this.save();
};

/**
 * 移动到上一首
 */
playQueueSchema.methods.previous = function() {
  if (!this.hasPrevious) {
    return false;
  }
  
  switch (this.playMode) {
    case 'sequential':
      if (this.currentPosition > 0) {
        this.currentPosition--;
      } else {
        return false;
      }
      break;
    case 'repeat_all':
      this.currentPosition = this.currentPosition === 0 ? this.songs.length - 1 : this.currentPosition - 1;
      break;
    case 'shuffle':
      // 随机选择上一首（避免重复当前歌曲）
      if (this.songs.length > 1) {
        let prevPosition;
        do {
          prevPosition = Math.floor(Math.random() * this.songs.length);
        } while (prevPosition === this.currentPosition);
        this.currentPosition = prevPosition;
      }
      break;
  }
  
  this.currentProgress = 0;
  this.lastActiveAt = new Date();
  return this.save();
};

const PlayQueue = mongoose.model('PlayQueue', playQueueSchema);

module.exports = PlayQueue;
