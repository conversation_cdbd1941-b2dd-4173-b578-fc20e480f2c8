const mongoose = require('mongoose');

/**
 * 相似度矩阵模型
 * 存储用户-用户和音乐-音乐的相似度数据，用于协同过滤推荐
 */
const similarityMatrixSchema = new mongoose.Schema({
  // 相似度类型
  similarityType: {
    type: String,
    enum: ['user_user', 'item_item', 'genre_genre', 'artist_artist'],
    required: [true, 'Similarity type is required'],
    index: true
  },
  
  // 源ID (用户ID或音乐ID)
  sourceId: {
    type: mongoose.Schema.Types.ObjectId,
    required: [true, 'Source ID is required'],
    index: true
  },
  
  // 目标ID (用户ID或音乐ID)
  targetId: {
    type: mongoose.Schema.Types.ObjectId,
    required: [true, 'Target ID is required'],
    index: true
  },
  
  // 相似度分数 (0-1)
  similarity: {
    type: Number,
    min: 0,
    max: 1,
    required: [true, 'Similarity score is required']
  },
  
  // 计算方法
  method: {
    type: String,
    enum: ['cosine', 'pearson', 'jaccard', 'euclidean', 'manhattan'],
    required: [true, 'Calculation method is required']
  },
  
  // 计算详情
  calculationDetails: {
    // 共同项目数量
    commonItems: {
      type: Number,
      min: 0,
      default: 0
    },
    
    // 源项目总数
    sourceItemCount: {
      type: Number,
      min: 0,
      default: 0
    },
    
    // 目标项目总数
    targetItemCount: {
      type: Number,
      min: 0,
      default: 0
    },
    
    // 置信度
    confidence: {
      type: Number,
      min: 0,
      max: 1,
      default: 0
    },
    
    // 支持度
    support: {
      type: Number,
      min: 0,
      max: 1,
      default: 0
    }
  },
  
  // 数据来源
  dataSource: {
    // 基于的数据类型
    baseDataType: {
      type: String,
      enum: ['play_history', 'user_behavior', 'music_features', 'user_preferences'],
      required: true
    },
    
    // 数据时间范围
    timeRange: {
      startDate: {
        type: Date,
        default: null
      },
      endDate: {
        type: Date,
        default: null
      }
    },
    
    // 数据点数量
    dataPoints: {
      type: Number,
      min: 0,
      default: 0
    }
  },
  
  // 质量指标
  qualityMetrics: {
    // 统计显著性
    significance: {
      type: Number,
      min: 0,
      max: 1,
      default: 0
    },
    
    // 稳定性分数
    stability: {
      type: Number,
      min: 0,
      max: 1,
      default: 0
    },
    
    // 新颖性分数
    novelty: {
      type: Number,
      min: 0,
      max: 1,
      default: 0
    }
  },
  
  // 计算时间戳
  calculatedAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  // 过期时间
  expiresAt: {
    type: Date,
    required: true,
    default: function() {
      // 默认7天后过期
      return new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
    }
  },
  
  // 使用统计
  usageStats: {
    // 使用次数
    usageCount: {
      type: Number,
      default: 0,
      min: 0
    },
    
    // 最后使用时间
    lastUsedAt: {
      type: Date,
      default: null
    },
    
    // 推荐成功次数
    successCount: {
      type: Number,
      default: 0,
      min: 0
    },
    
    // 推荐成功率
    successRate: {
      type: Number,
      min: 0,
      max: 1,
      default: 0
    }
  },
  
  // 状态
  status: {
    type: String,
    enum: ['active', 'expired', 'deprecated'],
    default: 'active'
  }
}, {
  timestamps: true,
  collection: 'similarity_matrices'
});

// 复合索引
similarityMatrixSchema.index({ similarityType: 1, sourceId: 1, targetId: 1 }, { unique: true });
similarityMatrixSchema.index({ similarityType: 1, sourceId: 1, similarity: -1 });
similarityMatrixSchema.index({ similarityType: 1, targetId: 1, similarity: -1 });
similarityMatrixSchema.index({ status: 1, expiresAt: 1 });
similarityMatrixSchema.index({ calculatedAt: -1 });

// TTL索引 - 自动删除过期记录
similarityMatrixSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// 静态方法

/**
 * 获取最相似的项目
 */
similarityMatrixSchema.statics.getMostSimilar = async function(similarityType, sourceId, limit = 10, minSimilarity = 0.1) {
  return this.find({
    similarityType,
    sourceId: new mongoose.Types.ObjectId(sourceId),
    similarity: { $gte: minSimilarity },
    status: 'active',
    expiresAt: { $gt: new Date() }
  })
  .sort({ similarity: -1 })
  .limit(limit)
  .populate('targetId');
};

/**
 * 批量获取相似度
 */
similarityMatrixSchema.statics.getBatchSimilarities = async function(similarityType, sourceIds, targetIds) {
  const sourceObjectIds = sourceIds.map(id => new mongoose.Types.ObjectId(id));
  const targetObjectIds = targetIds.map(id => new mongoose.Types.ObjectId(id));
  
  return this.find({
    similarityType,
    sourceId: { $in: sourceObjectIds },
    targetId: { $in: targetObjectIds },
    status: 'active',
    expiresAt: { $gt: new Date() }
  });
};

/**
 * 更新使用统计
 */
similarityMatrixSchema.methods.updateUsageStats = function(isSuccess = false) {
  this.usageStats.usageCount += 1;
  this.usageStats.lastUsedAt = new Date();
  
  if (isSuccess) {
    this.usageStats.successCount += 1;
  }
  
  this.usageStats.successRate = this.usageStats.successCount / this.usageStats.usageCount;
  
  return this.save();
};

/**
 * 检查是否需要重新计算
 */
similarityMatrixSchema.methods.needsRecalculation = function() {
  const now = new Date();
  const daysSinceCalculation = (now - this.calculatedAt) / (1000 * 60 * 60 * 24);
  
  // 如果计算时间超过3天，或者成功率低于30%，需要重新计算
  return daysSinceCalculation > 3 || this.usageStats.successRate < 0.3;
};

module.exports = mongoose.model('SimilarityMatrix', similarityMatrixSchema);
