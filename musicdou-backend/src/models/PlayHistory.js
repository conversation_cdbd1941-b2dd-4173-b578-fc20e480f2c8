const mongoose = require('mongoose');

/**
 * 播放历史记录模型
 * 记录用户的音乐播放历史，支持播放统计和历史查询
 */
const playHistorySchema = new mongoose.Schema({
  // 用户ID
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },

  // 音乐ID
  musicId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Music',
    required: [true, 'Music ID is required']
  },

  // 歌单ID（如果是从歌单播放）
  playlistId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Playlist',
    default: null
  },

  // 播放开始时间
  startTime: {
    type: Date,
    required: [true, 'Start time is required'],
    default: Date.now
  },
  
  // 播放结束时间
  endTime: {
    type: Date,
    default: null
  },
  
  // 播放时长（秒）
  playDuration: {
    type: Number,
    min: 0,
    default: 0
  },
  
  // 音乐总时长（秒）
  totalDuration: {
    type: Number,
    min: 0,
    required: [true, 'Total duration is required']
  },
  
  // 播放进度百分比
  playProgress: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  
  // 是否完整播放（播放进度 >= 80%）
  isCompleted: {
    type: Boolean,
    default: false
  },
  
  // 播放来源
  playSource: {
    type: String,
    enum: ['playlist', 'search', 'recommendation', 'album', 'artist', 'random', 'queue'],
    default: 'playlist'
  },
  
  // 播放模式
  playMode: {
    type: String,
    enum: ['sequential', 'shuffle', 'repeat_one', 'repeat_all'],
    default: 'sequential'
  },
  
  // 设备信息
  deviceInfo: {
    type: String,
    maxlength: 200,
    default: null
  },
  
  // IP地址
  ipAddress: {
    type: String,
    maxlength: 45,
    default: null
  },
  
  // 用户代理
  userAgent: {
    type: String,
    maxlength: 500,
    default: null
  },
  
  // 会话ID
  sessionId: {
    type: String,
    maxlength: 100,
    default: null
  },
  
  // 播放质量
  playQuality: {
    type: String,
    enum: ['standard', 'high', 'super', 'lossless'],
    default: 'standard'
  },
  
  // 是否跳过
  isSkipped: {
    type: Boolean,
    default: false
  },
  
  // 跳过时间点（秒）
  skipTime: {
    type: Number,
    min: 0,
    default: null
  },
  
  // 额外元数据
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true,
  collection: 'play_histories'
});

// 复合索引
playHistorySchema.index({ userId: 1, startTime: -1 });
playHistorySchema.index({ musicId: 1, startTime: -1 });
playHistorySchema.index({ playlistId: 1, startTime: -1 });
playHistorySchema.index({ userId: 1, musicId: 1 });
playHistorySchema.index({ userId: 1, isCompleted: 1 });
playHistorySchema.index({ startTime: -1 });

// 虚拟字段
playHistorySchema.virtual('duration').get(function() {
  if (this.endTime && this.startTime) {
    return Math.floor((this.endTime - this.startTime) / 1000);
  }
  return this.playDuration;
});

// 实例方法

/**
 * 标记播放完成
 */
playHistorySchema.methods.markCompleted = function(endTime = new Date()) {
  this.endTime = endTime;
  this.playDuration = Math.floor((endTime - this.startTime) / 1000);
  this.playProgress = Math.min(100, (this.playDuration / this.totalDuration) * 100);
  this.isCompleted = this.playProgress >= 80;
  return this.save();
};

/**
 * 标记跳过
 */
playHistorySchema.methods.markSkipped = function(skipTime, endTime = new Date()) {
  this.isSkipped = true;
  this.skipTime = skipTime;
  this.endTime = endTime;
  this.playDuration = Math.floor((endTime - this.startTime) / 1000);
  this.playProgress = Math.min(100, (this.playDuration / this.totalDuration) * 100);
  return this.save();
};

// 静态方法

/**
 * 获取用户播放历史
 */
playHistorySchema.statics.getUserHistory = async function(userId, options = {}) {
  const { page = 1, limit = 20, startDate, endDate } = options;
  const skip = (page - 1) * limit;

  const query = { userId: new mongoose.Types.ObjectId(userId) };

  if (startDate || endDate) {
    query.startTime = {};
    if (startDate) query.startTime.$gte = new Date(startDate);
    if (endDate) query.startTime.$lte = new Date(endDate);
  }

  const [histories, total] = await Promise.all([
    this.find(query)
      .populate('musicId', 'title artist album duration coverImage')
      .populate('playlistId', 'name coverImage')
      .sort({ startTime: -1 })
      .skip(skip)
      .limit(limit),
    this.countDocuments(query)
  ]);

  return {
    histories,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  };
};

/**
 * 获取用户播放统计
 */
playHistorySchema.statics.getUserStats = async function(userId, period = '30d') {
  const now = new Date();
  let startDate;

  switch (period) {
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '90d':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case '1y':
      startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }

  const stats = await this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        startTime: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: null,
        totalPlays: { $sum: 1 },
        completedPlays: { $sum: { $cond: ['$isCompleted', 1, 0] } },
        skippedPlays: { $sum: { $cond: ['$isSkipped', 1, 0] } },
        totalPlayTime: { $sum: '$playDuration' },
        uniqueSongs: { $addToSet: '$musicId' },
        uniquePlaylists: { $addToSet: '$playlistId' }
      }
    },
    {
      $addFields: {
        uniqueSongsCount: { $size: '$uniqueSongs' },
        uniquePlaylistsCount: { $size: '$uniquePlaylists' },
        completionRate: {
          $cond: [
            { $gt: ['$totalPlays', 0] },
            { $multiply: [{ $divide: ['$completedPlays', '$totalPlays'] }, 100] },
            0
          ]
        },
        skipRate: {
          $cond: [
            { $gt: ['$totalPlays', 0] },
            { $multiply: [{ $divide: ['$skippedPlays', '$totalPlays'] }, 100] },
            0
          ]
        }
      }
    }
  ]);

  return stats[0] || {
    totalPlays: 0,
    completedPlays: 0,
    skippedPlays: 0,
    totalPlayTime: 0,
    uniqueSongsCount: 0,
    uniquePlaylistsCount: 0,
    completionRate: 0,
    skipRate: 0
  };
};

/**
 * 获取最近播放的音乐
 */
playHistorySchema.statics.getRecentlyPlayed = async function(userId, limit = 10) {
  return this.aggregate([
    {
      $match: { userId: new mongoose.Types.ObjectId(userId) }
    },
    {
      $sort: { startTime: -1 }
    },
    {
      $group: {
        _id: '$musicId',
        lastPlayed: { $first: '$startTime' },
        playCount: { $sum: 1 },
        totalPlayTime: { $sum: '$playDuration' },
        lastPlaylistId: { $first: '$playlistId' }
      }
    },
    {
      $sort: { lastPlayed: -1 }
    },
    {
      $limit: limit
    },
    {
      $lookup: {
        from: 'music',
        localField: '_id',
        foreignField: '_id',
        as: 'music'
      }
    },
    {
      $unwind: '$music'
    },
    {
      $lookup: {
        from: 'playlists',
        localField: 'lastPlaylistId',
        foreignField: '_id',
        as: 'playlist'
      }
    },
    {
      $project: {
        music: 1,
        lastPlayed: 1,
        playCount: 1,
        totalPlayTime: 1,
        playlist: { $arrayElemAt: ['$playlist', 0] }
      }
    }
  ]);
};

/**
 * 获取热门音乐
 */
playHistorySchema.statics.getPopularMusic = async function(options = {}) {
  const { period = '30d', limit = 20 } = options;
  const now = new Date();
  let startDate;

  switch (period) {
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '90d':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }

  return this.aggregate([
    {
      $match: {
        startTime: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$musicId',
        playCount: { $sum: 1 },
        uniqueUsers: { $addToSet: '$userId' },
        totalPlayTime: { $sum: '$playDuration' },
        completedPlays: { $sum: { $cond: ['$isCompleted', 1, 0] } }
      }
    },
    {
      $addFields: {
        uniqueUsersCount: { $size: '$uniqueUsers' },
        completionRate: {
          $cond: [
            { $gt: ['$playCount', 0] },
            { $multiply: [{ $divide: ['$completedPlays', '$playCount'] }, 100] },
            0
          ]
        }
      }
    },
    {
      $sort: { playCount: -1, uniqueUsersCount: -1 }
    },
    {
      $limit: limit
    },
    {
      $lookup: {
        from: 'music',
        localField: '_id',
        foreignField: '_id',
        as: 'music'
      }
    },
    {
      $unwind: '$music'
    },
    {
      $project: {
        music: 1,
        playCount: 1,
        uniqueUsersCount: 1,
        totalPlayTime: 1,
        completionRate: 1
      }
    }
  ]);
};

const PlayHistory = mongoose.model('PlayHistory', playHistorySchema);

module.exports = PlayHistory;
