const mongoose = require('mongoose');

/**
 * 推荐结果模型
 * 存储推荐算法生成的推荐结果，支持缓存和效果追踪
 */
const recommendationResultSchema = new mongoose.Schema({
  // 用户ID
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
    index: true
  },
  
  // 推荐类型
  recommendationType: {
    type: String,
    enum: ['personalized', 'similar', 'popular', 'new_discovery', 'genre_based', 'artist_based', 'collaborative'],
    required: [true, 'Recommendation type is required'],
    index: true
  },
  
  // 推荐音乐列表
  recommendations: [{
    musicId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Music',
      required: true
    },
    score: {
      type: Number,
      min: 0,
      max: 1,
      required: true
    },
    reason: {
      type: String,
      maxlength: 200,
      default: null
    },
    algorithmUsed: {
      type: String,
      enum: ['collaborative_filtering', 'content_based', 'hybrid', 'popularity', 'random'],
      required: true
    },
    position: {
      type: Number,
      min: 1,
      required: true
    }
  }],
  
  // 推荐参数
  parameters: {
    // 基础参数
    limit: {
      type: Number,
      default: 20,
      min: 1,
      max: 100
    },
    
    // 流派过滤
    genreFilter: [{
      type: String,
      trim: true
    }],
    
    // 艺术家过滤
    artistFilter: [{
      type: String,
      trim: true
    }],
    
    // 音质过滤
    qualityFilter: [{
      type: String,
      enum: ['standard', 'high', 'super', 'lossless']
    }],
    
    // 时间范围过滤
    timeRange: {
      type: String,
      enum: ['all', '1d', '7d', '30d', '90d', '1y'],
      default: 'all'
    },
    
    // 多样性权重
    diversityWeight: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.3
    },
    
    // 新颖性权重
    noveltyWeight: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.2
    },
    
    // 流行度权重
    popularityWeight: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.1
    }
  },
  
  // 算法信息
  algorithmInfo: {
    primaryAlgorithm: {
      type: String,
      enum: ['collaborative_filtering', 'content_based', 'hybrid', 'popularity', 'random'],
      required: true
    },
    
    algorithmVersion: {
      type: String,
      default: '1.0'
    },
    
    confidence: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.5
    },
    
    processingTime: {
      type: Number, // 毫秒
      min: 0,
      default: 0
    },
    
    dataPoints: {
      type: Number,
      min: 0,
      default: 0
    }
  },
  
  // 推荐效果追踪
  performance: {
    // 展示次数
    impressions: {
      type: Number,
      default: 0,
      min: 0
    },
    
    // 点击次数
    clicks: {
      type: Number,
      default: 0,
      min: 0
    },
    
    // 播放次数
    plays: {
      type: Number,
      default: 0,
      min: 0
    },
    
    // 完整播放次数
    completedPlays: {
      type: Number,
      default: 0,
      min: 0
    },
    
    // 收藏次数
    favorites: {
      type: Number,
      default: 0,
      min: 0
    },
    
    // 分享次数
    shares: {
      type: Number,
      default: 0,
      min: 0
    },
    
    // 跳过次数
    skips: {
      type: Number,
      default: 0,
      min: 0
    },
    
    // 点击率
    ctr: {
      type: Number,
      min: 0,
      max: 1,
      default: 0
    },
    
    // 播放率
    playRate: {
      type: Number,
      min: 0,
      max: 1,
      default: 0
    },
    
    // 完成率
    completionRate: {
      type: Number,
      min: 0,
      max: 1,
      default: 0
    }
  },
  
  // 状态信息
  status: {
    type: String,
    enum: ['active', 'expired', 'replaced'],
    default: 'active',
    index: true
  },
  
  // 过期时间
  expiresAt: {
    type: Date,
    required: true,
    default: function() {
      // 默认24小时后过期
      return new Date(Date.now() + 24 * 60 * 60 * 1000);
    }
  },
  
  // 生成时间
  generatedAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  // 最后访问时间
  lastAccessedAt: {
    type: Date,
    default: Date.now
  },
  
  // 元数据
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true,
  collection: 'recommendation_results'
});

// 复合索引
recommendationResultSchema.index({ userId: 1, recommendationType: 1 });
recommendationResultSchema.index({ userId: 1, status: 1, expiresAt: 1 });
recommendationResultSchema.index({ status: 1, expiresAt: 1 });
recommendationResultSchema.index({ generatedAt: -1 });

// TTL索引 - 自动删除过期记录
recommendationResultSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

module.exports = mongoose.model('RecommendationResult', recommendationResultSchema);
