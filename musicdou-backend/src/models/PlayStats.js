const mongoose = require('mongoose');

/**
 * 播放统计模型
 * 存储聚合的播放统计数据，用于快速查询和分析
 */
const playStatsSchema = new mongoose.Schema({
  // 统计类型
  statsType: {
    type: String,
    enum: ['daily', 'weekly', 'monthly', 'yearly'],
    required: [true, 'Stats type is required'],
    index: true
  },
  
  // 统计日期
  date: {
    type: Date,
    required: [true, 'Date is required'],
    index: true
  },
  
  // 音乐ID（如果是单首歌曲统计）
  musicId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Music',
    default: null,
    index: true
  },
  
  // 用户ID（如果是单用户统计）
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  
  // 歌单ID（如果是歌单统计）
  playlistId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Playlist',
    default: null,
    index: true
  },
  
  // 播放次数
  playCount: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // 完整播放次数
  completedPlayCount: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // 跳过次数
  skipCount: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // 总播放时长（秒）
  totalPlayTime: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // 独立用户数
  uniqueUsers: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // 独立用户ID列表（用于去重计算）
  uniqueUserIds: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  
  // 平均播放时长
  avgPlayTime: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // 完成率（百分比）
  completionRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  
  // 跳过率（百分比）
  skipRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  
  // 播放质量分布
  qualityDistribution: {
    standard: { type: Number, default: 0 },
    high: { type: Number, default: 0 },
    super: { type: Number, default: 0 },
    lossless: { type: Number, default: 0 }
  },
  
  // 设备类型分布
  deviceDistribution: {
    web: { type: Number, default: 0 },
    mobile: { type: Number, default: 0 },
    desktop: { type: Number, default: 0 },
    tablet: { type: Number, default: 0 },
    tv: { type: Number, default: 0 },
    other: { type: Number, default: 0 }
  },
  
  // 播放来源分布
  sourceDistribution: {
    playlist: { type: Number, default: 0 },
    search: { type: Number, default: 0 },
    recommendation: { type: Number, default: 0 },
    album: { type: Number, default: 0 },
    artist: { type: Number, default: 0 },
    random: { type: Number, default: 0 },
    queue: { type: Number, default: 0 }
  },
  
  // 时段分布（24小时）
  hourlyDistribution: [{
    hour: { type: Number, min: 0, max: 23 },
    playCount: { type: Number, default: 0 }
  }],
  
  // 额外统计数据
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true,
  collection: 'play_stats'
});

// 复合索引
playStatsSchema.index({ statsType: 1, date: -1 });
playStatsSchema.index({ musicId: 1, statsType: 1, date: -1 });
playStatsSchema.index({ userId: 1, statsType: 1, date: -1 });
playStatsSchema.index({ playlistId: 1, statsType: 1, date: -1 });
playStatsSchema.index({ statsType: 1, playCount: -1 });

// 虚拟字段
playStatsSchema.virtual('totalPlays').get(function() {
  return this.playCount + this.skipCount;
});

playStatsSchema.virtual('engagementScore').get(function() {
  // 计算参与度分数（0-100）
  const completionWeight = 0.4;
  const playTimeWeight = 0.3;
  const uniqueUsersWeight = 0.3;
  
  const completionScore = this.completionRate;
  const playTimeScore = Math.min(100, (this.avgPlayTime / 180) * 100); // 假设3分钟为满分
  const uniqueUsersScore = Math.min(100, this.uniqueUsers * 10); // 10个独立用户为满分
  
  return Math.round(
    completionScore * completionWeight +
    playTimeScore * playTimeWeight +
    uniqueUsersScore * uniqueUsersWeight
  );
});

// 实例方法

/**
 * 更新统计数据
 */
playStatsSchema.methods.updateStats = function(playData) {
  const {
    playCount = 0,
    completedPlayCount = 0,
    skipCount = 0,
    totalPlayTime = 0,
    userIds = [],
    avgPlayTime = 0,
    qualityDistribution = {},
    deviceDistribution = {},
    sourceDistribution = {},
    hourlyData = []
  } = playData;
  
  // 更新基础统计
  this.playCount += playCount;
  this.completedPlayCount += completedPlayCount;
  this.skipCount += skipCount;
  this.totalPlayTime += totalPlayTime;
  
  // 更新独立用户
  const newUserIds = userIds.filter(id => !this.uniqueUserIds.includes(id));
  this.uniqueUserIds.push(...newUserIds);
  this.uniqueUsers = this.uniqueUserIds.length;
  
  // 更新平均播放时长
  if (this.playCount > 0) {
    this.avgPlayTime = this.totalPlayTime / this.playCount;
  }
  
  // 更新完成率和跳过率
  const totalPlays = this.playCount + this.skipCount;
  if (totalPlays > 0) {
    this.completionRate = (this.completedPlayCount / this.playCount) * 100;
    this.skipRate = (this.skipCount / totalPlays) * 100;
  }
  
  // 更新质量分布
  Object.keys(qualityDistribution).forEach(quality => {
    if (this.qualityDistribution[quality] !== undefined) {
      this.qualityDistribution[quality] += qualityDistribution[quality];
    }
  });
  
  // 更新设备分布
  Object.keys(deviceDistribution).forEach(device => {
    if (this.deviceDistribution[device] !== undefined) {
      this.deviceDistribution[device] += deviceDistribution[device];
    }
  });
  
  // 更新来源分布
  Object.keys(sourceDistribution).forEach(source => {
    if (this.sourceDistribution[source] !== undefined) {
      this.sourceDistribution[source] += sourceDistribution[source];
    }
  });
  
  // 更新时段分布
  hourlyData.forEach(({ hour, count }) => {
    let hourlyRecord = this.hourlyDistribution.find(h => h.hour === hour);
    if (hourlyRecord) {
      hourlyRecord.playCount += count;
    } else {
      this.hourlyDistribution.push({ hour, playCount: count });
    }
  });
  
  return this.save();
};

// 静态方法

/**
 * 获取或创建统计记录
 */
playStatsSchema.statics.getOrCreate = async function(statsType, date, filters = {}) {
  const query = { statsType, date, ...filters };
  
  let stats = await this.findOne(query);
  if (!stats) {
    stats = new this(query);
    await stats.save();
  }
  
  return stats;
};

/**
 * 获取热门音乐统计
 */
playStatsSchema.statics.getPopularMusic = function(options = {}) {
  const { 
    statsType = 'daily', 
    startDate, 
    endDate, 
    limit = 20,
    sortBy = 'playCount' 
  } = options;
  
  const query = { 
    statsType, 
    musicId: { $ne: null }
  };
  
  if (startDate || endDate) {
    query.date = {};
    if (startDate) query.date.$gte = new Date(startDate);
    if (endDate) query.date.$lte = new Date(endDate);
  }
  
  const sortOptions = {};
  sortOptions[sortBy] = -1;
  
  return this.find(query)
    .populate('musicId', 'title artist album duration coverImage')
    .sort(sortOptions)
    .limit(limit);
};

/**
 * 获取用户播放统计
 */
playStatsSchema.statics.getUserStats = function(userId, options = {}) {
  const { 
    statsType = 'daily', 
    startDate, 
    endDate 
  } = options;
  
  const query = { 
    statsType, 
    userId 
  };
  
  if (startDate || endDate) {
    query.date = {};
    if (startDate) query.date.$gte = new Date(startDate);
    if (endDate) query.date.$lte = new Date(endDate);
  }
  
  return this.find(query).sort({ date: -1 });
};

/**
 * 获取系统总体统计
 */
playStatsSchema.statics.getSystemStats = function(options = {}) {
  const { 
    statsType = 'daily', 
    startDate, 
    endDate 
  } = options;
  
  const query = { 
    statsType,
    musicId: null,
    userId: null,
    playlistId: null
  };
  
  if (startDate || endDate) {
    query.date = {};
    if (startDate) query.date.$gte = new Date(startDate);
    if (endDate) query.date.$lte = new Date(endDate);
  }
  
  return this.find(query).sort({ date: -1 });
};

const PlayStats = mongoose.model('PlayStats', playStatsSchema);

module.exports = PlayStats;
