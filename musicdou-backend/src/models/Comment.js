const mongoose = require('mongoose');

/**
 * 音乐评论模型
 * 支持多层级回复、点赞、举报、审核等功能
 */
const commentSchema = new mongoose.Schema({
  // 评论内容
  content: {
    type: String,
    required: [true, 'Comment content is required'],
    trim: true,
    minlength: [1, 'Comment content cannot be empty'],
    maxlength: [1000, 'Comment content cannot exceed 1000 characters']
  },

  // 评论者
  author: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Comment author is required']
  },

  // 被评论的音乐
  musicId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Music',
    required: [true, 'Music ID is required'],
    index: true
  },

  // 父评论ID（用于回复功能）
  parentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Comment',
    default: null,
    index: true
  },

  // 根评论ID（用于快速查找评论树）
  rootId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Comment',
    default: null,
    index: true
  },

  // 回复层级（0为根评论，1为一级回复，以此类推）
  level: {
    type: Number,
    default: 0,
    min: 0,
    max: 5, // 限制最大回复层级为5层
    index: true
  },

  // 回复目标用户（@某人）
  replyToUser: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },

  // 评论状态
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'hidden', 'deleted'],
    default: 'approved', // 默认自动通过，可配置为pending需要审核
    index: true
  },

  // 审核信息
  moderation: {
    // 审核者
    reviewedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      default: null
    },
    // 审核时间
    reviewedAt: {
      type: Date,
      default: null
    },
    // 审核原因
    reviewReason: {
      type: String,
      maxlength: [200, 'Review reason cannot exceed 200 characters'],
      default: null
    },
    // 是否为系统自动审核
    isAutoReview: {
      type: Boolean,
      default: false
    }
  },

  // 统计信息
  stats: {
    // 点赞数
    likesCount: {
      type: Number,
      default: 0,
      min: 0
    },
    // 回复数
    repliesCount: {
      type: Number,
      default: 0,
      min: 0
    },
    // 举报数
    reportsCount: {
      type: Number,
      default: 0,
      min: 0
    }
  },

  // 内容分析
  analysis: {
    // 情感分析结果
    sentiment: {
      type: String,
      enum: ['positive', 'neutral', 'negative'],
      default: 'neutral'
    },
    // 情感分数 (-1到1，-1最负面，1最正面)
    sentimentScore: {
      type: Number,
      default: 0,
      min: -1,
      max: 1
    },
    // 是否包含敏感词
    hasSensitiveWords: {
      type: Boolean,
      default: false
    },
    // 敏感词列表
    sensitiveWords: [{
      type: String
    }],
    // 内容质量分数 (0-100)
    qualityScore: {
      type: Number,
      default: 50,
      min: 0,
      max: 100
    }
  },

  // 元数据
  metadata: {
    // 用户IP地址
    ipAddress: {
      type: String,
      maxlength: 45
    },
    // 用户代理
    userAgent: {
      type: String,
      maxlength: 500
    },
    // 设备信息
    deviceInfo: {
      type: String,
      maxlength: 200
    },
    // 是否为移动端
    isMobile: {
      type: Boolean,
      default: false
    },
    // 地理位置信息
    location: {
      country: String,
      region: String,
      city: String
    }
  },

  // 是否置顶
  isPinned: {
    type: Boolean,
    default: false,
    index: true
  },

  // 是否为热门评论
  isHot: {
    type: Boolean,
    default: false,
    index: true
  },

  // 编辑历史
  editHistory: [{
    content: String,
    editedAt: {
      type: Date,
      default: Date.now
    },
    reason: String
  }],

  // 最后编辑时间
  lastEditedAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 复合索引优化查询性能
commentSchema.index({ musicId: 1, status: 1, createdAt: -1 }); // 音乐评论列表
commentSchema.index({ author: 1, createdAt: -1 }); // 用户评论历史
commentSchema.index({ parentId: 1, createdAt: 1 }); // 回复列表
commentSchema.index({ rootId: 1, level: 1, createdAt: 1 }); // 评论树结构
commentSchema.index({ status: 1, createdAt: -1 }); // 审核队列
commentSchema.index({ isPinned: -1, isHot: -1, 'stats.likesCount': -1 }); // 热门评论
commentSchema.index({ 'analysis.hasSensitiveWords': 1, status: 1 }); // 敏感内容筛选

// 虚拟字段
commentSchema.virtual('isEdited').get(function() {
  return this.editHistory && this.editHistory.length > 0;
});

commentSchema.virtual('canEdit').get(function() {
  // 评论发布后30分钟内可以编辑
  const editTimeLimit = 30 * 60 * 1000; // 30分钟
  return Date.now() - this.createdAt.getTime() < editTimeLimit;
});

commentSchema.virtual('isReply').get(function() {
  return this.parentId !== null;
});

commentSchema.virtual('isRootComment').get(function() {
  return this.level === 0;
});

// 实例方法

/**
 * 增加点赞数
 */
commentSchema.methods.incrementLikes = function() {
  this.stats.likesCount += 1;
  // 更新热门状态
  this.updateHotStatus();
  return this.save();
};

/**
 * 减少点赞数
 */
commentSchema.methods.decrementLikes = function() {
  if (this.stats.likesCount > 0) {
    this.stats.likesCount -= 1;
    this.updateHotStatus();
  }
  return this.save();
};

/**
 * 增加回复数
 */
commentSchema.methods.incrementReplies = function() {
  this.stats.repliesCount += 1;
  return this.save();
};

/**
 * 减少回复数
 */
commentSchema.methods.decrementReplies = function() {
  if (this.stats.repliesCount > 0) {
    this.stats.repliesCount -= 1;
  }
  return this.save();
};

/**
 * 增加举报数
 */
commentSchema.methods.incrementReports = function() {
  this.stats.reportsCount += 1;
  // 如果举报数过多，自动隐藏评论
  if (this.stats.reportsCount >= 5) {
    this.status = 'hidden';
  }
  return this.save();
};

/**
 * 更新热门状态
 */
commentSchema.methods.updateHotStatus = function() {
  // 热门评论的判断标准：点赞数 >= 10 或 回复数 >= 5
  const isHot = this.stats.likesCount >= 10 || this.stats.repliesCount >= 5;
  this.isHot = isHot;
};

/**
 * 编辑评论内容
 */
commentSchema.methods.editContent = function(newContent, reason = '') {
  if (!this.canEdit) {
    throw new Error('Comment can no longer be edited');
  }

  // 保存编辑历史
  this.editHistory.push({
    content: this.content,
    editedAt: new Date(),
    reason: reason
  });

  this.content = newContent;
  this.lastEditedAt = new Date();

  return this.save();
};

/**
 * 软删除评论
 */
commentSchema.methods.softDelete = function() {
  this.status = 'deleted';
  this.content = '[此评论已被删除]';
  return this.save();
};

/**
 * 审核评论
 */
commentSchema.methods.moderate = function(status, reviewerId, reason = '') {
  this.status = status;
  this.moderation.reviewedBy = reviewerId;
  this.moderation.reviewedAt = new Date();
  this.moderation.reviewReason = reason;
  this.moderation.isAutoReview = false;

  return this.save();
};

// 静态方法

/**
 * 获取音乐的评论列表
 */
commentSchema.statics.getMusicComments = function(musicId, options = {}) {
  const {
    page = 1,
    limit = 20,
    sortBy = 'newest',
    status = 'approved',
    includeReplies = false
  } = options;

  let query = { musicId, status };

  // 如果不包含回复，只获取根评论
  if (!includeReplies) {
    query.level = 0;
  }

  let sort = {};
  switch (sortBy) {
    case 'newest':
      sort = { createdAt: -1 };
      break;
    case 'oldest':
      sort = { createdAt: 1 };
      break;
    case 'hot':
      sort = { isPinned: -1, isHot: -1, 'stats.likesCount': -1, createdAt: -1 };
      break;
    case 'likes':
      sort = { 'stats.likesCount': -1, createdAt: -1 };
      break;
    default:
      sort = { createdAt: -1 };
  }

  return this.find(query)
    .populate('author', 'username avatar userGroup')
    .populate('replyToUser', 'username')
    .sort(sort)
    .skip((page - 1) * limit)
    .limit(limit)
    .lean();
};

/**
 * 获取评论的回复列表
 */
commentSchema.statics.getCommentReplies = function(commentId, options = {}) {
  const {
    page = 1,
    limit = 10,
    sortBy = 'oldest',
    status = 'approved'
  } = options;

  let sort = {};
  switch (sortBy) {
    case 'newest':
      sort = { createdAt: -1 };
      break;
    case 'oldest':
      sort = { createdAt: 1 };
      break;
    case 'likes':
      sort = { 'stats.likesCount': -1, createdAt: 1 };
      break;
    default:
      sort = { createdAt: 1 };
  }

  return this.find({
    $or: [
      { parentId: commentId },
      { rootId: commentId }
    ],
    status
  })
    .populate('author', 'username avatar userGroup')
    .populate('replyToUser', 'username')
    .sort(sort)
    .skip((page - 1) * limit)
    .limit(limit)
    .lean();
};

/**
 * 获取用户的评论历史
 */
commentSchema.statics.getUserComments = function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    status = 'approved'
  } = options;

  return this.find({ author: userId, status })
    .populate('musicId', 'title artist')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(limit)
    .lean();
};

/**
 * 获取待审核的评论列表
 */
commentSchema.statics.getPendingComments = function(options = {}) {
  const {
    page = 1,
    limit = 20,
    sortBy = 'newest'
  } = options;

  let sort = {};
  switch (sortBy) {
    case 'newest':
      sort = { createdAt: -1 };
      break;
    case 'oldest':
      sort = { createdAt: 1 };
      break;
    case 'reports':
      sort = { 'stats.reportsCount': -1, createdAt: -1 };
      break;
    default:
      sort = { createdAt: -1 };
  }

  return this.find({ status: 'pending' })
    .populate('author', 'username avatar userGroup')
    .populate('musicId', 'title artist')
    .sort(sort)
    .skip((page - 1) * limit)
    .limit(limit)
    .lean();
};

/**
 * 获取评论统计信息
 */
commentSchema.statics.getCommentStats = function(musicId = null) {
  const matchStage = musicId ? { musicId: new mongoose.Types.ObjectId(musicId) } : {};

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalComments: { $sum: 1 },
        approvedComments: {
          $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
        },
        pendingComments: {
          $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
        },
        rejectedComments: {
          $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
        },
        totalLikes: { $sum: '$stats.likesCount' },
        totalReplies: { $sum: '$stats.repliesCount' },
        totalReports: { $sum: '$stats.reportsCount' },
        avgLikesPerComment: { $avg: '$stats.likesCount' },
        avgRepliesPerComment: { $avg: '$stats.repliesCount' }
      }
    }
  ]);
};

/**
 * 搜索评论
 */
commentSchema.statics.searchComments = function(keyword, options = {}) {
  const {
    page = 1,
    limit = 20,
    status = 'approved',
    musicId = null
  } = options;

  let query = {
    content: { $regex: keyword, $options: 'i' },
    status
  };

  if (musicId) {
    query.musicId = musicId;
  }

  return this.find(query)
    .populate('author', 'username avatar userGroup')
    .populate('musicId', 'title artist')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(limit)
    .lean();
};

/**
 * 获取热门评论
 */
commentSchema.statics.getHotComments = function(options = {}) {
  const {
    page = 1,
    limit = 20,
    musicId = null,
    timeRange = '7d' // 7d, 30d, all
  } = options;

  let query = { status: 'approved', isHot: true };

  if (musicId) {
    query.musicId = musicId;
  }

  // 时间范围过滤
  if (timeRange !== 'all') {
    const days = parseInt(timeRange);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    query.createdAt = { $gte: startDate };
  }

  return this.find(query)
    .populate('author', 'username avatar userGroup')
    .populate('musicId', 'title artist')
    .sort({ 'stats.likesCount': -1, 'stats.repliesCount': -1, createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(limit)
    .lean();
};

/**
 * 批量审核评论
 */
commentSchema.statics.batchModerate = function(commentIds, status, reviewerId, reason = '') {
  return this.updateMany(
    { _id: { $in: commentIds } },
    {
      $set: {
        status,
        'moderation.reviewedBy': reviewerId,
        'moderation.reviewedAt': new Date(),
        'moderation.reviewReason': reason,
        'moderation.isAutoReview': false
      }
    }
  );
};

/**
 * 获取评论树结构
 */
commentSchema.statics.getCommentTree = function(musicId, options = {}) {
  const {
    maxDepth = 3,
    status = 'approved'
  } = options;

  return this.find({
    musicId,
    status,
    level: { $lte: maxDepth }
  })
    .populate('author', 'username avatar userGroup')
    .populate('replyToUser', 'username')
    .sort({ level: 1, createdAt: 1 })
    .lean()
    .then(comments => {
      // 构建树形结构
      const commentMap = new Map();
      const rootComments = [];

      // 先创建所有评论的映射
      comments.forEach(comment => {
        comment.replies = [];
        commentMap.set(comment._id.toString(), comment);
      });

      // 构建父子关系
      comments.forEach(comment => {
        if (comment.parentId) {
          const parent = commentMap.get(comment.parentId.toString());
          if (parent) {
            parent.replies.push(comment);
          }
        } else {
          rootComments.push(comment);
        }
      });

      return rootComments;
    });
};

// 中间件

// 保存前的处理
commentSchema.pre('save', function(next) {
  // 设置根评论ID
  if (this.parentId && !this.rootId) {
    // 如果有父评论但没有根评论ID，需要查找根评论
    this.constructor.findById(this.parentId)
      .then(parentComment => {
        this.rootId = parentComment.rootId || parentComment._id;
        next();
      })
      .catch(next);
  } else if (!this.parentId) {
    // 如果是根评论，设置rootId为自己的ID
    this.rootId = this._id;
    next();
  } else {
    next();
  }
});

// 删除后的处理
commentSchema.post('remove', async function(doc) {
  try {
    // 如果删除的是根评论，删除所有回复
    if (doc.level === 0) {
      await this.constructor.deleteMany({ rootId: doc._id });
    }

    // 更新父评论的回复数
    if (doc.parentId) {
      await this.constructor.findByIdAndUpdate(
        doc.parentId,
        { $inc: { 'stats.repliesCount': -1 } }
      );
    }
  } catch (error) {
    console.error('Error in comment post-remove middleware:', error);
  }
});

module.exports = mongoose.model('Comment', commentSchema);
