const mongoose = require('mongoose');

/**
 * 用户行为记录模型
 * 用于记录用户的音乐播放、收藏、分享等行为，为推荐算法提供数据支持
 */
const userBehaviorSchema = new mongoose.Schema({
  // 用户ID
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },

  // 音乐ID
  musicId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Music',
    required: [true, 'Music ID is required']
  },

  // 行为类型
  actionType: {
    type: String,
    enum: ['play', 'like', 'share', 'download', 'skip', 'complete'],
    required: [true, 'Action type is required']
  },
  
  // 播放时长（秒）
  playDuration: {
    type: Number,
    min: 0,
    default: 0
  },
  
  // 是否完整播放
  isCompleted: {
    type: Boolean,
    default: false
  },
  
  // 播放进度百分比
  playProgress: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  
  // 行为权重（用于推荐算法）
  weight: {
    type: Number,
    default: 1,
    min: 0,
    max: 10
  },
  
  // 设备信息
  deviceInfo: {
    type: String,
    maxlength: 200
  },
  
  // IP地址
  ipAddress: {
    type: String,
    maxlength: 45
  },
  
  // 用户代理
  userAgent: {
    type: String,
    maxlength: 500
  },
  
  // 来源页面
  referrer: {
    type: String,
    maxlength: 500
  },
  
  // 会话ID
  sessionId: {
    type: String,
    maxlength: 100
  },
  
  // 额外元数据
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true,
  collection: 'user_behaviors'
});

// 复合索引
userBehaviorSchema.index({ userId: 1, musicId: 1 });
userBehaviorSchema.index({ userId: 1, actionType: 1 });
userBehaviorSchema.index({ musicId: 1, actionType: 1 });
userBehaviorSchema.index({ userId: 1, createdAt: -1 });
userBehaviorSchema.index({ actionType: 1, createdAt: -1 });

// 实例方法

/**
 * 计算行为权重
 */
userBehaviorSchema.methods.calculateWeight = function() {
  const weights = {
    play: 1,
    like: 3,
    share: 5,
    download: 4,
    skip: -1,
    complete: 2
  };
  
  let baseWeight = weights[this.actionType] || 1;
  
  // 根据播放完成度调整权重
  if (this.actionType === 'play') {
    if (this.isCompleted) {
      baseWeight *= 2;
    } else if (this.playProgress > 80) {
      baseWeight *= 1.5;
    } else if (this.playProgress < 20) {
      baseWeight *= 0.5;
    }
  }
  
  this.weight = Math.max(0, Math.min(10, baseWeight));
  return this.weight;
};

// 静态方法

/**
 * 获取用户的音乐偏好
 */
userBehaviorSchema.statics.getUserPreferences = async function(userId, options = {}) {
  const { limit = 50, minWeight = 1 } = options;
  
  const preferences = await this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        weight: { $gte: minWeight }
      }
    },
    {
      $lookup: {
        from: 'music',
        localField: 'musicId',
        foreignField: '_id',
        as: 'music'
      }
    },
    { $unwind: '$music' },
    {
      $group: {
        _id: {
          genre: '$music.genre',
          artist: '$music.artist'
        },
        totalWeight: { $sum: '$weight' },
        playCount: { $sum: 1 },
        avgWeight: { $avg: '$weight' }
      }
    },
    {
      $sort: { totalWeight: -1 }
    },
    { $limit: limit }
  ]);
  
  return preferences;
};

/**
 * 获取音乐的受欢迎程度
 */
userBehaviorSchema.statics.getMusicPopularity = async function(musicId, period = '30d') {
  const now = new Date();
  let startDate;
  
  switch (period) {
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '90d':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }
  
  const popularity = await this.aggregate([
    {
      $match: {
        musicId: new mongoose.Types.ObjectId(musicId),
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$actionType',
        count: { $sum: 1 },
        totalWeight: { $sum: '$weight' }
      }
    }
  ]);
  
  return popularity;
};

/**
 * 获取用户相似度
 */
userBehaviorSchema.statics.getUserSimilarity = async function(userId1, userId2) {
  const user1Behaviors = await this.find({ userId: userId1 }).select('musicId weight');
  const user2Behaviors = await this.find({ userId: userId2 }).select('musicId weight');
  
  // 计算共同音乐
  const user1Music = new Map();
  user1Behaviors.forEach(b => user1Music.set(b.musicId.toString(), b.weight));
  
  const user2Music = new Map();
  user2Behaviors.forEach(b => user2Music.set(b.musicId.toString(), b.weight));
  
  const commonMusic = [];
  for (const [musicId, weight1] of user1Music) {
    if (user2Music.has(musicId)) {
      commonMusic.push({
        musicId,
        weight1,
        weight2: user2Music.get(musicId)
      });
    }
  }
  
  if (commonMusic.length === 0) return 0;
  
  // 计算余弦相似度
  let dotProduct = 0;
  let norm1 = 0;
  let norm2 = 0;
  
  commonMusic.forEach(({ weight1, weight2 }) => {
    dotProduct += weight1 * weight2;
    norm1 += weight1 * weight1;
    norm2 += weight2 * weight2;
  });
  
  if (norm1 === 0 || norm2 === 0) return 0;
  
  return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
};

/**
 * 记录用户行为
 */
userBehaviorSchema.statics.recordBehavior = async function(userId, musicId, actionType, options = {}) {
  const behavior = new this({
    userId,
    musicId,
    actionType,
    playDuration: options.playDuration || 0,
    isCompleted: options.isCompleted || false,
    playProgress: options.playProgress || 0,
    deviceInfo: options.deviceInfo,
    ipAddress: options.ipAddress,
    userAgent: options.userAgent,
    referrer: options.referrer,
    sessionId: options.sessionId,
    metadata: options.metadata || {}
  });
  
  behavior.calculateWeight();
  await behavior.save();
  
  return behavior;
};

const UserBehavior = mongoose.model('UserBehavior', userBehaviorSchema);

module.exports = UserBehavior;
