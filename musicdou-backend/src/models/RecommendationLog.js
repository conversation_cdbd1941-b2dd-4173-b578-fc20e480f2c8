const mongoose = require('mongoose');

/**
 * 推荐日志模型
 * 记录推荐系统的运行日志，用于效果分析和系统优化
 */
const recommendationLogSchema = new mongoose.Schema({
  // 用户ID
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },

  // 推荐会话ID
  sessionId: {
    type: String,
    required: [true, 'Session ID is required']
  },

  // 推荐类型
  recommendationType: {
    type: String,
    enum: ['personalized', 'similar', 'popular', 'new_discovery', 'genre_based', 'artist_based', 'collaborative'],
    required: [true, 'Recommendation type is required']
  },

  // 事件类型
  eventType: {
    type: String,
    enum: ['request', 'generate', 'display', 'click', 'play', 'complete', 'skip', 'favorite', 'share'],
    required: [true, 'Event type is required']
  },

  // 推荐音乐ID
  musicId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Music',
    default: null
  },
  
  // 推荐位置
  position: {
    type: Number,
    min: 1,
    default: null
  },
  
  // 推荐分数
  score: {
    type: Number,
    min: 0,
    max: 1,
    default: null
  },
  
  // 推荐原因
  reason: {
    type: String,
    maxlength: 200,
    default: null
  },
  
  // 使用的算法
  algorithm: {
    type: String,
    enum: ['collaborative_filtering', 'content_based', 'hybrid', 'popularity', 'random'],
    default: null
  },
  
  // 算法参数
  algorithmParams: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  
  // 用户上下文
  userContext: {
    // 当前时间
    currentTime: {
      type: Date,
      default: Date.now
    },
    
    // 设备信息
    deviceInfo: {
      type: String,
      maxlength: 200,
      default: null
    },
    
    // 用户代理
    userAgent: {
      type: String,
      maxlength: 500,
      default: null
    },
    
    // IP地址
    ipAddress: {
      type: String,
      maxlength: 45,
      default: null
    },
    
    // 来源页面
    referrer: {
      type: String,
      maxlength: 500,
      default: null
    },
    
    // 当前播放的音乐
    currentMusic: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Music',
      default: null
    },
    
    // 当前歌单
    currentPlaylist: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Playlist',
      default: null
    }
  },
  
  // 推荐结果
  recommendationResult: {
    // 推荐结果ID
    resultId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'RecommendationResult',
      default: null
    },
    
    // 推荐数量
    totalRecommendations: {
      type: Number,
      min: 0,
      default: 0
    },
    
    // 生成时间
    generationTime: {
      type: Number, // 毫秒
      min: 0,
      default: 0
    },
    
    // 算法置信度
    confidence: {
      type: Number,
      min: 0,
      max: 1,
      default: 0
    }
  },
  
  // 用户反馈
  userFeedback: {
    // 显式反馈
    explicitFeedback: {
      type: String,
      enum: ['like', 'dislike', 'not_interested', 'already_known'],
      default: null
    },
    
    // 隐式反馈
    implicitFeedback: {
      // 停留时间
      dwellTime: {
        type: Number, // 毫秒
        min: 0,
        default: 0
      },
      
      // 播放时长
      playDuration: {
        type: Number, // 秒
        min: 0,
        default: 0
      },
      
      // 是否完整播放
      isCompleted: {
        type: Boolean,
        default: false
      },
      
      // 是否跳过
      isSkipped: {
        type: Boolean,
        default: false
      }
    }
  },
  
  // 性能指标
  performance: {
    // 响应时间
    responseTime: {
      type: Number, // 毫秒
      min: 0,
      default: 0
    },
    
    // 缓存命中
    cacheHit: {
      type: Boolean,
      default: false
    },
    
    // 数据库查询次数
    dbQueries: {
      type: Number,
      min: 0,
      default: 0
    },
    
    // 内存使用量
    memoryUsage: {
      type: Number, // MB
      min: 0,
      default: 0
    }
  },
  
  // 错误信息
  error: {
    hasError: {
      type: Boolean,
      default: false
    },
    errorType: {
      type: String,
      enum: ['algorithm_error', 'data_error', 'system_error', 'timeout_error'],
      default: null
    },
    errorMessage: {
      type: String,
      maxlength: 500,
      default: null
    },
    errorStack: {
      type: String,
      default: null
    }
  },
  
  // A/B测试信息
  abTest: {
    testId: {
      type: String,
      default: null
    },
    variant: {
      type: String,
      default: null
    },
    controlGroup: {
      type: Boolean,
      default: false
    }
  },
  
  // 时间戳
  timestamp: {
    type: Date,
    default: Date.now
  },
  
  // 元数据
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true,
  collection: 'recommendation_logs'
});

// 复合索引
recommendationLogSchema.index({ userId: 1, timestamp: -1 });
recommendationLogSchema.index({ sessionId: 1, timestamp: -1 });
recommendationLogSchema.index({ recommendationType: 1, eventType: 1, timestamp: -1 });
recommendationLogSchema.index({ musicId: 1, eventType: 1, timestamp: -1 });
recommendationLogSchema.index({ timestamp: -1 });

// TTL索引 - 保留30天的日志
recommendationLogSchema.index({ timestamp: 1 }, { expireAfterSeconds: 30 * 24 * 60 * 60 });

// 静态方法

/**
 * 记录推荐事件
 */
recommendationLogSchema.statics.logEvent = async function(eventData) {
  const log = new this(eventData);
  return log.save();
};

/**
 * 获取用户推荐统计
 */
recommendationLogSchema.statics.getUserStats = async function(userId, timeRange = '7d') {
  const now = new Date();
  let startDate;
  
  switch (timeRange) {
    case '1d':
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  }
  
  return this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        timestamp: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$eventType',
        count: { $sum: 1 },
        avgResponseTime: { $avg: '$performance.responseTime' }
      }
    }
  ]);
};

/**
 * 获取推荐效果分析
 */
recommendationLogSchema.statics.getEffectivenessAnalysis = async function(recommendationType, timeRange = '7d') {
  const now = new Date();
  let startDate;
  
  switch (timeRange) {
    case '1d':
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  }
  
  return this.aggregate([
    {
      $match: {
        recommendationType,
        timestamp: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$eventType',
        count: { $sum: 1 },
        avgScore: { $avg: '$score' },
        avgResponseTime: { $avg: '$performance.responseTime' }
      }
    }
  ]);
};

module.exports = mongoose.model('RecommendationLog', recommendationLogSchema);
