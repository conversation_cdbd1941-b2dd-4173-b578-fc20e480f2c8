const mongoose = require('mongoose');

/**
 * 用户动态模型
 * 记录用户的各种活动，用于生成时间线和社交动态
 */
const activitySchema = new mongoose.Schema({
  // 动态发布者
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User is required'],
    index: true
  },

  // 动态类型
  type: {
    type: String,
    enum: [
      'upload_music',      // 上传音乐
      'create_playlist',   // 创建歌单
      'follow_user',       // 关注用户
      'like_music',        // 点赞音乐
      'like_comment',      // 点赞评论
      'like_playlist',     // 点赞歌单
      'comment_music',     // 评论音乐
      'share_music',       // 分享音乐
      'share_playlist',    // 分享歌单
      'favorite_playlist', // 收藏歌单
      'play_milestone',    // 播放里程碑（如播放1000首歌）
      'achievement',       // 成就获得
      'custom'             // 自定义动态
    ],
    required: [true, 'Activity type is required'],
    index: true
  },

  // 动态标题（自动生成或用户自定义）
  title: {
    type: String,
    required: [true, 'Activity title is required'],
    trim: true,
    maxlength: [200, 'Activity title cannot exceed 200 characters']
  },

  // 动态描述/内容
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Activity description cannot exceed 1000 characters'],
    default: ''
  },

  // 关联的目标对象
  target: {
    // 目标类型
    type: {
      type: String,
      enum: ['music', 'playlist', 'user', 'comment', 'achievement', 'milestone'],
      default: null
    },
    // 目标ID
    id: {
      type: mongoose.Schema.Types.ObjectId,
      default: null,
      refPath: 'target.type'
    },
    // 目标的快照数据（防止目标被删除后动态失效）
    snapshot: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    }
  },

  // 动态隐私设置
  privacy: {
    type: String,
    enum: ['public', 'followers', 'private'],
    default: 'public'
  },

  // 动态来源
  source: {
    type: String,
    enum: ['web', 'mobile', 'api', 'system'],
    default: 'web'
  },

  // 动态状态
  status: {
    type: String,
    enum: ['active', 'hidden', 'deleted'],
    default: 'active'
  },

  // 动态权重（用于时间线排序算法）
  weight: {
    type: Number,
    default: 1.0,
    min: 0,
    max: 10
  },

  // 动态热度分数（基于互动数量和时间衰减）
  hotScore: {
    type: Number,
    default: 0,
    min: 0
  },

  // 统计信息
  stats: {
    // 查看次数
    viewCount: {
      type: Number,
      default: 0,
      min: 0
    },
    // 点赞次数
    likeCount: {
      type: Number,
      default: 0,
      min: 0
    },
    // 评论次数
    commentCount: {
      type: Number,
      default: 0,
      min: 0
    },
    // 分享次数
    shareCount: {
      type: Number,
      default: 0,
      min: 0
    }
  },

  // 动态标签
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Tag cannot exceed 30 characters']
  }],

  // 动态图片（可选）
  images: [{
    objectName: {
      type: String,
      required: true
    },
    bucket: {
      type: String,
      default: 'images'
    },
    url: {
      type: String,
      default: null
    }
  }],

  // 地理位置信息（可选）
  location: {
    name: {
      type: String,
      trim: true,
      maxlength: [100, 'Location name cannot exceed 100 characters']
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      index: '2dsphere'
    }
  },

  // 元数据
  metadata: {
    // IP地址
    ipAddress: {
      type: String,
      default: null
    },
    // 用户代理
    userAgent: {
      type: String,
      default: null
    },
    // 设备信息
    deviceInfo: {
      type: String,
      default: null
    },
    // 是否为移动设备
    isMobile: {
      type: Boolean,
      default: false
    },
    // 额外的自定义数据
    extra: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    }
  },

  // 是否置顶
  isPinned: {
    type: Boolean,
    default: false
  },

  // 置顶到期时间
  pinnedUntil: {
    type: Date,
    default: null
  },

  // 动态过期时间（可选，用于临时动态）
  expiresAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 复合索引优化
activitySchema.index({ user: 1, createdAt: -1 }); // 用户动态时间线
activitySchema.index({ type: 1, createdAt: -1 }); // 按类型查询动态
activitySchema.index({ privacy: 1, status: 1, createdAt: -1 }); // 公开动态查询
activitySchema.index({ hotScore: -1, createdAt: -1 }); // 热门动态排序
activitySchema.index({ 'target.type': 1, 'target.id': 1 }); // 目标对象关联查询
activitySchema.index({ tags: 1 }); // 标签查询
activitySchema.index({ isPinned: 1, pinnedUntil: 1 }); // 置顶动态查询

// TTL索引：自动删除过期动态
activitySchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// 虚拟字段
activitySchema.virtual('isExpired').get(function() {
  return this.expiresAt && this.expiresAt < new Date();
});

activitySchema.virtual('isPinnedActive').get(function() {
  return this.isPinned && (!this.pinnedUntil || this.pinnedUntil > new Date());
});

activitySchema.virtual('totalInteractions').get(function() {
  return this.stats.likeCount + this.stats.commentCount + this.stats.shareCount;
});

// 实例方法
activitySchema.methods.incrementViewCount = function() {
  this.stats.viewCount += 1;
  return this.save();
};

activitySchema.methods.incrementLikeCount = function() {
  this.stats.likeCount += 1;
  this.updateHotScore();
  return this.save();
};

activitySchema.methods.decrementLikeCount = function() {
  this.stats.likeCount = Math.max(0, this.stats.likeCount - 1);
  this.updateHotScore();
  return this.save();
};

activitySchema.methods.incrementCommentCount = function() {
  this.stats.commentCount += 1;
  this.updateHotScore();
  return this.save();
};

activitySchema.methods.incrementShareCount = function() {
  this.stats.shareCount += 1;
  this.updateHotScore();
  return this.save();
};

// 更新热度分数
activitySchema.methods.updateHotScore = function() {
  const now = new Date();
  const ageInHours = (now - this.createdAt) / (1000 * 60 * 60);
  const timeDecay = Math.exp(-ageInHours / 24); // 24小时衰减因子
  
  const interactionScore = (
    this.stats.likeCount * 1 +
    this.stats.commentCount * 2 +
    this.stats.shareCount * 3 +
    this.stats.viewCount * 0.1
  );
  
  this.hotScore = interactionScore * timeDecay * this.weight;
};

// 隐藏动态
activitySchema.methods.hide = function() {
  this.status = 'hidden';
  return this.save();
};

// 删除动态
activitySchema.methods.softDelete = function() {
  this.status = 'deleted';
  return this.save();
};

// 置顶动态
activitySchema.methods.pin = function(duration = null) {
  this.isPinned = true;
  if (duration) {
    this.pinnedUntil = new Date(Date.now() + duration);
  }
  return this.save();
};

// 取消置顶
activitySchema.methods.unpin = function() {
  this.isPinned = false;
  this.pinnedUntil = null;
  return this.save();
};

// 静态方法

// 创建动态
activitySchema.statics.createActivity = async function(data) {
  const activity = new this(data);

  // 自动生成标题（如果没有提供）
  if (!activity.title) {
    activity.title = this.generateTitle(activity.type, activity.target);
  }

  // 计算初始热度分数
  activity.updateHotScore();

  return await activity.save();
};

// 生成动态标题
activitySchema.statics.generateTitle = function(type, target) {
  const titleTemplates = {
    upload_music: '上传了新音乐',
    create_playlist: '创建了新歌单',
    follow_user: '关注了新用户',
    like_music: '点赞了音乐',
    like_comment: '点赞了评论',
    like_playlist: '点赞了歌单',
    comment_music: '评论了音乐',
    share_music: '分享了音乐',
    share_playlist: '分享了歌单',
    favorite_playlist: '收藏了歌单',
    play_milestone: '达成了播放里程碑',
    achievement: '获得了新成就',
    custom: '发布了动态'
  };

  return titleTemplates[type] || '发布了动态';
};

// 获取用户时间线
activitySchema.statics.getUserTimeline = async function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    includeFollowing = true,
    privacy = ['public', 'followers'],
    types = null
  } = options;

  const skip = (page - 1) * limit;

  let query = {
    status: 'active'
  };

  // 如果包含关注的用户动态
  if (includeFollowing) {
    const Follow = mongoose.model('Follow');
    const followingUsers = await Follow.find({
      follower: userId,
      status: 'active'
    }).distinct('following');

    followingUsers.push(userId); // 包含自己的动态
    query.user = { $in: followingUsers };
  } else {
    query.user = userId;
  }

  // 隐私过滤
  if (privacy && privacy.length > 0) {
    query.privacy = { $in: privacy };
  }

  // 类型过滤
  if (types && types.length > 0) {
    query.type = { $in: types };
  }

  const activities = await this.find(query)
    .populate('user', 'username avatar profile.displayName')
    .populate('target.id')
    .sort({
      isPinned: -1,
      hotScore: -1,
      createdAt: -1
    })
    .skip(skip)
    .limit(limit)
    .lean();

  const total = await this.countDocuments(query);

  return {
    activities,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  };
};

// 获取热门动态
activitySchema.statics.getHotActivities = async function(options = {}) {
  const {
    page = 1,
    limit = 20,
    timeRange = 24, // 小时
    types = null
  } = options;

  const skip = (page - 1) * limit;
  const timeThreshold = new Date(Date.now() - timeRange * 60 * 60 * 1000);

  let query = {
    status: 'active',
    privacy: 'public',
    createdAt: { $gte: timeThreshold }
  };

  if (types && types.length > 0) {
    query.type = { $in: types };
  }

  const activities = await this.find(query)
    .populate('user', 'username avatar profile.displayName')
    .populate('target.id')
    .sort({ hotScore: -1, createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .lean();

  const total = await this.countDocuments(query);

  return {
    activities,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  };
};

// 获取用户动态统计
activitySchema.statics.getUserActivityStats = async function(userId, timeRange = 30) {
  const timeThreshold = new Date(Date.now() - timeRange * 24 * 60 * 60 * 1000);

  const stats = await this.aggregate([
    {
      $match: {
        user: mongoose.Types.ObjectId(userId),
        status: 'active',
        createdAt: { $gte: timeThreshold }
      }
    },
    {
      $group: {
        _id: '$type',
        count: { $sum: 1 },
        totalLikes: { $sum: '$stats.likeCount' },
        totalComments: { $sum: '$stats.commentCount' },
        totalShares: { $sum: '$stats.shareCount' },
        totalViews: { $sum: '$stats.viewCount' }
      }
    }
  ]);

  const totalActivities = await this.countDocuments({
    user: userId,
    status: 'active',
    createdAt: { $gte: timeThreshold }
  });

  return {
    totalActivities,
    typeStats: stats,
    timeRange
  };
};

// 批量更新热度分数
activitySchema.statics.updateHotScores = async function(batchSize = 1000) {
  const activities = await this.find({
    status: 'active',
    createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // 只更新7天内的动态
  }).limit(batchSize);

  const bulkOps = activities.map(activity => {
    activity.updateHotScore();
    return {
      updateOne: {
        filter: { _id: activity._id },
        update: { hotScore: activity.hotScore }
      }
    };
  });

  if (bulkOps.length > 0) {
    await this.bulkWrite(bulkOps);
  }

  return bulkOps.length;
};

// 清理过期动态
activitySchema.statics.cleanupExpiredActivities = async function() {
  const result = await this.deleteMany({
    $or: [
      { expiresAt: { $lt: new Date() } },
      {
        status: 'deleted',
        createdAt: { $lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // 删除30天前的已删除动态
      }
    ]
  });

  return result.deletedCount;
};

module.exports = mongoose.model('Activity', activitySchema);
