const express = require('express');
const {
  createPlaylist,
  getPlaylists,
  getPlaylistById,
  updatePlaylist,
  deletePlaylist,
  getUserPlaylists,
  getPopularPlaylists,
  addSongToPlaylist,
  removeSongFromPlaylist,
  reorderPlaylistSongs,
  batchAddSongsToPlaylist,
  favoritePlaylist,
  unfavoritePlaylist,
  getUserFavorites,
  uploadPlaylistCover,
  removePlaylistCover
} = require('../controllers/playlistController');
const {
  authenticateToken,
  optionalAuth
} = require('../middleware/auth');
const { uploadCover } = require('../middleware/upload');

const router = express.Router();

// 公开路由（可选认证）
router.get('/', optionalAuth, getPlaylists);
router.get('/popular', optionalAuth, getPopularPlaylists);
router.get('/favorites', authenticateToken, getUserFavorites);
router.get('/user/:userId', optionalAuth, getUserPlaylists);
router.get('/:id', optionalAuth, getPlaylistById);

// 需要认证的路由
router.post('/', authenticateToken, createPlaylist);
router.put('/:id', authenticateToken, updatePlaylist);
router.delete('/:id', authenticateToken, deletePlaylist);

// 歌曲管理路由
router.post('/:id/songs', authenticateToken, addSongToPlaylist);
router.delete('/:id/songs/:musicId', authenticateToken, removeSongFromPlaylist);
router.put('/:id/songs/reorder', authenticateToken, reorderPlaylistSongs);
router.post('/:id/songs/batch', authenticateToken, batchAddSongsToPlaylist);

// 收藏功能路由
router.post('/:id/favorite', authenticateToken, favoritePlaylist);
router.delete('/:id/favorite', authenticateToken, unfavoritePlaylist);

// 封面上传路由
router.post('/:id/cover', authenticateToken, uploadCover, uploadPlaylistCover);
router.delete('/:id/cover', authenticateToken, removePlaylistCover);

module.exports = router;
