const express = require('express');
const PointRecord = require('../models/PointRecord');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Get user's point records
router.get('/records', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 20, type } = req.query;
    const skip = (page - 1) * limit;
    
    // Build query
    const query = { userId: req.user.userId };
    if (type) {
      query.type = type;
    }
    
    // Get records with pagination
    const records = await PointRecord.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('operatorId', 'username')
      .populate('relatedId');
    
    // Get total count
    const total = await PointRecord.countDocuments(query);
    
    res.status(200).json({
      success: true,
      data: {
        records,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
    
  } catch (error) {
    console.error('Get point records error:', error);
    res.status(500).json({
      error: 'Failed to get point records',
      message: error.message
    });
  }
});

// Get user's point statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const stats = await PointRecord.getUserPointsStats(req.user.userId);
    
    res.status(200).json({
      success: true,
      data: {
        stats,
        totalPoints: req.user.points
      }
    });
    
  } catch (error) {
    console.error('Get point stats error:', error);
    res.status(500).json({
      error: 'Failed to get point statistics',
      message: error.message
    });
  }
});

// Get points leaderboard
router.get('/leaderboard', async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    const leaderboard = await PointRecord.getPointsLeaderboard(parseInt(limit));
    
    res.status(200).json({
      success: true,
      data: {
        leaderboard
      }
    });
    
  } catch (error) {
    console.error('Get leaderboard error:', error);
    res.status(500).json({
      error: 'Failed to get leaderboard',
      message: error.message
    });
  }
});

// Admin: Get system point statistics
router.get('/system-stats', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const stats = await PointRecord.getSystemStats();
    
    res.status(200).json({
      success: true,
      data: {
        stats: stats[0] || {}
      }
    });
    
  } catch (error) {
    console.error('Get system stats error:', error);
    res.status(500).json({
      error: 'Failed to get system statistics',
      message: error.message
    });
  }
});

// Admin: Manually adjust user points
router.post('/adjust', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId, points, description, type = 'admin_adjust' } = req.body;
    
    if (!userId || !points || !description) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'userId, points, and description are required'
      });
    }
    
    // Create point record
    const record = await PointRecord.createRecord({
      userId,
      type,
      points: parseInt(points),
      description,
      operatorId: req.user.userId
    });
    
    res.status(200).json({
      success: true,
      message: 'Points adjusted successfully',
      data: {
        record
      }
    });
    
  } catch (error) {
    console.error('Adjust points error:', error);
    res.status(500).json({
      error: 'Failed to adjust points',
      message: error.message
    });
  }
});

// Get available point types
router.get('/types', (req, res) => {
  const types = [
    { value: 'register', label: '注册奖励', description: '用户注册时获得的奖励积分' },
    { value: 'daily_signin', label: '每日签到', description: '每日签到获得的积分' },
    { value: 'upload_music', label: '上传音乐', description: '上传音乐文件获得的积分' },
    { value: 'share_playlist', label: '分享歌单', description: '分享歌单获得的积分' },
    { value: 'invite_friend', label: '邀请好友', description: '邀请好友注册获得的积分' },
    { value: 'vip_purchase', label: 'VIP购买', description: '购买VIP会员消费的积分' },
    { value: 'admin_adjust', label: '管理员调整', description: '管理员手动调整的积分' },
    { value: 'system_reward', label: '系统奖励', description: '系统活动奖励的积分' },
    { value: 'penalty', label: '违规扣分', description: '违规行为扣除的积分' }
  ];
  
  res.status(200).json({
    success: true,
    data: {
      types
    }
  });
});

module.exports = router;
