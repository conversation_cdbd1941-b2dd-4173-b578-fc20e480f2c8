const express = require('express');
const {
  getUserSettings,
  updateUserSettings,
  resetUserSettings
} = require('../controllers/settingsController');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// 所有设置路由都需要认证
router.use(authenticateToken);

// 获取用户设置
router.get('/', getUserSettings);

// 更新用户设置
router.put('/', updateUserSettings);

// 重置设置为默认值
router.put('/reset', resetUserSettings);

module.exports = router;
