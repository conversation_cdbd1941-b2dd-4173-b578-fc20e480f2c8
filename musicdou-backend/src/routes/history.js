const express = require('express');
const {
  getPlayHistory,
  getRecentlyPlayed,
  getPlayStats,
  deletePlayHistory,
  batchDeletePlayHistory,
  getPlayHistoryDetail,
  getPlayHistoryChart
} = require('../controllers/historyController');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// 所有历史记录相关路由都需要认证
router.use(authenticateToken);

// 播放历史查询
router.get('/', getPlayHistory);                    // 获取播放历史列表
router.get('/recent', getRecentlyPlayed);           // 获取最近播放
router.get('/stats', getPlayStats);                 // 获取播放统计
router.get('/chart', getPlayHistoryChart);          // 获取图表数据
router.get('/:historyId', getPlayHistoryDetail);    // 获取历史记录详情

// 播放历史管理
router.delete('/:historyId', deletePlayHistory);    // 删除单个历史记录
router.delete('/', batchDeletePlayHistory);         // 批量删除历史记录

module.exports = router;
