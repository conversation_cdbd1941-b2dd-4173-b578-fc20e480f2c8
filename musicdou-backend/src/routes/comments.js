const express = require('express');
const router = express.Router();
const {
  createComment,
  getMusicComments,
  getCommentReplies,
  getCommentById,
  updateComment,
  deleteComment,
  likeComment,
  unlikeComment,
  reportComment,
  getUserComments,
  getHotComments,
  searchComments,
  getPendingComments,
  moderateComment,
  batchModerateComments,
  getCommentStats
} = require('../controllers/commentController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

// 应用认证中间件到所有路由
router.use(authenticateToken);

/**
 * @route   GET /api/v1/comments/search
 * @desc    搜索评论
 * @access  Private
 * @query   { keyword: string, page?: number, limit?: number, musicId?: string }
 */
router.get('/search', searchComments);

/**
 * @route   GET /api/v1/comments/hot
 * @desc    获取热门评论
 * @access  Private
 * @query   { page?: number, limit?: number, musicId?: string, timeRange?: string }
 */
router.get('/hot', getHotComments);

/**
 * @route   GET /api/v1/comments/stats
 * @desc    获取评论统计信息
 * @access  Private
 * @query   { musicId?: string }
 */
router.get('/stats', getCommentStats);

/**
 * @route   GET /api/v1/comments/admin/pending
 * @desc    获取待审核评论列表（管理员功能）
 * @access  Admin
 * @query   { page?: number, limit?: number, sortBy?: string }
 */
router.get('/admin/pending', requireAdmin, getPendingComments);

/**
 * @route   PUT /api/v1/comments/admin/batch-moderate
 * @desc    批量审核评论（管理员功能）
 * @access  Admin
 * @body    { commentIds: string[], status: string, reason?: string }
 */
router.put('/admin/batch-moderate', requireAdmin, batchModerateComments);

/**
 * @route   GET /api/v1/comments/music/:musicId
 * @desc    获取音乐评论列表
 * @access  Private
 * @query   { page?: number, limit?: number, sortBy?: string, includeReplies?: boolean }
 */
router.get('/music/:musicId', getMusicComments);

/**
 * @route   GET /api/v1/comments/user/:userId
 * @desc    获取用户评论历史
 * @access  Private
 * @query   { page?: number, limit?: number }
 */
router.get('/user/:userId', getUserComments);

/**
 * @route   POST /api/v1/comments
 * @desc    发布评论
 * @access  Private
 * @body    { content: string, musicId: string, parentId?: string, replyToUserId?: string }
 */
router.post('/', createComment);

/**
 * @route   GET /api/v1/comments/:id
 * @desc    获取评论详情
 * @access  Private
 */
router.get('/:id', getCommentById);

/**
 * @route   PUT /api/v1/comments/:id
 * @desc    编辑评论
 * @access  Private (Own comments only)
 * @body    { content: string, reason?: string }
 */
router.put('/:id', updateComment);

/**
 * @route   DELETE /api/v1/comments/:id
 * @desc    删除评论
 * @access  Private (Own comments only or Admin)
 */
router.delete('/:id', deleteComment);

/**
 * @route   GET /api/v1/comments/:commentId/replies
 * @desc    获取评论回复列表
 * @access  Private
 * @query   { page?: number, limit?: number, sortBy?: string }
 */
router.get('/:commentId/replies', getCommentReplies);

/**
 * @route   POST /api/v1/comments/:id/like
 * @desc    点赞评论
 * @access  Private
 */
router.post('/:id/like', likeComment);

/**
 * @route   DELETE /api/v1/comments/:id/like
 * @desc    取消点赞评论
 * @access  Private
 */
router.delete('/:id/like', unlikeComment);

/**
 * @route   POST /api/v1/comments/:id/report
 * @desc    举报评论
 * @access  Private
 * @body    { reason: string }
 */
router.post('/:id/report', reportComment);

/**
 * @route   PUT /api/v1/comments/:id/moderate
 * @desc    审核评论（管理员功能）
 * @access  Admin
 * @body    { status: string, reason?: string }
 */
router.put('/:id/moderate', requireAdmin, moderateComment);

module.exports = router;
