const express = require('express');
const {
  getPopularMusic,
  getUserBehaviorAnalysis,
  getSystemStats,
  getMusicStats,
  getUserLeaderboard,
  getArtistStats,
  generatePlayReport
} = require('../controllers/statsController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// 公开统计接口（需要认证）
router.get('/popular', authenticateToken, getPopularMusic);        // 热门音乐排行
router.get('/artists', authenticateToken, getArtistStats);         // 艺术家统计排行

// 用户个人统计接口
router.get('/user/behavior', authenticateToken, getUserBehaviorAnalysis);  // 用户行为分析
router.get('/user/report', authenticateToken, generatePlayReport);         // 生成个人播放报告

// 音乐详细统计
router.get('/music/:musicId', authenticateToken, getMusicStats);   // 音乐详细统计

// 系统统计接口（管理员权限）
router.get('/system', authenticateToken, requireAdmin, getSystemStats);        // 系统统计
router.get('/leaderboard', authenticateToken, requireAdmin, getUserLeaderboard); // 用户排行榜

module.exports = router;
