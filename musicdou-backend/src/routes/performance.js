const express = require('express');
const router = express.Router();
const PerformanceController = require('../controllers/performanceController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

/**
 * 性能监控路由
 * 提供系统性能监控和分析的API接口
 */

/**
 * @route GET /api/v1/performance/overview
 * @desc 获取系统性能概览
 * @access Admin
 */
router.get('/overview', authenticateToken, requireAdmin, PerformanceController.getSystemOverview);

/**
 * @route GET /api/v1/performance/report
 * @desc 获取详细性能报告
 * @access Admin
 */
router.get('/report', authenticateToken, requireAdmin, PerformanceController.getDetailedReport);

/**
 * @route GET /api/v1/performance/requests
 * @desc 获取请求统计
 * @access Admin
 */
router.get('/requests', authenticateToken, requireAdmin, PerformanceController.getRequestStats);

/**
 * @route GET /api/v1/performance/cache
 * @desc 获取缓存统计
 * @access Admin
 */
router.get('/cache', authenticateToken, requireAdmin, PerformanceController.getCacheStats);

/**
 * @route GET /api/v1/performance/database
 * @desc 获取数据库统计
 * @access Admin
 */
router.get('/database', authenticateToken, requireAdmin, PerformanceController.getDatabaseStats);

/**
 * @route GET /api/v1/performance/slow-queries
 * @desc 获取慢查询记录
 * @access Admin
 * @query {number} days - 查询天数，默认1天
 */
router.get('/slow-queries', authenticateToken, requireAdmin, PerformanceController.getSlowQueries);

/**
 * @route GET /api/v1/performance/memory-trend
 * @desc 获取内存使用趋势
 * @access Admin
 * @query {number} hours - 查询小时数，默认24小时
 */
router.get('/memory-trend', authenticateToken, requireAdmin, PerformanceController.getMemoryTrend);

/**
 * @route GET /api/v1/performance/health
 * @desc 获取系统健康状态
 * @access Public (用于健康检查)
 */
router.get('/health', PerformanceController.getHealthStatus);

/**
 * @route GET /api/v1/performance/suggestions
 * @desc 获取性能优化建议
 * @access Admin
 */
router.get('/suggestions', authenticateToken, requireAdmin, PerformanceController.getOptimizationSuggestions);

/**
 * @route POST /api/v1/performance/cache/clear
 * @desc 清理缓存
 * @access Admin
 * @body {string} pattern - 可选，清理匹配模式的缓存
 */
router.post('/cache/clear', authenticateToken, requireAdmin, PerformanceController.clearCache);

/**
 * @route POST /api/v1/performance/stats/reset
 * @desc 重置性能统计
 * @access Admin
 */
router.post('/stats/reset', authenticateToken, requireAdmin, PerformanceController.resetStats);

module.exports = router;
