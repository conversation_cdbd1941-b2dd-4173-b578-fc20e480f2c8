const express = require('express');
const {
  getQueue,
  addToQueue,
  addMultipleToQueue,
  removeFromQueue,
  clearQueue,
  reorderQueue,
  createQueueFromPlaylist,
  jumpToPosition,
  getQueueStats
} = require('../controllers/queueController');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// 所有队列相关路由都需要认证
router.use(authenticateToken);

// 队列基础操作
router.get('/', getQueue);                          // 获取播放队列
router.post('/add', addToQueue);                    // 添加歌曲到队列
router.post('/add-multiple', addMultipleToQueue);   // 批量添加歌曲到队列
router.delete('/remove/:position', removeFromQueue); // 从队列移除歌曲
router.delete('/clear', clearQueue);                // 清空队列

// 队列管理操作
router.post('/reorder', reorderQueue);              // 重新排序队列
router.post('/jump', jumpToPosition);               // 跳转到指定位置
router.post('/from-playlist', createQueueFromPlaylist); // 从歌单创建队列

// 队列信息
router.get('/stats', getQueueStats);                // 获取队列统计信息

module.exports = router;
