const mongoose = require('mongoose');

/**
 * 数据库优化工具
 * 提供数据库性能优化和维护功能
 */
class DatabaseOptimizer {
  /**
   * 创建所有必要的索引
   */
  static async createIndexes() {
    try {
      console.log('🔧 Creating database indexes...');
      
      const db = mongoose.connection.db;
      
      // User集合索引
      await db.collection('users').createIndex({ email: 1 }, { unique: true });
      await db.collection('users').createIndex({ username: 1 }, { unique: true });
      await db.collection('users').createIndex({ userGroup: 1 });
      await db.collection('users').createIndex({ createdAt: -1 });
      await db.collection('users').createIndex({ lastLoginAt: -1 });
      await db.collection('users').createIndex({ points: -1 });
      
      // Music集合索引
      await db.collection('music').createIndex({ title: 'text', artist: 'text', album: 'text' });
      await db.collection('music').createIndex({ artist: 1 });
      await db.collection('music').createIndex({ album: 1 });
      await db.collection('music').createIndex({ genre: 1 });
      await db.collection('music').createIndex({ uploadedBy: 1 });
      await db.collection('music').createIndex({ status: 1 });
      await db.collection('music').createIndex({ quality: 1 });
      await db.collection('music').createIndex({ createdAt: -1 });
      await db.collection('music').createIndex({ playCount: -1 });
      await db.collection('music').createIndex({ favoriteCount: -1 });
      await db.collection('music').createIndex({ status: 1, createdAt: -1 });
      
      // Playlist集合索引
      await db.collection('playlists').createIndex({ createdBy: 1 });
      await db.collection('playlists').createIndex({ name: 'text', description: 'text' });
      await db.collection('playlists').createIndex({ isPublic: 1, createdAt: -1 });
      await db.collection('playlists').createIndex({ playCount: -1 });
      await db.collection('playlists').createIndex({ favoriteCount: -1 });
      await db.collection('playlists').createIndex({ category: 1, isPublic: 1 });
      await db.collection('playlists').createIndex({ 'songs.musicId': 1 });
      await db.collection('playlists').createIndex({ tags: 1 });
      
      // Follow集合索引
      await db.collection('follows').createIndex({ follower: 1, following: 1 }, { unique: true });
      await db.collection('follows').createIndex({ follower: 1, status: 1, createdAt: -1 });
      await db.collection('follows').createIndex({ following: 1, status: 1, createdAt: -1 });
      await db.collection('follows').createIndex({ isMutual: 1, status: 1 });
      
      // Comment集合索引
      await db.collection('comments').createIndex({ musicId: 1, status: 1, createdAt: -1 });
      await db.collection('comments').createIndex({ author: 1, createdAt: -1 });
      await db.collection('comments').createIndex({ parentId: 1, createdAt: 1 });
      await db.collection('comments').createIndex({ rootId: 1, level: 1, createdAt: 1 });
      await db.collection('comments').createIndex({ status: 1, createdAt: -1 });
      await db.collection('comments').createIndex({ isPinned: -1, isHot: -1, 'stats.likesCount': -1 });
      await db.collection('comments').createIndex({ 'analysis.hasSensitiveWords': 1, status: 1 });
      
      // Like集合索引
      await db.collection('likes').createIndex({ user: 1, createdAt: -1 });
      await db.collection('likes').createIndex({ targetType: 1, targetId: 1, status: 1 });
      await db.collection('likes').createIndex({ user: 1, targetType: 1, targetId: 1 }, { unique: true });
      await db.collection('likes').createIndex({ status: 1, createdAt: -1 });
      
      // Activity集合索引
      await db.collection('activities').createIndex({ user: 1, createdAt: -1 });
      await db.collection('activities').createIndex({ type: 1, createdAt: -1 });
      await db.collection('activities').createIndex({ privacy: 1, status: 1, createdAt: -1 });
      await db.collection('activities').createIndex({ hotScore: -1, createdAt: -1 });
      await db.collection('activities').createIndex({ 'target.type': 1, 'target.id': 1 });
      await db.collection('activities').createIndex({ tags: 1 });
      await db.collection('activities').createIndex({ isPinned: 1, pinnedUntil: 1 });
      await db.collection('activities').createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });
      
      // Notification集合索引
      await db.collection('notifications').createIndex({ recipient: 1, status: 1, createdAt: -1 });
      await db.collection('notifications').createIndex({ recipient: 1, type: 1, status: 1 });
      await db.collection('notifications').createIndex({ recipient: 1, group: 1, status: 1 });
      await db.collection('notifications').createIndex({ sender: 1, type: 1, createdAt: -1 });
      await db.collection('notifications').createIndex({ type: 1, priority: 1, createdAt: -1 });
      await db.collection('notifications').createIndex({ status: 1, priority: 1, createdAt: -1 });
      await db.collection('notifications').createIndex({ 'metadata.batchId': 1 });
      await db.collection('notifications').createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });
      
      // UserBehavior集合索引
      await db.collection('user_behaviors').createIndex({ userId: 1, musicId: 1 });
      await db.collection('user_behaviors').createIndex({ userId: 1, actionType: 1 });
      await db.collection('user_behaviors').createIndex({ musicId: 1, actionType: 1 });
      await db.collection('user_behaviors').createIndex({ userId: 1, createdAt: -1 });
      await db.collection('user_behaviors').createIndex({ actionType: 1, createdAt: -1 });
      
      // PlayHistory集合索引
      await db.collection('playhistories').createIndex({ userId: 1, createdAt: -1 });
      await db.collection('playhistories').createIndex({ musicId: 1, createdAt: -1 });
      await db.collection('playhistories').createIndex({ userId: 1, musicId: 1 });
      await db.collection('playhistories').createIndex({ sessionId: 1 });
      
      console.log('✅ Database indexes created successfully');
    } catch (error) {
      console.error('❌ Error creating indexes:', error);
      throw error;
    }
  }

  /**
   * 分析集合统计信息
   */
  static async analyzeCollections() {
    try {
      console.log('📊 Analyzing collection statistics...');
      
      const db = mongoose.connection.db;
      const collections = await db.listCollections().toArray();
      const stats = {};
      
      for (const collection of collections) {
        const collName = collection.name;
        try {
          const collStats = await db.collection(collName).stats();
          stats[collName] = {
            count: collStats.count,
            size: collStats.size,
            avgObjSize: collStats.avgObjSize,
            storageSize: collStats.storageSize,
            indexes: collStats.nindexes,
            indexSize: collStats.totalIndexSize
          };
        } catch (error) {
          console.warn(`Warning: Could not get stats for collection ${collName}`);
        }
      }
      
      return stats;
    } catch (error) {
      console.error('❌ Error analyzing collections:', error);
      throw error;
    }
  }

  /**
   * 获取索引使用情况
   */
  static async getIndexUsage() {
    try {
      console.log('📈 Analyzing index usage...');
      
      const db = mongoose.connection.db;
      const collections = ['users', 'music', 'playlists', 'follows', 'comments', 'likes', 'activities', 'notifications'];
      const indexUsage = {};
      
      for (const collName of collections) {
        try {
          const indexes = await db.collection(collName).indexStats().toArray();
          indexUsage[collName] = indexes.map(index => ({
            name: index.name,
            accesses: index.accesses,
            since: index.accesses.since
          }));
        } catch (error) {
          console.warn(`Warning: Could not get index stats for collection ${collName}`);
        }
      }
      
      return indexUsage;
    } catch (error) {
      console.error('❌ Error getting index usage:', error);
      throw error;
    }
  }

  /**
   * 清理过期数据
   */
  static async cleanupExpiredData() {
    try {
      console.log('🧹 Cleaning up expired data...');
      
      const db = mongoose.connection.db;
      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      
      // 清理过期的播放历史（保留30天）
      const playHistoryResult = await db.collection('playhistories').deleteMany({
        createdAt: { $lt: thirtyDaysAgo }
      });
      
      // 清理过期的用户行为记录（保留30天）
      const behaviorResult = await db.collection('user_behaviors').deleteMany({
        createdAt: { $lt: thirtyDaysAgo }
      });
      
      // 清理过期的通知（保留7天）
      const notificationResult = await db.collection('notifications').deleteMany({
        status: 'read',
        createdAt: { $lt: sevenDaysAgo }
      });
      
      // 清理过期的推荐结果缓存
      const recommendationResult = await db.collection('recommendationresults').deleteMany({
        expiresAt: { $lt: now }
      });
      
      console.log(`✅ Cleanup completed:
        - Play history: ${playHistoryResult.deletedCount} records
        - User behaviors: ${behaviorResult.deletedCount} records  
        - Notifications: ${notificationResult.deletedCount} records
        - Recommendations: ${recommendationResult.deletedCount} records`);
      
      return {
        playHistory: playHistoryResult.deletedCount,
        userBehaviors: behaviorResult.deletedCount,
        notifications: notificationResult.deletedCount,
        recommendations: recommendationResult.deletedCount
      };
    } catch (error) {
      console.error('❌ Error cleaning up expired data:', error);
      throw error;
    }
  }

  /**
   * 优化数据库性能
   */
  static async optimizeDatabase() {
    try {
      console.log('⚡ Optimizing database performance...');
      
      // 创建索引
      await this.createIndexes();
      
      // 清理过期数据
      await this.cleanupExpiredData();
      
      // 分析集合统计
      const stats = await this.analyzeCollections();
      
      console.log('✅ Database optimization completed');
      return stats;
    } catch (error) {
      console.error('❌ Database optimization failed:', error);
      throw error;
    }
  }

  /**
   * 获取数据库性能报告
   */
  static async getPerformanceReport() {
    try {
      const stats = await this.analyzeCollections();
      const indexUsage = await this.getIndexUsage();
      
      // 计算总体统计
      const totalDocuments = Object.values(stats).reduce((sum, stat) => sum + stat.count, 0);
      const totalSize = Object.values(stats).reduce((sum, stat) => sum + stat.size, 0);
      const totalIndexSize = Object.values(stats).reduce((sum, stat) => sum + stat.indexSize, 0);
      
      return {
        timestamp: new Date().toISOString(),
        summary: {
          totalCollections: Object.keys(stats).length,
          totalDocuments,
          totalSize,
          totalIndexSize,
          indexSizeRatio: ((totalIndexSize / totalSize) * 100).toFixed(2) + '%'
        },
        collections: stats,
        indexUsage
      };
    } catch (error) {
      console.error('❌ Error generating performance report:', error);
      throw error;
    }
  }
}

module.exports = DatabaseOptimizer;
