{"openapi": "3.0.3", "info": {"title": "MusicDou 用户认证模块 API", "description": "MusicDou音乐平台用户认证相关API，包括用户注册、登录、个人资料管理、每日签到等功能", "version": "1.0.0", "contact": {"name": "MusicDou API Support", "url": "http://localhost:3000"}}, "servers": [{"url": "http://localhost:3000/api/v1", "description": "开发环境服务器"}], "tags": [{"name": "认证", "description": "用户认证相关操作"}, {"name": "用户资料", "description": "用户个人资料管理"}, {"name": "积分系统", "description": "用户积分和签到功能"}], "paths": {"/auth/register": {"post": {"tags": ["认证"], "summary": "用户注册", "description": "创建新用户账户，自动获得100积分注册奖励", "operationId": "registerUser", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["username", "email", "password"], "properties": {"username": {"type": "string", "minLength": 3, "maxLength": 30, "pattern": "^[a-zA-Z0-9_]+$", "description": "用户名，3-30个字符，只能包含字母、数字和下划线"}, "email": {"type": "string", "format": "email", "description": "邮箱地址"}, "password": {"type": "string", "minLength": 6, "description": "密码，至少6个字符"}}}}}}, "responses": {"201": {"description": "注册成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "User registered successfully"}, "data": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}, "token": {"type": "string", "description": "JWT访问令牌"}, "expiresIn": {"type": "string", "example": "7d", "description": "令牌过期时间"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "409": {"$ref": "#/components/responses/Conflict"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/auth/login": {"post": {"tags": ["认证"], "summary": "用户登录", "description": "使用用户名或邮箱登录，支持账户锁定机制", "operationId": "loginUser", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["identifier", "password"], "properties": {"identifier": {"type": "string", "description": "用户名或邮箱地址"}, "password": {"type": "string", "description": "用户密码"}}}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Login successful"}, "data": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}, "token": {"type": "string", "description": "JWT访问令牌"}, "expiresIn": {"type": "string", "example": "7d", "description": "令牌过期时间"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "423": {"description": "账户被锁定", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Account locked", "message": "Account is temporarily locked due to too many failed login attempts"}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/auth/profile": {"get": {"tags": ["用户资料"], "summary": "获取当前用户信息", "description": "获取当前登录用户的详细信息，包括积分、签到状态等", "operationId": "getUserProfile", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Profile retrieved successfully"}, "data": {"$ref": "#/components/schemas/User"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/auth/signin": {"post": {"tags": ["积分系统"], "summary": "每日签到", "description": "用户每日签到获取积分奖励，支持连续签到奖励机制", "operationId": "dailySignIn", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "签到成功或已签到", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Daily sign-in successful"}, "data": {"type": "object", "properties": {"alreadySignedIn": {"type": "boolean", "description": "是否已经签到过"}, "pointsEarned": {"type": "number", "description": "本次签到获得的积分"}, "bonusPoints": {"type": "number", "description": "连续签到奖励积分"}, "totalPoints": {"type": "number", "description": "用户总积分"}, "consecutiveDays": {"type": "number", "description": "连续签到天数"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/auth/logout": {"post": {"tags": ["认证"], "summary": "用户登出", "description": "用户登出系统（为将来的token黑名单功能预留）", "operationId": "logoutUser", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "登出成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Logout successful"}}}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/auth/test": {"get": {"tags": ["认证"], "summary": "测试认证状态", "description": "测试用户认证是否有效", "operationId": "testAuth", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "认证有效", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Authentication successful"}, "user": {"$ref": "#/components/schemas/UserBasic"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT认证令牌"}}, "schemas": {"User": {"type": "object", "properties": {"_id": {"type": "string", "description": "用户唯一标识符"}, "username": {"type": "string", "description": "用户名"}, "email": {"type": "string", "format": "email", "description": "邮箱地址"}, "userGroup": {"type": "string", "enum": ["normal", "vip", "admin"], "description": "用户组"}, "points": {"type": "number", "description": "用户积分"}, "avatar": {"type": "string", "nullable": true, "description": "头像URL"}, "bio": {"type": "string", "nullable": true, "description": "个人简介"}, "isActive": {"type": "boolean", "description": "账户是否激活"}, "isEmailVerified": {"type": "boolean", "description": "邮箱是否已验证"}, "consecutiveSignInDays": {"type": "number", "description": "连续签到天数"}, "lastSignInDate": {"type": "string", "format": "date-time", "nullable": true, "description": "最后签到日期"}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true, "description": "最后登录时间"}, "createdAt": {"type": "string", "format": "date-time", "description": "账户创建时间"}, "updatedAt": {"type": "string", "format": "date-time", "description": "账户更新时间"}}}, "UserBasic": {"type": "object", "properties": {"_id": {"type": "string", "description": "用户唯一标识符"}, "userId": {"type": "string", "description": "用户ID"}, "username": {"type": "string", "description": "用户名"}, "email": {"type": "string", "format": "email", "description": "邮箱地址"}, "userGroup": {"type": "string", "enum": ["normal", "vip", "admin"], "description": "用户组"}, "points": {"type": "number", "description": "用户积分"}}}, "ErrorResponse": {"type": "object", "properties": {"error": {"type": "string", "description": "错误类型"}, "message": {"type": "string", "description": "错误描述"}}}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Validation error", "message": "Username and email are required"}}}}, "Unauthorized": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Access denied", "message": "No token provided"}}}}, "Forbidden": {"description": "访问被禁止", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Account disabled", "message": "Your account has been disabled"}}}}, "NotFound": {"description": "资源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "User not found", "message": "User not found"}}}}, "Conflict": {"description": "资源冲突", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "User already exists", "message": "Username or email already exists"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Internal Server Error", "message": "An error occurred during processing"}}}}}}}