{"openapi": "3.0.3", "info": {"title": "MusicDou 统计分析模块 API", "description": "MusicDou音乐平台统计分析相关API，包括用户统计、音乐统计、系统监控等功能", "version": "1.0.0", "contact": {"name": "MusicDou API Support", "url": "http://localhost:3000"}}, "servers": [{"url": "http://localhost:3000/api/v1", "description": "开发环境服务器"}], "tags": [{"name": "用户统计", "description": "用户行为和数据统计"}, {"name": "音乐统计", "description": "音乐播放和互动统计"}, {"name": "系统统计", "description": "系统整体数据统计"}], "paths": {"/stats/user/{userId}": {"get": {"tags": ["用户统计"], "summary": "获取用户统计数据", "description": "获取指定用户的统计数据", "operationId": "getUserStats", "security": [{"bearerAuth": []}], "parameters": [{"name": "userId", "in": "path", "required": true, "description": "用户ID", "schema": {"type": "string"}}, {"name": "period", "in": "query", "description": "统计周期", "schema": {"type": "string", "enum": ["daily", "weekly", "monthly", "yearly", "all"], "default": "monthly"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "User stats retrieved successfully"}, "data": {"$ref": "#/components/schemas/UserStats"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/stats/music/{musicId}": {"get": {"tags": ["音乐统计"], "summary": "获取音乐统计数据", "description": "获取指定音乐的统计数据", "operationId": "getMusicStats", "security": [{"bearerAuth": []}], "parameters": [{"name": "musicId", "in": "path", "required": true, "description": "音乐ID", "schema": {"type": "string"}}, {"name": "period", "in": "query", "description": "统计周期", "schema": {"type": "string", "enum": ["daily", "weekly", "monthly", "yearly", "all"], "default": "monthly"}}, {"name": "breakdown", "in": "query", "description": "是否包含详细分解数据", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Music stats retrieved successfully"}, "data": {"$ref": "#/components/schemas/MusicStats"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/stats/system/overview": {"get": {"tags": ["系统统计"], "summary": "获取系统概览统计", "description": "获取系统整体统计数据概览", "operationId": "getSystemOverview", "security": [{"bearerAuth": []}], "parameters": [{"name": "period", "in": "query", "description": "统计周期", "schema": {"type": "string", "enum": ["daily", "weekly", "monthly", "yearly"], "default": "monthly"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "System overview retrieved successfully"}, "data": {"$ref": "#/components/schemas/SystemOverview"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/stats/trending": {"get": {"tags": ["音乐统计"], "summary": "获取趋势统计", "description": "获取音乐和用户的趋势统计数据", "operationId": "getTrendingStats", "security": [{"bearerAuth": []}], "parameters": [{"name": "type", "in": "query", "description": "趋势类型", "schema": {"type": "string", "enum": ["music", "artists", "genres", "playlists"], "default": "music"}}, {"name": "period", "in": "query", "description": "统计周期", "schema": {"type": "string", "enum": ["daily", "weekly", "monthly"], "default": "weekly"}}, {"name": "limit", "in": "query", "description": "返回数量", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Trending stats retrieved successfully"}, "data": {"$ref": "#/components/schemas/TrendingStats"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/stats/analytics/report": {"post": {"tags": ["系统统计"], "summary": "生成分析报告", "description": "生成自定义分析报告", "operationId": "generateAnalyticsReport", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["reportType", "date<PERSON><PERSON><PERSON>"], "properties": {"reportType": {"type": "string", "enum": ["user_activity", "music_performance", "system_health", "revenue"], "description": "报告类型"}, "dateRange": {"type": "object", "required": ["startDate", "endDate"], "properties": {"startDate": {"type": "string", "format": "date", "description": "开始日期"}, "endDate": {"type": "string", "format": "date", "description": "结束日期"}}}, "filters": {"type": "object", "properties": {"userGroups": {"type": "array", "items": {"type": "string"}, "description": "用户组过滤"}, "genres": {"type": "array", "items": {"type": "string"}, "description": "音乐流派过滤"}, "regions": {"type": "array", "items": {"type": "string"}, "description": "地区过滤"}}, "description": "过滤条件"}, "format": {"type": "string", "enum": ["json", "csv", "pdf"], "default": "json", "description": "报告格式"}}}}}}, "responses": {"200": {"description": "报告生成成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Analytics report generated successfully"}, "data": {"$ref": "#/components/schemas/AnalyticsReport"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT认证令牌"}}, "schemas": {"UserStats": {"type": "object", "properties": {"userId": {"type": "string", "description": "用户ID"}, "period": {"type": "string", "description": "统计周期"}, "listening": {"type": "object", "properties": {"totalPlayTime": {"type": "number", "description": "总播放时长（分钟）"}, "totalPlays": {"type": "number", "description": "总播放次数"}, "uniqueSongs": {"type": "number", "description": "播放的不同歌曲数"}, "averageSessionLength": {"type": "number", "description": "平均会话时长（分钟）"}, "topGenres": {"type": "array", "items": {"type": "object", "properties": {"genre": {"type": "string"}, "playCount": {"type": "number"}, "percentage": {"type": "number"}}}}}}, "social": {"type": "object", "properties": {"playlistsCreated": {"type": "number"}, "songsLiked": {"type": "number"}, "commentsPosted": {"type": "number"}, "sharesCount": {"type": "number"}, "followersGained": {"type": "number"}, "followingCount": {"type": "number"}}}, "achievements": {"type": "object", "properties": {"pointsEarned": {"type": "number"}, "streakDays": {"type": "number"}, "badgesUnlocked": {"type": "array", "items": {"type": "string"}}}}}}, "MusicStats": {"type": "object", "properties": {"musicId": {"type": "string", "description": "音乐ID"}, "period": {"type": "string", "description": "统计周期"}, "plays": {"type": "object", "properties": {"total": {"type": "number", "description": "总播放次数"}, "unique": {"type": "number", "description": "独立用户播放次数"}, "completed": {"type": "number", "description": "完整播放次数"}, "averageCompletion": {"type": "number", "description": "平均完成率"}}}, "engagement": {"type": "object", "properties": {"likes": {"type": "number"}, "comments": {"type": "number"}, "shares": {"type": "number"}, "addedToPlaylists": {"type": "number"}}}, "demographics": {"type": "object", "properties": {"ageGroups": {"type": "array", "items": {"type": "object", "properties": {"ageRange": {"type": "string"}, "count": {"type": "number"}, "percentage": {"type": "number"}}}}, "regions": {"type": "array", "items": {"type": "object", "properties": {"region": {"type": "string"}, "count": {"type": "number"}, "percentage": {"type": "number"}}}}}}}}, "SystemOverview": {"type": "object", "properties": {"period": {"type": "string", "description": "统计周期"}, "users": {"type": "object", "properties": {"total": {"type": "number"}, "active": {"type": "number"}, "new": {"type": "number"}, "retention": {"type": "number"}}}, "content": {"type": "object", "properties": {"totalMusic": {"type": "number"}, "totalPlaylists": {"type": "number"}, "newUploads": {"type": "number"}, "storageUsed": {"type": "number"}}}, "activity": {"type": "object", "properties": {"totalPlays": {"type": "number"}, "totalPlayTime": {"type": "number"}, "peakConcurrentUsers": {"type": "number"}, "averageSessionLength": {"type": "number"}}}, "performance": {"type": "object", "properties": {"averageResponseTime": {"type": "number"}, "uptime": {"type": "number"}, "errorRate": {"type": "number"}}}}}, "TrendingStats": {"type": "object", "properties": {"type": {"type": "string", "description": "趋势类型"}, "period": {"type": "string", "description": "统计周期"}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "rank": {"type": "integer"}, "score": {"type": "number"}, "change": {"type": "integer", "description": "排名变化"}, "metrics": {"type": "object", "properties": {"plays": {"type": "number"}, "likes": {"type": "number"}, "shares": {"type": "number"}}}}}}, "generatedAt": {"type": "string", "format": "date-time"}}}, "AnalyticsReport": {"type": "object", "properties": {"reportId": {"type": "string", "description": "报告ID"}, "reportType": {"type": "string", "description": "报告类型"}, "dateRange": {"type": "object", "properties": {"startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}}}, "format": {"type": "string", "description": "报告格式"}, "downloadUrl": {"type": "string", "description": "下载链接"}, "status": {"type": "string", "enum": ["generating", "completed", "failed"], "description": "生成状态"}, "generatedAt": {"type": "string", "format": "date-time"}, "expiresAt": {"type": "string", "format": "date-time"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string"}, "message": {"type": "string"}}}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "Unauthorized": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "Forbidden": {"description": "访问被禁止", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "NotFound": {"description": "资源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}