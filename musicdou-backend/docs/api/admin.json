{"openapi": "3.0.3", "info": {"title": "MusicDou 系统管理模块 API", "description": "MusicDou音乐平台系统管理相关API，包括性能监控、音频质量管理、批量操作等功能", "version": "1.0.0", "contact": {"name": "MusicDou API Support", "url": "http://localhost:3000"}}, "servers": [{"url": "http://localhost:3000/api/v1", "description": "开发环境服务器"}], "tags": [{"name": "性能监控", "description": "系统性能监控和指标"}, {"name": "音频质量管理", "description": "音频质量检测和管理"}, {"name": "批量操作", "description": "批量数据处理操作"}], "paths": {"/performance/metrics": {"get": {"tags": ["性能监控"], "summary": "获取性能指标", "description": "获取系统性能监控指标", "operationId": "getPerformanceMetrics", "security": [{"bearerAuth": []}], "parameters": [{"name": "timeRange", "in": "query", "description": "时间范围", "schema": {"type": "string", "enum": ["1h", "6h", "24h", "7d", "30d"], "default": "1h"}}, {"name": "metrics", "in": "query", "description": "指定指标类型（逗号分隔）", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Performance metrics retrieved successfully"}, "data": {"$ref": "#/components/schemas/PerformanceMetrics"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/performance/health": {"get": {"tags": ["性能监控"], "summary": "获取系统健康状态", "description": "获取系统各组件的健康状态", "operationId": "getSystemHealth", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "System health retrieved successfully"}, "data": {"$ref": "#/components/schemas/SystemHealth"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/audio-quality/analyze": {"post": {"tags": ["音频质量管理"], "summary": "分析音频质量", "description": "对指定音频文件进行质量分析", "operationId": "analyzeAudioQuality", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["musicId"], "properties": {"musicId": {"type": "string", "description": "音乐ID"}, "analysisType": {"type": "string", "enum": ["basic", "detailed", "full"], "default": "basic", "description": "分析类型"}, "priority": {"type": "string", "enum": ["low", "normal", "high"], "default": "normal", "description": "分析优先级"}}}}}}, "responses": {"202": {"description": "分析任务已创建", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Audio quality analysis started"}, "data": {"$ref": "#/components/schemas/AnalysisTask"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/audio-quality/batch-analyze": {"post": {"tags": ["音频质量管理"], "summary": "批量分析音频质量", "description": "批量分析多个音频文件的质量", "operationId": "batchAnalyzeAudioQuality", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["musicIds"], "properties": {"musicIds": {"type": "array", "items": {"type": "string"}, "minItems": 1, "maxItems": 100, "description": "音乐ID列表"}, "analysisType": {"type": "string", "enum": ["basic", "detailed", "full"], "default": "basic", "description": "分析类型"}, "batchName": {"type": "string", "description": "批次名称"}}}}}}, "responses": {"202": {"description": "批量分析任务已创建", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Batch audio quality analysis started"}, "data": {"$ref": "#/components/schemas/BatchAnalysisTask"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/audio-quality/results/{taskId}": {"get": {"tags": ["音频质量管理"], "summary": "获取分析结果", "description": "获取音频质量分析任务的结果", "operationId": "getAnalysisResults", "security": [{"bearerAuth": []}], "parameters": [{"name": "taskId", "in": "path", "required": true, "description": "分析任务ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Analysis results retrieved successfully"}, "data": {"$ref": "#/components/schemas/AnalysisResults"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT认证令牌"}}, "schemas": {"PerformanceMetrics": {"type": "object", "properties": {"timeRange": {"type": "string", "description": "时间范围"}, "timestamp": {"type": "string", "format": "date-time", "description": "数据时间戳"}, "system": {"type": "object", "properties": {"cpu": {"type": "object", "properties": {"usage": {"type": "number", "description": "CPU使用率（%）"}, "loadAverage": {"type": "array", "items": {"type": "number"}, "description": "负载平均值"}}}, "memory": {"type": "object", "properties": {"used": {"type": "number", "description": "已使用内存（MB）"}, "total": {"type": "number", "description": "总内存（MB）"}, "usage": {"type": "number", "description": "内存使用率（%）"}}}, "disk": {"type": "object", "properties": {"used": {"type": "number", "description": "已使用磁盘空间（GB）"}, "total": {"type": "number", "description": "总磁盘空间（GB）"}, "usage": {"type": "number", "description": "磁盘使用率（%）"}}}}}, "application": {"type": "object", "properties": {"responseTime": {"type": "object", "properties": {"average": {"type": "number", "description": "平均响应时间（ms）"}, "p95": {"type": "number", "description": "95%响应时间（ms）"}, "p99": {"type": "number", "description": "99%响应时间（ms）"}}}, "throughput": {"type": "object", "properties": {"requestsPerSecond": {"type": "number", "description": "每秒请求数"}, "concurrentUsers": {"type": "number", "description": "并发用户数"}}}, "errors": {"type": "object", "properties": {"rate": {"type": "number", "description": "错误率（%）"}, "count": {"type": "number", "description": "错误数量"}}}}}, "database": {"type": "object", "properties": {"connections": {"type": "object", "properties": {"active": {"type": "number", "description": "活跃连接数"}, "idle": {"type": "number", "description": "空闲连接数"}, "total": {"type": "number", "description": "总连接数"}}}, "queryTime": {"type": "object", "properties": {"average": {"type": "number", "description": "平均查询时间（ms）"}, "slowQueries": {"type": "number", "description": "慢查询数量"}}}}}}}, "SystemHealth": {"type": "object", "properties": {"overall": {"type": "string", "enum": ["healthy", "warning", "critical"], "description": "整体健康状态"}, "components": {"type": "object", "properties": {"api": {"type": "object", "properties": {"status": {"type": "string", "enum": ["up", "down", "degraded"]}, "responseTime": {"type": "number"}, "lastCheck": {"type": "string", "format": "date-time"}}}, "database": {"type": "object", "properties": {"status": {"type": "string", "enum": ["up", "down", "degraded"]}, "connectionTime": {"type": "number"}, "lastCheck": {"type": "string", "format": "date-time"}}}, "storage": {"type": "object", "properties": {"status": {"type": "string", "enum": ["up", "down", "degraded"]}, "availableSpace": {"type": "number"}, "lastCheck": {"type": "string", "format": "date-time"}}}, "cache": {"type": "object", "properties": {"status": {"type": "string", "enum": ["up", "down", "degraded"]}, "hitRate": {"type": "number"}, "lastCheck": {"type": "string", "format": "date-time"}}}}}, "uptime": {"type": "number", "description": "系统运行时间（秒）"}, "lastUpdated": {"type": "string", "format": "date-time"}}}, "AnalysisTask": {"type": "object", "properties": {"taskId": {"type": "string", "description": "任务ID"}, "musicId": {"type": "string", "description": "音乐ID"}, "analysisType": {"type": "string", "description": "分析类型"}, "status": {"type": "string", "enum": ["queued", "processing", "completed", "failed"], "description": "任务状态"}, "priority": {"type": "string", "description": "优先级"}, "createdAt": {"type": "string", "format": "date-time"}, "estimatedCompletion": {"type": "string", "format": "date-time", "nullable": true}}}, "BatchAnalysisTask": {"type": "object", "properties": {"batchId": {"type": "string", "description": "批次ID"}, "batchName": {"type": "string", "description": "批次名称"}, "totalItems": {"type": "integer", "description": "总项目数"}, "completedItems": {"type": "integer", "description": "已完成项目数"}, "failedItems": {"type": "integer", "description": "失败项目数"}, "status": {"type": "string", "enum": ["queued", "processing", "completed", "failed", "cancelled"], "description": "批次状态"}, "progress": {"type": "number", "minimum": 0, "maximum": 100, "description": "进度百分比"}, "createdAt": {"type": "string", "format": "date-time"}, "estimatedCompletion": {"type": "string", "format": "date-time", "nullable": true}}}, "AnalysisResults": {"type": "object", "properties": {"taskId": {"type": "string", "description": "任务ID"}, "musicId": {"type": "string", "description": "音乐ID"}, "status": {"type": "string", "enum": ["completed", "failed"], "description": "分析状态"}, "results": {"type": "object", "properties": {"quality": {"type": "object", "properties": {"overall": {"type": "string", "enum": ["excellent", "good", "fair", "poor"], "description": "整体质量评级"}, "score": {"type": "number", "minimum": 0, "maximum": 100, "description": "质量分数"}, "bitrate": {"type": "number", "description": "比特率"}, "sampleRate": {"type": "number", "description": "采样率"}, "dynamicRange": {"type": "number", "description": "动态范围"}}}, "issues": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["clipping", "noise", "silence", "distortion", "low_quality"]}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "description": {"type": "string"}, "timestamp": {"type": "number", "description": "问题出现的时间点（秒）"}}}, "description": "检测到的问题"}, "recommendations": {"type": "array", "items": {"type": "string"}, "description": "改进建议"}}}, "completedAt": {"type": "string", "format": "date-time"}, "processingTime": {"type": "number", "description": "处理时间（秒）"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string"}, "message": {"type": "string"}}}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "Unauthorized": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "Forbidden": {"description": "访问被禁止", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "NotFound": {"description": "资源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}