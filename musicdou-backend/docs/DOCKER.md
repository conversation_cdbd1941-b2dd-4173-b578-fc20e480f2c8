# MusicDou Docker 开发环境

本文档介绍如何使用 Docker 来管理 MusicDou 项目的开发环境。

## 前置要求

1. **Docker Desktop**: 确保已安装并运行 Docker Desktop
2. **Node.js**: 本地需要 Node.js 环境来运行应用程序
3. **npm**: 用于管理 Node.js 依赖

## 服务架构

Docker 环境包含以下服务：

- **MongoDB** (端口 27017): 主数据库
- **Redis** (端口 6379): 缓存和会话存储
- **MinIO** (端口 9000/9001): 对象存储服务

## 快速开始

### 1. 启动所有服务

```bash
# 使用 npm 脚本
npm run docker:start

# 或直接使用脚本
./scripts/docker-dev.sh start
```

### 2. 查看服务状态

```bash
npm run docker:status
```

### 3. 启动应用程序

```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

### 4. 停止服务

```bash
npm run docker:stop
```

## 详细命令

### 服务管理

```bash
# 启动服务
./scripts/docker-dev.sh start

# 停止服务
./scripts/docker-dev.sh stop

# 重启服务
./scripts/docker-dev.sh restart

# 查看状态
./scripts/docker-dev.sh status
```

### 日志查看

```bash
# 查看所有服务日志
./scripts/docker-dev.sh logs

# 查看特定服务日志
./scripts/docker-dev.sh logs mongodb
./scripts/docker-dev.sh logs redis
./scripts/docker-dev.sh logs minio
```

### 进入容器

```bash
# 进入 MongoDB 容器
./scripts/docker-dev.sh enter mongodb

# 进入 Redis 容器
./scripts/docker-dev.sh enter redis

# 进入 MinIO 容器
./scripts/docker-dev.sh enter minio
```

### 数据管理

```bash
# 清理所有数据（谨慎使用）
./scripts/docker-dev.sh clean
```

## 服务配置

### MongoDB

- **容器名**: musicdou-mongodb
- **端口**: 27017
- **管理员用户**: admin / musicdou123
- **应用用户**: musicdou_user / musicdou123
- **数据库**: musicdou

### Redis

- **容器名**: musicdou-redis
- **端口**: 6379
- **密码**: musicdou123

### MinIO

- **容器名**: musicdou-minio
- **API端口**: 9000
- **控制台端口**: 9001
- **用户名**: minioadmin
- **密码**: musicdou123
- **控制台地址**: http://localhost:9001

## 数据持久化

所有服务数据都通过 Docker volumes 进行持久化：

- `mongodb_data`: MongoDB 数据
- `redis_data`: Redis 数据
- `minio_data`: MinIO 对象存储数据

## 网络配置

所有服务都在 `musicdou-network` 网络中运行，可以通过服务名相互访问。

## 故障排除

### 1. 端口冲突

如果遇到端口冲突，请检查以下端口是否被占用：
- 27017 (MongoDB)
- 6379 (Redis)
- 9000 (MinIO API)
- 9001 (MinIO Console)

### 2. 权限问题

确保脚本有执行权限：
```bash
chmod +x scripts/docker-dev.sh
```

### 3. Docker 未运行

确保 Docker Desktop 已启动并运行。

### 4. 数据库连接失败

检查环境变量配置是否正确，特别是 `.env` 文件中的数据库连接字符串。

## 开发工作流

1. **启动开发环境**:
   ```bash
   npm run docker:start
   ```

2. **等待服务就绪** (约10秒):
   ```bash
   npm run docker:status
   ```

3. **启动应用程序**:
   ```bash
   npm run dev
   ```

4. **开发完成后停止服务**:
   ```bash
   npm run docker:stop
   ```

## 生产环境注意事项

- 修改默认密码
- 配置适当的资源限制
- 设置备份策略
- 配置监控和日志收集
- 使用 Docker Secrets 管理敏感信息
