<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MusicDou Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        input, button {
            margin: 5px 0;
            padding: 8px;
            width: 200px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: auto;
            padding: 10px 20px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .token-display {
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>MusicDou Authentication Test</h1>
    
    <div class="section">
        <h3>User Registration</h3>
        <input type="text" id="regUsername" placeholder="Username" required>
        <input type="email" id="regEmail" placeholder="Email" required>
        <input type="password" id="regPassword" placeholder="Password" required>
        <input type="text" id="regDisplayName" placeholder="Display Name">
        <button onclick="register()">Register</button>
        <div id="regResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="section">
        <h3>User Login</h3>
        <input type="text" id="loginIdentifier" placeholder="Username or Email" required>
        <input type="password" id="loginPassword" placeholder="Password" required>
        <button onclick="login()">Login</button>
        <div id="loginResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="section">
        <h3>User Profile (Requires Authentication)</h3>
        <button onclick="getProfile()">Get Profile</button>
        <div id="profileResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="section">
        <h3>Daily Sign-in (Requires Authentication)</h3>
        <button onclick="dailySignIn()">Daily Sign-in</button>
        <div id="signinResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="section">
        <h3>Current Token</h3>
        <div id="tokenDisplay" class="token-display">No token</div>
        <button onclick="clearToken()">Clear Token</button>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api/v1';
        let currentToken = localStorage.getItem('authToken') || null;
        
        // Update token display
        function updateTokenDisplay() {
            const tokenDiv = document.getElementById('tokenDisplay');
            tokenDiv.textContent = currentToken || 'No token';
        }
        
        // Show result
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }
        
        // Make authenticated request
        async function makeRequest(url, options = {}) {
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };
            
            if (currentToken) {
                headers.Authorization = `Bearer ${currentToken}`;
            }
            
            return fetch(url, {
                ...options,
                headers
            });
        }
        
        // Register user
        async function register() {
            const username = document.getElementById('regUsername').value;
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            const displayName = document.getElementById('regDisplayName').value;
            
            if (!username || !email || !password) {
                showResult('regResult', 'Please fill in all required fields', 'error');
                return;
            }
            
            try {
                const response = await makeRequest(`${API_BASE}/auth/register`, {
                    method: 'POST',
                    body: JSON.stringify({
                        username,
                        email,
                        password,
                        displayName: displayName || undefined
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    currentToken = result.data.token;
                    localStorage.setItem('authToken', currentToken);
                    updateTokenDisplay();
                    
                    showResult('regResult', 
                        `✅ Registration successful!<br>
                        Username: ${result.data.user.username}<br>
                        Email: ${result.data.user.email}<br>
                        Points: ${result.data.user.points}<br>
                        User Group: ${result.data.user.userGroup}`, 
                        'success'
                    );
                } else {
                    showResult('regResult', `❌ ${result.error}: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('regResult', `❌ Registration failed: ${error.message}`, 'error');
            }
        }
        
        // Login user
        async function login() {
            const identifier = document.getElementById('loginIdentifier').value;
            const password = document.getElementById('loginPassword').value;
            
            if (!identifier || !password) {
                showResult('loginResult', 'Please fill in all fields', 'error');
                return;
            }
            
            try {
                const response = await makeRequest(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    body: JSON.stringify({
                        identifier,
                        password
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    currentToken = result.data.token;
                    localStorage.setItem('authToken', currentToken);
                    updateTokenDisplay();
                    
                    showResult('loginResult', 
                        `✅ Login successful!<br>
                        Username: ${result.data.user.username}<br>
                        Email: ${result.data.user.email}<br>
                        Points: ${result.data.user.points}<br>
                        User Group: ${result.data.user.userGroup}`, 
                        'success'
                    );
                } else {
                    showResult('loginResult', `❌ ${result.error}: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('loginResult', `❌ Login failed: ${error.message}`, 'error');
            }
        }
        
        // Get user profile
        async function getProfile() {
            if (!currentToken) {
                showResult('profileResult', 'Please login first', 'error');
                return;
            }
            
            try {
                const response = await makeRequest(`${API_BASE}/auth/profile`);
                const result = await response.json();
                
                if (response.ok) {
                    const user = result.data.user;
                    showResult('profileResult', 
                        `✅ Profile loaded!<br>
                        Username: ${user.username}<br>
                        Email: ${user.email}<br>
                        Display Name: ${user.profile?.displayName || 'Not set'}<br>
                        Points: ${user.points}<br>
                        User Group: ${user.userGroup}<br>
                        Created: ${new Date(user.createdAt).toLocaleString()}<br>
                        Last Login: ${user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleString() : 'Never'}`, 
                        'success'
                    );
                } else {
                    showResult('profileResult', `❌ ${result.error}: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('profileResult', `❌ Failed to get profile: ${error.message}`, 'error');
            }
        }
        
        // Daily sign-in
        async function dailySignIn() {
            if (!currentToken) {
                showResult('signinResult', 'Please login first', 'error');
                return;
            }
            
            try {
                const response = await makeRequest(`${API_BASE}/auth/signin`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    if (result.data.alreadySignedIn) {
                        showResult('signinResult', 
                            `ℹ️ Already signed in today!<br>
                            Current Points: ${result.data.points}<br>
                            Consecutive Days: ${result.data.consecutiveDays}`, 
                            'success'
                        );
                    } else {
                        showResult('signinResult', 
                            `✅ Daily sign-in successful!<br>
                            Points Earned: +${result.data.pointsEarned}<br>
                            Total Points: ${result.data.totalPoints}<br>
                            Consecutive Days: ${result.data.consecutiveDays}`, 
                            'success'
                        );
                    }
                } else {
                    showResult('signinResult', `❌ ${result.error}: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('signinResult', `❌ Sign-in failed: ${error.message}`, 'error');
            }
        }
        
        // Clear token
        function clearToken() {
            currentToken = null;
            localStorage.removeItem('authToken');
            updateTokenDisplay();
            showResult('profileResult', 'Token cleared', 'success');
        }
        
        // Initialize
        window.onload = function() {
            updateTokenDisplay();
        };
    </script>
</body>
</html>
