#!/usr/bin/env node

/**
 * 音乐统计分析功能测试脚本
 * 测试新增的统计分析接口
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';

// 测试用户凭据
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123'
};

let adminToken = '';

/**
 * 登录获取token
 */
async function login(credentials) {
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, credentials);
    return response.data.data.token;
  } catch (error) {
    console.error('Login failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试详细音乐统计
 */
async function testDetailedMusicStats() {
  console.log('\n📊 Testing: Detailed music statistics');
  try {
    const response = await axios.get(`${BASE_URL}/music/admin/stats/detailed`, {
      headers: { Authorization: `Bearer ${adminToken}` },
      params: { period: '30d', groupBy: 'day' }
    });
    
    console.log('✅ Detailed music statistics retrieved successfully');
    console.log(`   Period: ${response.data.data.period}`);
    console.log(`   Group by: ${response.data.data.groupBy}`);
    console.log(`   Total music: ${response.data.data.overall.totalMusic || 0}`);
    console.log(`   Total plays: ${response.data.data.overall.totalPlayCount || 0}`);
    console.log(`   Time series data points: ${response.data.data.timeSeries.length}`);
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Detailed music statistics failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试用户行为分析
 */
async function testUserBehaviorAnalysis() {
  console.log('\n👥 Testing: User behavior analysis');
  try {
    const response = await axios.get(`${BASE_URL}/music/admin/stats/user-behavior`, {
      headers: { Authorization: `Bearer ${adminToken}` },
      params: { period: '30d', analysisType: 'overview' }
    });
    
    console.log('✅ User behavior analysis retrieved successfully');
    console.log(`   Period: ${response.data.data.period}`);
    console.log(`   Analysis type: ${response.data.data.analysisType}`);
    
    if (response.data.data.overview) {
      const overview = response.data.data.overview;
      console.log(`   Action types: ${overview.actionStats.length}`);
      console.log(`   Popular content items: ${overview.popularContent.length}`);
    }
    
    return response.data.data;
  } catch (error) {
    console.error('❌ User behavior analysis failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试音乐趋势分析
 */
async function testMusicTrendAnalysis() {
  console.log('\n📈 Testing: Music trend analysis');
  try {
    const response = await axios.get(`${BASE_URL}/music/admin/stats/trends`, {
      headers: { Authorization: `Bearer ${adminToken}` },
      params: { period: '90d', trendType: 'popularity' }
    });
    
    console.log('✅ Music trend analysis retrieved successfully');
    console.log(`   Period: ${response.data.data.period}`);
    console.log(`   Trend type: ${response.data.data.trendType}`);
    
    if (response.data.data.popularity) {
      console.log(`   Trending music items: ${response.data.data.popularity.length}`);
    }
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Music trend analysis failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试流派分析
 */
async function testGenreAnalysis() {
  console.log('\n🎵 Testing: Genre analysis');
  try {
    const response = await axios.get(`${BASE_URL}/music/admin/stats/genres`, {
      headers: { Authorization: `Bearer ${adminToken}` },
      params: { period: '30d', limit: 15 }
    });
    
    console.log('✅ Genre analysis retrieved successfully');
    console.log(`   Period: ${response.data.data.period}`);
    console.log(`   Genre stats: ${response.data.data.stats.length}`);
    console.log(`   Genre growth data: ${response.data.data.growth.length}`);
    console.log(`   Genre engagement data: ${response.data.data.engagement.length}`);
    
    if (response.data.data.stats.length > 0) {
      const topGenre = response.data.data.stats[0];
      console.log(`   Top genre: ${topGenre._id} (${topGenre.totalMusic} music)`);
    }
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Genre analysis failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试艺术家排行榜
 */
async function testArtistRanking() {
  console.log('\n🎤 Testing: Artist ranking');
  try {
    const response = await axios.get(`${BASE_URL}/music/admin/stats/artists`, {
      headers: { Authorization: `Bearer ${adminToken}` },
      params: { period: '30d', rankBy: 'playCount', limit: 20 }
    });
    
    console.log('✅ Artist ranking retrieved successfully');
    console.log(`   Artists in ranking: ${response.data.data.length}`);
    
    if (response.data.data.length > 0) {
      const topArtist = response.data.data[0];
      console.log(`   #1 Artist: ${topArtist.artist}`);
      console.log(`   Total plays: ${topArtist.totalPlays}`);
      console.log(`   Total music: ${topArtist.totalMusic}`);
    }
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Artist ranking failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试系统性能指标
 */
async function testSystemMetrics() {
  console.log('\n⚙️ Testing: System metrics');
  try {
    const response = await axios.get(`${BASE_URL}/music/admin/stats/system`, {
      headers: { Authorization: `Bearer ${adminToken}` },
      params: { period: '24h' }
    });
    
    console.log('✅ System metrics retrieved successfully');
    console.log(`   Period: ${response.data.data.period}`);
    console.log(`   Total API requests: ${response.data.data.api.totalRequests || 0}`);
    console.log(`   Active users: ${response.data.data.users.activeUsers || 0}`);
    console.log(`   Total files: ${response.data.data.storage.totalFiles || 0}`);
    
    return response.data.data;
  } catch (error) {
    console.error('❌ System metrics failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试统计报告生成
 */
async function testStatisticsReport() {
  console.log('\n📋 Testing: Statistics report generation');
  try {
    const response = await axios.post(`${BASE_URL}/music/admin/reports/generate`, {
      reportType: 'comprehensive',
      period: '30d',
      format: 'json',
      includeCharts: true
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    console.log('✅ Statistics report generated successfully');
    console.log(`   Report type: ${response.data.data.metadata.reportType}`);
    console.log(`   Period: ${response.data.data.metadata.period}`);
    console.log(`   Sections: ${Object.keys(response.data.data.sections).join(', ')}`);
    console.log(`   Has summary: ${!!response.data.data.summary}`);
    console.log(`   Has charts: ${!!response.data.data.charts}`);
    
    if (response.data.data.summary) {
      console.log(`   Key insights: ${response.data.data.summary.insights.length}`);
      console.log(`   Recommendations: ${response.data.data.summary.recommendations.length}`);
    }
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Statistics report generation failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试权限控制
 */
async function testPermissionControl() {
  console.log('\n🔒 Testing: Permission control for statistics');
  try {
    // 尝试不带token访问统计接口
    const response = await axios.get(`${BASE_URL}/music/admin/stats/detailed`);
    
    console.log('❌ Permission control failed - should require admin token');
  } catch (error) {
    if (error.response?.status === 401 || error.response?.status === 403) {
      console.log('✅ Permission control working - access denied without admin token');
    } else {
      console.error('❌ Unexpected error:', error.response?.data?.message || error.message);
    }
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 Starting Music Statistics Tests\n');
  
  // 登录获取token
  console.log('🔐 Logging in as admin...');
  adminToken = await login(ADMIN_CREDENTIALS);
  
  if (!adminToken) {
    console.error('❌ Failed to get admin token. Please ensure admin user exists.');
    return;
  }
  
  console.log('✅ Admin login successful');
  
  // 运行测试
  await testDetailedMusicStats();
  await testUserBehaviorAnalysis();
  await testMusicTrendAnalysis();
  await testGenreAnalysis();
  await testArtistRanking();
  await testSystemMetrics();
  await testStatisticsReport();
  await testPermissionControl();
  
  console.log('\n🎉 Music Statistics Tests Completed!');
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  testDetailedMusicStats,
  testUserBehaviorAnalysis,
  testMusicTrendAnalysis,
  testGenreAnalysis,
  testArtistRanking,
  testSystemMetrics,
  testStatisticsReport,
  testPermissionControl
};
