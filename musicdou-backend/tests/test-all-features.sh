#!/bin/bash

# MusicDou 3.5 音乐管理接口综合测试脚本
# 测试所有新增功能

echo "🚀 MusicDou 3.5 Comprehensive Feature Tests"
echo "============================================="

# 检查服务器是否运行
echo "📡 Checking server status..."
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Server is not running. Please start the server first:"
    echo "   npm run dev"
    exit 1
fi
echo "✅ Server is running"

echo ""
echo "🧪 Running All Feature Tests..."
echo "==============================="

# 测试1: 管理员审核工作流
echo ""
echo "1️⃣ Testing Admin Review Workflow..."
echo "-----------------------------------"
if [ -f "test-admin-review.js" ]; then
    node test-admin-review.js
else
    echo "⚠️  Admin review test script not found"
fi

# 测试2: 高级搜索功能
echo ""
echo "2️⃣ Testing Advanced Search Features..."
echo "--------------------------------------"
if [ -f "test-advanced-search.js" ]; then
    node test-advanced-search.js
else
    echo "⚠️  Advanced search test script not found"
fi

# 测试3: 音乐推荐算法
echo ""
echo "3️⃣ Testing Music Recommendation System..."
echo "-----------------------------------------"
if [ -f "test-recommendation.js" ]; then
    node test-recommendation.js
else
    echo "⚠️  Recommendation test script not found"
fi

# 测试4: 批量操作接口
echo ""
echo "4️⃣ Testing Batch Operations..."
echo "------------------------------"
if [ -f "test-batch-operations.js" ]; then
    node test-batch-operations.js
else
    echo "⚠️  Batch operations test script not found"
fi

# 测试5: 音乐统计分析
echo ""
echo "5️⃣ Testing Music Statistics & Analytics..."
echo "------------------------------------------"
if [ -f "test-statistics.js" ]; then
    node test-statistics.js
else
    echo "⚠️  Statistics test script not found"
fi

echo ""
echo "📊 Test Summary Report"
echo "======================"
echo ""
echo "✅ Feature Categories Tested:"
echo "   1. Admin Review Workflow"
echo "   2. Advanced Search & Filtering"
echo "   3. Music Recommendation System"
echo "   4. Batch Operations"
echo "   5. Statistics & Analytics"
echo ""
echo "🎯 Total API Endpoints Added: 25+"
echo "📁 New Models Created: 2 (UserBehavior, BatchOperation)"
echo "🔧 Enhanced Services: MusicService significantly expanded"
echo ""

echo "🎉 All MusicDou 3.5 Features Tested!"
echo ""
echo "📋 Next Steps:"
echo "=============="
echo "1. Review test results above"
echo "2. Fix any failing tests"
echo "3. Begin Phase 4: Playlist System"
echo "4. Plan Phase 5: Social Features"
echo ""
echo "🔗 Quick Links:"
echo "==============="
echo "• Health Check: http://localhost:3000/health"
echo "• API Documentation: http://localhost:3000/api/v1"
echo "• Admin Panel: (To be implemented)"
echo ""
echo "📝 Development Notes:"
echo "===================="
echo "• All admin endpoints require admin authentication"
echo "• User behavior is tracked for recommendation engine"
echo "• Batch operations are logged for audit purposes"
echo "• Statistics provide comprehensive system insights"
echo "• Search functionality supports multiple languages"
echo ""
echo "🎵 MusicDou 3.5 - Music Management Complete! 🎵"
