const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000/api/v1';
let authToken = '';
let testUserId = '';
let testMusicId = '';
let testCommentId = '';
let testPlaylistId = '';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use((config) => {
  if (authToken) {
    config.headers.Authorization = `Bearer ${authToken}`;
  }
  return config;
});

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      console.error(`❌ API Error: ${error.response.status} - ${error.response.data.message || error.response.data.error}`);
    } else {
      console.error(`❌ Network Error: ${error.message}`);
    }
    throw error;
  }
);

/**
 * 测试用例
 */

// 1. 用户设置
async function setupTestUser() {
  console.log('\n🔧 Setting up test user...');
  
  try {
    // 注册测试用户
    const registerData = {
      username: `testuser_${Date.now()}`,
      email: `test_${Date.now()}@example.com`,
      password: 'testpass123'
    };

    const registerResponse = await api.post('/auth/register', registerData);
    console.log('✅ User registered successfully');

    // 登录获取token
    const loginResponse = await api.post('/auth/login', {
      identifier: registerData.username,
      password: registerData.password
    });

    authToken = loginResponse.data.data.token;
    testUserId = loginResponse.data.data.user._id;
    console.log(`✅ User logged in, ID: ${testUserId}`);

  } catch (error) {
    console.error('❌ Failed to setup test user');
    throw error;
  }
}

// 2. 获取测试数据
async function getTestData() {
  console.log('\n🔧 Getting test data...');
  
  try {
    // 获取音乐列表
    const musicResponse = await api.get('/music?limit=1');
    if (musicResponse.data.data.music.length > 0) {
      testMusicId = musicResponse.data.data.music[0]._id;
      console.log(`✅ Got test music ID: ${testMusicId}`);
    }

    // 获取歌单列表
    const playlistResponse = await api.get('/playlists?limit=1');
    if (playlistResponse.data.data.playlists.length > 0) {
      testPlaylistId = playlistResponse.data.data.playlists[0]._id;
      console.log(`✅ Got test playlist ID: ${testPlaylistId}`);
    }

    // 创建测试评论
    if (testMusicId) {
      const commentResponse = await api.post('/comments', {
        content: 'This is a test comment for like system testing',
        musicId: testMusicId
      });
      testCommentId = commentResponse.data.data._id;
      console.log(`✅ Created test comment ID: ${testCommentId}`);
    }

  } catch (error) {
    console.error('❌ Failed to get test data');
    throw error;
  }
}

// 3. 测试点赞音乐
async function testLikeMusic() {
  console.log('\n🎵 Testing music like functionality...');
  
  if (!testMusicId) {
    console.log('⚠️ No test music available, skipping music like test');
    return;
  }

  try {
    // 点赞音乐
    const likeResponse = await api.post('/likes', {
      targetType: 'music',
      targetId: testMusicId
    });
    console.log('✅ Music liked successfully');
    console.log(`   Like ID: ${likeResponse.data.data._id}`);

    // 检查点赞状态
    const statusResponse = await api.get('/likes/check', {
      params: {
        targetType: 'music',
        targetId: testMusicId
      }
    });
    console.log(`✅ Like status checked: ${statusResponse.data.data.isLiked}`);

    // 获取音乐点赞列表
    const likesResponse = await api.get(`/likes/music/${testMusicId}`);
    console.log(`✅ Music likes count: ${likesResponse.data.data.likes.length}`);

    // 取消点赞
    const unlikeResponse = await api.delete('/likes', {
      data: {
        targetType: 'music',
        targetId: testMusicId
      }
    });
    console.log('✅ Music unliked successfully');

  } catch (error) {
    console.error('❌ Music like test failed');
    throw error;
  }
}

// 4. 测试点赞评论
async function testLikeComment() {
  console.log('\n💬 Testing comment like functionality...');
  
  if (!testCommentId) {
    console.log('⚠️ No test comment available, skipping comment like test');
    return;
  }

  try {
    // 点赞评论
    const likeResponse = await api.post('/likes', {
      targetType: 'comment',
      targetId: testCommentId
    });
    console.log('✅ Comment liked successfully');

    // 检查点赞状态
    const statusResponse = await api.get('/likes/check', {
      params: {
        targetType: 'comment',
        targetId: testCommentId
      }
    });
    console.log(`✅ Comment like status: ${statusResponse.data.data.isLiked}`);

    // 获取评论点赞列表
    const likesResponse = await api.get(`/likes/comment/${testCommentId}`);
    console.log(`✅ Comment likes count: ${likesResponse.data.data.likes.length}`);

  } catch (error) {
    console.error('❌ Comment like test failed');
    throw error;
  }
}

// 5. 测试批量检查点赞状态
async function testBatchCheckLikes() {
  console.log('\n📋 Testing batch like status check...');
  
  const targetIds = [testMusicId, testCommentId].filter(id => id);
  
  if (targetIds.length === 0) {
    console.log('⚠️ No test targets available, skipping batch check test');
    return;
  }

  try {
    // 批量检查音乐点赞状态
    if (testMusicId) {
      const batchResponse = await api.post('/likes/batch-check', {
        targetType: 'music',
        targetIds: [testMusicId]
      });
      console.log('✅ Batch music like status checked');
      console.log(`   Results: ${JSON.stringify(batchResponse.data.data)}`);
    }

    // 批量检查评论点赞状态
    if (testCommentId) {
      const batchResponse = await api.post('/likes/batch-check', {
        targetType: 'comment',
        targetIds: [testCommentId]
      });
      console.log('✅ Batch comment like status checked');
      console.log(`   Results: ${JSON.stringify(batchResponse.data.data)}`);
    }

  } catch (error) {
    console.error('❌ Batch like check test failed');
    throw error;
  }
}

// 6. 测试用户点赞统计
async function testUserLikeStats() {
  console.log('\n📊 Testing user like statistics...');
  
  try {
    // 获取用户点赞统计
    const statsResponse = await api.get(`/likes/stats/user/${testUserId}`);
    console.log('✅ User like stats retrieved');
    console.log(`   Stats: ${JSON.stringify(statsResponse.data.data)}`);

    // 获取用户点赞列表
    const likesResponse = await api.get(`/likes/user/${testUserId}`);
    console.log('✅ User likes list retrieved');
    console.log(`   Total likes: ${likesResponse.data.data.likes.length}`);

  } catch (error) {
    console.error('❌ User like stats test failed');
    throw error;
  }
}

// 7. 测试分享功能
async function testShareFunctionality() {
  console.log('\n🔗 Testing share functionality...');
  
  if (!testMusicId) {
    console.log('⚠️ No test music available, skipping share test');
    return;
  }

  try {
    // 生成音乐分享链接
    const shareResponse = await api.post('/shares/generate', {
      targetType: 'music',
      targetId: testMusicId,
      platform: 'web'
    });
    console.log('✅ Music share link generated');
    console.log(`   Share URL: ${shareResponse.data.data.shareUrl}`);
    console.log(`   Share title: ${shareResponse.data.data.shareContent.title}`);

    // 获取分享内容详情
    const contentResponse = await api.get(`/shares/music/${testMusicId}`);
    console.log('✅ Share content retrieved');
    console.log(`   Content type: ${contentResponse.data.data.type}`);

    // 获取分享统计
    const statsResponse = await api.get(`/shares/stats/music/${testMusicId}`);
    console.log('✅ Share stats retrieved');
    console.log(`   Share count: ${statsResponse.data.data.shareCount}`);

  } catch (error) {
    console.error('❌ Share functionality test failed');
    throw error;
  }
}

// 8. 测试热门点赞目标
async function testPopularTargets() {
  console.log('\n🔥 Testing popular targets...');
  
  try {
    // 获取热门音乐
    const popularMusicResponse = await api.get('/likes/popular/music?timeRange=30&limit=5');
    console.log('✅ Popular music retrieved');
    console.log(`   Popular music count: ${popularMusicResponse.data.data.length}`);

    // 获取热门评论
    const popularCommentsResponse = await api.get('/likes/popular/comment?timeRange=30&limit=5');
    console.log('✅ Popular comments retrieved');
    console.log(`   Popular comments count: ${popularCommentsResponse.data.data.length}`);

  } catch (error) {
    console.error('❌ Popular targets test failed');
    throw error;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 Starting Like & Share System Tests...');
  console.log('=====================================');

  try {
    await setupTestUser();
    await getTestData();
    await testLikeMusic();
    await testLikeComment();
    await testBatchCheckLikes();
    await testUserLikeStats();
    await testShareFunctionality();
    await testPopularTargets();

    console.log('\n🎉 All tests completed successfully!');
    console.log('=====================================');

  } catch (error) {
    console.error('\n💥 Test suite failed:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests
};
