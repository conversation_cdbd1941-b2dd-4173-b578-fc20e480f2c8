const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// 配置
const BASE_URL = 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api/v1`;

// 测试用户凭据
const TEST_USER = {
  username: 'testuser',
  password: 'password123'
};

let authToken = '';

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠ ${msg}${colors.reset}`),
  test: (msg) => console.log(`${colors.cyan}🧪 ${msg}${colors.reset}`)
};

// 登录获取token
async function login() {
  try {
    log.info('正在登录获取访问令牌...');
    
    const response = await axios.post(`${API_BASE}/auth/login`, {
      username: TEST_USER.username,
      password: TEST_USER.password
    });
    
    if (response.data.success && response.data.token) {
      authToken = response.data.token;
      log.success('登录成功');
      return true;
    } else {
      log.error('登录失败: ' + response.data.message);
      return false;
    }
  } catch (error) {
    log.error('登录请求失败: ' + error.message);
    return false;
  }
}

// 创建axios实例
function createAuthenticatedAxios() {
  return axios.create({
    baseURL: API_BASE,
    headers: {
      'Authorization': `Bearer ${authToken}`
    }
  });
}

// 测试获取支持的格式信息
async function testGetFormats() {
  try {
    log.test('测试获取支持的音频格式信息');
    
    const api = createAuthenticatedAxios();
    const response = await api.get('/audio-quality/formats');
    
    if (response.data.success) {
      log.success('获取格式信息成功');
      console.log('支持的格式:', Object.keys(response.data.data.supportedFormats));
      return true;
    } else {
      log.error('获取格式信息失败: ' + response.data.message);
      return false;
    }
  } catch (error) {
    log.error('获取格式信息请求失败: ' + error.message);
    return false;
  }
}

// 测试质量统计
async function testGetStatistics() {
  try {
    log.test('测试获取质量统计信息');
    
    const api = createAuthenticatedAxios();
    const response = await api.get('/audio-quality/statistics');
    
    if (response.data.success) {
      log.success('获取统计信息成功');
      console.log('总音乐数量:', response.data.data.totalMusic);
      console.log('平均质量分数:', response.data.data.averageQualityScore);
      return true;
    } else {
      log.error('获取统计信息失败: ' + response.data.message);
      return false;
    }
  } catch (error) {
    log.error('获取统计信息请求失败: ' + error.message);
    return false;
  }
}

// 创建测试音频文件
async function createTestAudio() {
  try {
    log.info('创建测试音频文件...');
    
    // 检查是否有现有的测试文件
    const testDir = path.join(__dirname, 'test-audio');
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
    
    // 创建一个简单的测试音频文件（模拟）
    // 注意：这里创建的是一个假的音频文件，仅用于测试API
    const testFiles = [
      { name: 'test_128k.mp3', content: 'fake mp3 content for testing' },
      { name: 'test_320k.mp3', content: 'fake high quality mp3 content for testing' },
      { name: 'test_lossless.flac', content: 'fake flac content for testing' }
    ];
    
    for (const file of testFiles) {
      const filePath = path.join(testDir, file.name);
      fs.writeFileSync(filePath, file.content);
    }
    
    log.success('测试音频文件创建完成');
    return true;
  } catch (error) {
    log.error('创建测试音频文件失败: ' + error.message);
    return false;
  }
}

// 测试单个文件质量分析
async function testAnalyzeAudio(fileName, description) {
  try {
    log.test(`测试分析音频文件: ${description}`);
    
    const filePath = path.join(__dirname, 'test-audio', fileName);
    
    if (!fs.existsSync(filePath)) {
      log.warning(`测试文件不存在: ${fileName}`);
      return false;
    }
    
    const formData = new FormData();
    formData.append('audio', fs.createReadStream(filePath));
    
    const response = await axios.post(`${API_BASE}/audio-quality/analyze`, formData, {
      headers: {
        ...formData.getHeaders(),
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    if (response.data.success) {
      log.success(`分析成功: ${description}`);
      const analysis = response.data.data.analysis;
      console.log(`   质量等级: ${analysis.qualityLevel || 'N/A'}`);
      console.log(`   质量分数: ${analysis.qualityScore || 'N/A'}`);
      console.log(`   文件有效: ${analysis.isValid || 'N/A'}`);
      return true;
    } else {
      log.error(`分析失败: ${response.data.message}`);
      return false;
    }
  } catch (error) {
    log.error(`分析音频文件请求失败: ${error.message}`);
    return false;
  }
}

// 测试管理员功能
async function testAdminOverview() {
  try {
    log.test('测试管理员质量概览');
    
    const api = createAuthenticatedAxios();
    const response = await api.get('/audio-quality/admin/overview');
    
    if (response.data.success) {
      log.success('获取管理员概览成功');
      console.log('概览数据:', response.data.data.overview);
      return true;
    } else {
      log.warning('获取管理员概览失败 (可能没有管理员权限): ' + response.data.message);
      return false;
    }
  } catch (error) {
    log.warning('管理员概览请求失败 (可能没有管理员权限): ' + error.message);
    return false;
  }
}

// 清理测试文件
function cleanupTestFiles() {
  try {
    log.info('清理测试文件...');
    const testDir = path.join(__dirname, 'test-audio');
    if (fs.existsSync(testDir)) {
      fs.rmSync(testDir, { recursive: true, force: true });
    }
    log.success('清理完成');
  } catch (error) {
    log.warning('清理测试文件失败: ' + error.message);
  }
}

// 主测试函数
async function runTests() {
  console.log(`${colors.blue}🎵 MusicDou 音频质量检测功能测试${colors.reset}`);
  console.log('='.repeat(50));
  
  let passedTests = 0;
  let totalTests = 0;
  
  // 1. 登录
  totalTests++;
  if (await login()) {
    passedTests++;
  } else {
    log.error('登录失败，无法继续测试');
    return;
  }
  
  console.log('='.repeat(50));
  
  // 2. 测试获取格式信息
  totalTests++;
  if (await testGetFormats()) {
    passedTests++;
  }
  
  console.log('='.repeat(50));
  
  // 3. 测试获取统计信息
  totalTests++;
  if (await testGetStatistics()) {
    passedTests++;
  }
  
  console.log('='.repeat(50));
  
  // 4. 创建测试文件
  totalTests++;
  if (await createTestAudio()) {
    passedTests++;
  }
  
  console.log('='.repeat(50));
  
  // 5. 测试音频分析
  const testFiles = [
    { name: 'test_128k.mp3', desc: '128k MP3文件' },
    { name: 'test_320k.mp3', desc: '320k MP3文件' },
    { name: 'test_lossless.flac', desc: 'FLAC无损文件' }
  ];
  
  for (const file of testFiles) {
    totalTests++;
    if (await testAnalyzeAudio(file.name, file.desc)) {
      passedTests++;
    }
    console.log('-'.repeat(30));
  }
  
  console.log('='.repeat(50));
  
  // 6. 测试管理员功能
  totalTests++;
  if (await testAdminOverview()) {
    passedTests++;
  }
  
  console.log('='.repeat(50));
  
  // 7. 清理
  cleanupTestFiles();
  
  // 测试结果
  console.log(`${colors.magenta}📊 测试结果${colors.reset}`);
  console.log(`通过: ${passedTests}/${totalTests}`);
  console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    log.success('🎉 所有测试通过！');
  } else {
    log.warning(`⚠ ${totalTests - passedTests} 个测试失败`);
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    log.error('测试运行失败: ' + error.message);
    process.exit(1);
  });
}

module.exports = {
  runTests,
  login,
  testGetFormats,
  testGetStatistics,
  testAnalyzeAudio,
  testAdminOverview
};
