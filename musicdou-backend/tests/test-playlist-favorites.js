const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';

// 测试用户凭据
let authToken = '';
let testUserId = '';
let testPlaylistId = '';
let secondUserToken = '';

// 辅助函数：发送请求
async function makeRequest(method, url, data = null, token = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: {}
    };
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }
    
    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status
    };
  }
}

// 测试用户登录
async function testLogin() {
  console.log('\n🔐 Testing user login...');
  
  const result = await makeRequest('POST', '/auth/login', {
    identifier: '<EMAIL>',
    password: 'password123'
  });
  
  if (result.success) {
    authToken = result.data.data.token;
    testUserId = result.data.data.user._id || result.data.data.user.id;
    console.log('✅ Login successful');
    console.log(`   User ID: ${testUserId}`);
    return true;
  } else {
    console.log('❌ Login failed:', result.error);
    return false;
  }
}

// 创建第二个测试用户
async function createSecondUser() {
  console.log('\n👤 Creating second test user...');
  
  const registerResult = await makeRequest('POST', '/auth/register', {
    username: 'testuser2',
    email: '<EMAIL>',
    password: 'password123'
  });
  
  if (registerResult.success) {
    secondUserToken = registerResult.data.data.token;
    console.log('✅ Second user created and logged in');
    return true;
  } else {
    // 尝试登录已存在的用户
    const loginResult = await makeRequest('POST', '/auth/login', {
      identifier: '<EMAIL>',
      password: 'password123'
    });
    
    if (loginResult.success) {
      secondUserToken = loginResult.data.data.token;
      console.log('✅ Second user login successful');
      return true;
    } else {
      console.log('❌ Failed to create/login second user:', loginResult.error);
      return false;
    }
  }
}

// 创建测试歌单（用第一个用户）
async function createTestPlaylist() {
  console.log('\n📝 Creating test playlist...');
  
  const playlistData = {
    name: '收藏测试歌单',
    description: '用于测试收藏功能的公开歌单',
    isPublic: true,
    tags: ['测试', '收藏'],
    category: 'pop'
  };
  
  const result = await makeRequest('POST', '/playlists', playlistData, authToken);
  
  if (result.success) {
    testPlaylistId = result.data.data.playlist._id;
    console.log('✅ Test playlist created');
    console.log(`   Playlist ID: ${testPlaylistId}`);
    console.log(`   Name: ${result.data.data.playlist.name}`);
    return true;
  } else {
    console.log('❌ Failed to create test playlist:', result.error);
    return false;
  }
}

// 测试收藏歌单（用第二个用户）
async function testFavoritePlaylist() {
  console.log('\n⭐ Testing favorite playlist...');
  
  const result = await makeRequest('POST', `/playlists/${testPlaylistId}/favorite`, null, secondUserToken);
  
  if (result.success) {
    console.log('✅ Playlist favorited successfully');
    console.log(`   Playlist: ${result.data.data.playlist.name}`);
    console.log(`   New favorite count: ${result.data.data.playlist.favoriteCount}`);
    return true;
  } else {
    console.log('❌ Failed to favorite playlist:', result.error);
    return false;
  }
}

// 测试重复收藏（应该失败）
async function testDuplicateFavorite() {
  console.log('\n🔄 Testing duplicate favorite (should fail)...');
  
  const result = await makeRequest('POST', `/playlists/${testPlaylistId}/favorite`, null, secondUserToken);
  
  if (!result.success && result.error.error === 'Already Favorited') {
    console.log('✅ Duplicate favorite correctly rejected');
    console.log(`   Message: ${result.error.message}`);
    return true;
  } else {
    console.log('❌ Duplicate favorite should have been rejected');
    return false;
  }
}

// 测试获取用户收藏的歌单
async function testGetUserFavorites() {
  console.log('\n📋 Testing get user favorites...');
  
  const result = await makeRequest('GET', '/playlists/favorites', null, secondUserToken);
  
  if (result.success) {
    console.log('✅ Got user favorites successfully');
    console.log(`   Total favorites: ${result.data.data.pagination.total}`);
    
    if (result.data.data.playlists.length > 0) {
      result.data.data.playlists.forEach((playlist, index) => {
        console.log(`   ${index + 1}. ${playlist.name} by ${playlist.createdBy.username}`);
        console.log(`      Favorited at: ${new Date(playlist.favoritedAt).toLocaleString()}`);
      });
    }
    
    return true;
  } else {
    console.log('❌ Failed to get user favorites:', result.error);
    return false;
  }
}

// 测试歌单详情中的收藏状态
async function testPlaylistFavoriteStatus() {
  console.log('\n🔍 Testing playlist favorite status...');
  
  const result = await makeRequest('GET', `/playlists/${testPlaylistId}`, null, secondUserToken);
  
  if (result.success) {
    const playlist = result.data.data.playlist;
    console.log('✅ Got playlist with favorite status');
    console.log(`   Playlist: ${playlist.name}`);
    console.log(`   Is favorited: ${playlist.isFavorited}`);
    console.log(`   Favorite count: ${playlist.favoriteCount}`);
    return true;
  } else {
    console.log('❌ Failed to get playlist favorite status:', result.error);
    return false;
  }
}

// 测试取消收藏
async function testUnfavoritePlaylist() {
  console.log('\n💔 Testing unfavorite playlist...');
  
  const result = await makeRequest('DELETE', `/playlists/${testPlaylistId}/favorite`, null, secondUserToken);
  
  if (result.success) {
    console.log('✅ Playlist unfavorited successfully');
    console.log(`   Playlist: ${result.data.data.playlist.name}`);
    console.log(`   New favorite count: ${result.data.data.playlist.favoriteCount}`);
    return true;
  } else {
    console.log('❌ Failed to unfavorite playlist:', result.error);
    return false;
  }
}

// 测试收藏自己的歌单（应该失败）
async function testFavoriteOwnPlaylist() {
  console.log('\n🚫 Testing favorite own playlist (should fail)...');
  
  const result = await makeRequest('POST', `/playlists/${testPlaylistId}/favorite`, null, authToken);
  
  if (!result.success && result.error.error === 'Invalid Operation') {
    console.log('✅ Favorite own playlist correctly rejected');
    console.log(`   Message: ${result.error.message}`);
    return true;
  } else {
    console.log('❌ Favorite own playlist should have been rejected');
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('⭐ Starting Playlist Favorite Tests...');
  console.log('=====================================');
  
  // 测试登录
  const loginSuccess = await testLogin();
  if (!loginSuccess) {
    console.log('\n❌ Cannot proceed without authentication');
    return;
  }
  
  // 创建第二个用户
  const secondUserSuccess = await createSecondUser();
  if (!secondUserSuccess) {
    console.log('\n❌ Cannot proceed without second user');
    return;
  }
  
  // 创建测试歌单
  const playlistSuccess = await createTestPlaylist();
  if (!playlistSuccess) {
    console.log('\n❌ Cannot proceed without test playlist');
    return;
  }
  
  // 测试收藏歌单
  await testFavoritePlaylist();
  
  // 测试重复收藏
  await testDuplicateFavorite();
  
  // 测试获取用户收藏
  await testGetUserFavorites();
  
  // 测试歌单收藏状态
  await testPlaylistFavoriteStatus();
  
  // 测试取消收藏
  await testUnfavoritePlaylist();
  
  // 测试收藏自己的歌单
  await testFavoriteOwnPlaylist();
  
  // 最终检查收藏列表
  console.log('\n📋 Final check - user favorites after unfavorite...');
  await testGetUserFavorites();
  
  console.log('\n=====================================');
  console.log('🎉 Playlist Favorite Tests Completed!');
}

// 运行测试
runTests().catch(console.error);
