#!/usr/bin/env node

/**
 * 高级搜索功能测试脚本
 * 测试新增的高级搜索和过滤功能
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';

/**
 * 测试基本搜索功能
 */
async function testBasicSearch() {
  console.log('\n🔍 Testing: Basic search');
  try {
    const response = await axios.get(`${BASE_URL}/music/search`, {
      params: { q: '音乐', page: 1, limit: 5 }
    });
    
    console.log('✅ Basic search successful');
    console.log(`   Found ${response.data.data.pagination.totalCount} results`);
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Basic search failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试高级搜索功能
 */
async function testAdvancedSearch() {
  console.log('\n🔍 Testing: Advanced search');
  try {
    const response = await axios.get(`${BASE_URL}/music/search/advanced`, {
      params: {
        q: '音乐',
        quality: 'high',
        minDuration: 180, // 3分钟以上
        maxDuration: 300, // 5分钟以下
        sortBy: 'popular',
        page: 1,
        limit: 5
      }
    });
    
    console.log('✅ Advanced search successful');
    console.log(`   Found ${response.data.data.pagination.totalCount} results`);
    console.log(`   Filters applied: quality=${response.data.data.filters.quality}, duration=3-5min`);
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Advanced search failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试搜索建议功能
 */
async function testSearchSuggestions() {
  console.log('\n💡 Testing: Search suggestions');
  try {
    const response = await axios.get(`${BASE_URL}/music/search/suggestions`, {
      params: { q: '周', type: 'all', limit: 5 }
    });
    
    console.log('✅ Search suggestions successful');
    console.log(`   Artists: ${response.data.data.artists.length}`);
    console.log(`   Albums: ${response.data.data.albums.length}`);
    console.log(`   Titles: ${response.data.data.titles.length}`);
    console.log(`   Genres: ${response.data.data.genres.length}`);
    
    if (response.data.data.artists.length > 0) {
      console.log(`   Sample artist: ${response.data.data.artists[0]}`);
    }
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Search suggestions failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试过滤器选项
 */
async function testFilterOptions() {
  console.log('\n⚙️ Testing: Filter options');
  try {
    const response = await axios.get(`${BASE_URL}/music/filters`);
    
    console.log('✅ Filter options retrieved successfully');
    console.log(`   Qualities: ${response.data.data.qualities.join(', ')}`);
    console.log(`   Genres count: ${response.data.data.genres.length}`);
    console.log(`   Artists count: ${response.data.data.artists.length}`);
    console.log(`   Year range: ${response.data.data.yearRange.minYear} - ${response.data.data.yearRange.maxYear}`);
    console.log(`   Duration range: ${response.data.data.durationRange.minDuration}s - ${response.data.data.durationRange.maxDuration}s`);
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Filter options failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试相似音乐功能
 */
async function testSimilarMusic() {
  console.log('\n🎵 Testing: Similar music');
  try {
    // 先获取一个音乐ID
    const searchResponse = await axios.get(`${BASE_URL}/music`, {
      params: { limit: 1 }
    });
    
    if (searchResponse.data.data.music.length === 0) {
      console.log('ℹ️  No music found for similar music test');
      return null;
    }
    
    const musicId = searchResponse.data.data.music[0]._id;
    const response = await axios.get(`${BASE_URL}/music/${musicId}/similar`, {
      params: { limit: 5 }
    });
    
    console.log('✅ Similar music retrieved successfully');
    console.log(`   Found ${response.data.data.length} similar music`);
    
    if (response.data.data.length > 0) {
      console.log(`   Sample: ${response.data.data[0].title} by ${response.data.data[0].artist}`);
    }
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Similar music failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试精确匹配搜索
 */
async function testExactMatchSearch() {
  console.log('\n🎯 Testing: Exact match search');
  try {
    const response = await axios.get(`${BASE_URL}/music/search/advanced`, {
      params: {
        q: '夜曲',
        exactMatch: true,
        searchFields: 'title',
        limit: 5
      }
    });
    
    console.log('✅ Exact match search successful');
    console.log(`   Found ${response.data.data.pagination.totalCount} exact matches`);
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Exact match search failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试多条件组合搜索
 */
async function testMultiFilterSearch() {
  console.log('\n🔧 Testing: Multi-filter search');
  try {
    const response = await axios.get(`${BASE_URL}/music/search/advanced`, {
      params: {
        q: '音乐',
        quality: 'high',
        genre: 'Pop',
        minBitrate: 192,
        hasLyrics: true,
        sortBy: 'newest',
        limit: 5
      }
    });
    
    console.log('✅ Multi-filter search successful');
    console.log(`   Found ${response.data.data.pagination.totalCount} results`);
    console.log(`   Filters: quality=high, genre=Pop, bitrate>=192, hasLyrics=true`);
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Multi-filter search failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 Starting Advanced Search Tests\n');
  
  await testBasicSearch();
  await testAdvancedSearch();
  await testSearchSuggestions();
  await testFilterOptions();
  await testSimilarMusic();
  await testExactMatchSearch();
  await testMultiFilterSearch();
  
  console.log('\n🎉 Advanced Search Tests Completed!');
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  testBasicSearch,
  testAdvancedSearch,
  testSearchSuggestions,
  testFilterOptions,
  testSimilarMusic,
  testExactMatchSearch,
  testMultiFilterSearch
};
