#!/bin/bash

# 通知系统测试脚本
# 用于测试通知系统的各项功能

echo "🔔 通知系统测试脚本"
echo "===================="

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查服务器是否运行
echo "📡 检查服务器状态..."
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ 服务器正在运行"
else
    echo "❌ 服务器未运行，请先启动服务器: npm run dev"
    exit 1
fi

# 运行通知系统测试
echo ""
echo "🚀 开始运行通知系统测试..."
echo ""

node test-notification-system.js

# 检查测试结果
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 通知系统测试完成！"
    echo ""
    echo "📋 测试覆盖的功能："
    echo "   ✅ 通知模型和数据库设计"
    echo "   ✅ 通知生成服务"
    echo "   ✅ 通知API接口"
    echo "   ✅ 通知设置管理"
    echo "   ✅ 通知统计和查询"
    echo "   ✅ 系统通知和成就通知"
    echo ""
    echo "🔧 下一步可以测试："
    echo "   - 实时通知推送功能"
    echo "   - 与其他系统的集成（关注、点赞、评论等）"
    echo "   - 通知频率限制和免打扰功能"
    echo ""
else
    echo ""
    echo "💥 通知系统测试失败！"
    echo "请检查错误信息并修复问题后重新运行测试。"
    echo ""
    exit 1
fi
