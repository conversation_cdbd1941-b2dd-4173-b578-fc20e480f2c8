#!/bin/bash

# 关注系统测试脚本
# 用于测试MusicDou关注系统的所有功能

echo "🚀 MusicDou 关注系统测试"
echo "=========================="

# 检查服务器是否运行
echo "📡 检查服务器状态..."
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ 服务器正在运行"
else
    echo "❌ 服务器未运行，请先启动服务器"
    echo "   运行命令: npm run dev"
    exit 1
fi

# 检查Node.js依赖
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装"
    exit 1
fi

# 检查axios依赖
if ! node -e "require('axios')" 2>/dev/null; then
    echo "📦 安装测试依赖..."
    npm install axios
fi

echo ""
echo "🧪 开始执行关注系统测试..."
echo "================================"

# 运行测试脚本
node test-follow-system.js

echo ""
echo "📋 测试完成！"
echo ""
echo "🔍 手动测试命令："
echo "================================"
echo ""

# 获取用户token（需要先运行测试脚本）
echo "# 1. 获取用户token"
echo "curl -X POST http://localhost:3000/api/v1/auth/login \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"username\":\"testuser1\",\"password\":\"password123\"}'"
echo ""

echo "# 2. 关注用户 (需要替换USER_ID和TOKEN)"
echo "curl -X POST http://localhost:3000/api/v1/follows/USER_ID \\"
echo "  -H 'Authorization: Bearer TOKEN' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"source\":\"manual\"}'"
echo ""

echo "# 3. 检查关注状态"
echo "curl -X GET http://localhost:3000/api/v1/follows/USER_ID/status \\"
echo "  -H 'Authorization: Bearer TOKEN'"
echo ""

echo "# 4. 获取关注列表"
echo "curl -X GET 'http://localhost:3000/api/v1/follows/USER_ID/following?page=1&limit=10' \\"
echo "  -H 'Authorization: Bearer TOKEN'"
echo ""

echo "# 5. 获取粉丝列表"
echo "curl -X GET 'http://localhost:3000/api/v1/follows/USER_ID/followers?page=1&limit=10' \\"
echo "  -H 'Authorization: Bearer TOKEN'"
echo ""

echo "# 6. 获取相互关注列表"
echo "curl -X GET 'http://localhost:3000/api/v1/follows/mutual?page=1&limit=10' \\"
echo "  -H 'Authorization: Bearer TOKEN'"
echo ""

echo "# 7. 获取用户统计"
echo "curl -X GET http://localhost:3000/api/v1/follows/USER_ID/stats \\"
echo "  -H 'Authorization: Bearer TOKEN'"
echo ""

echo "# 8. 获取推荐用户"
echo "curl -X GET 'http://localhost:3000/api/v1/follows/recommendations?limit=10' \\"
echo "  -H 'Authorization: Bearer TOKEN'"
echo ""

echo "# 9. 批量关注用户"
echo "curl -X POST http://localhost:3000/api/v1/follows/batch \\"
echo "  -H 'Authorization: Bearer TOKEN' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"userIds\":[\"USER_ID1\",\"USER_ID2\"],\"source\":\"recommendation\"}'"
echo ""

echo "# 10. 取消关注用户"
echo "curl -X DELETE http://localhost:3000/api/v1/follows/USER_ID \\"
echo "  -H 'Authorization: Bearer TOKEN'"
echo ""

echo "📚 API文档："
echo "================================"
echo "关注系统提供以下API接口："
echo ""
echo "POST   /api/v1/follows/:userId          - 关注用户"
echo "DELETE /api/v1/follows/:userId          - 取消关注用户"
echo "GET    /api/v1/follows/:userId/following - 获取关注列表"
echo "GET    /api/v1/follows/:userId/followers - 获取粉丝列表"
echo "GET    /api/v1/follows/mutual           - 获取相互关注列表"
echo "GET    /api/v1/follows/:userId/status   - 检查关注状态"
echo "GET    /api/v1/follows/:userId/stats    - 获取用户统计"
echo "GET    /api/v1/follows/recommendations  - 获取推荐用户"
echo "POST   /api/v1/follows/batch            - 批量关注用户"
echo ""

echo "✨ 关注系统测试完成！"
