const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';

// 测试用户凭据
let authToken = '';
let testUserId = '';
let testPlaylistId = '';

// 辅助函数：发送请求
async function makeRequest(method, url, data = null, token = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: {}
    };
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }
    
    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status
    };
  }
}

// 测试用户登录
async function testLogin() {
  console.log('\n🔐 Testing user login...');
  
  const result = await makeRequest('POST', '/auth/login', {
    identifier: '<EMAIL>',
    password: 'password123'
  });
  
  if (result.success) {
    authToken = result.data.data.token;
    testUserId = result.data.data.user._id || result.data.data.user.id;
    console.log('✅ Login successful');
    console.log(`   Token: ${authToken.substring(0, 20)}...`);
    console.log(`   User ID: ${testUserId}`);
  } else {
    console.log('❌ Login failed:', result.error);
    
    // 尝试注册新用户
    console.log('\n📝 Trying to register new user...');
    const registerResult = await makeRequest('POST', '/auth/register', {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123'
    });
    
    if (registerResult.success) {
      authToken = registerResult.data.data.token;
      testUserId = registerResult.data.data.user._id || registerResult.data.data.user.id;
      console.log('✅ Registration successful');
      console.log(`   Token: ${authToken.substring(0, 20)}...`);
      console.log(`   User ID: ${testUserId}`);
    } else {
      console.log('❌ Registration failed:', registerResult.error);
      return false;
    }
  }
  
  return true;
}

// 测试创建歌单
async function testCreatePlaylist() {
  console.log('\n📝 Testing playlist creation...');
  
  const playlistData = {
    name: '我的测试歌单',
    description: '这是一个用于测试的歌单',
    isPublic: true,
    tags: ['测试', '流行'],
    category: 'pop'
  };
  
  const result = await makeRequest('POST', '/playlists', playlistData, authToken);
  
  if (result.success) {
    testPlaylistId = result.data.data.playlist._id;
    console.log('✅ Playlist created successfully');
    console.log(`   Playlist ID: ${testPlaylistId}`);
    console.log(`   Name: ${result.data.data.playlist.name}`);
    console.log(`   Description: ${result.data.data.playlist.description}`);
    console.log(`   Tags: ${result.data.data.playlist.tags.join(', ')}`);
  } else {
    console.log('❌ Playlist creation failed:', result.error);
    return false;
  }
  
  return true;
}

// 测试获取歌单列表
async function testGetPlaylists() {
  console.log('\n📋 Testing get playlists...');
  
  const result = await makeRequest('GET', '/playlists?page=1&limit=10');
  
  if (result.success) {
    console.log('✅ Get playlists successful');
    console.log(`   Total playlists: ${result.data.data.pagination.total}`);
    console.log(`   Current page: ${result.data.data.pagination.page}`);
    
    if (result.data.data.playlists.length > 0) {
      const playlist = result.data.data.playlists[0];
      console.log(`   First playlist: ${playlist.name} by ${playlist.createdBy.username}`);
    }
  } else {
    console.log('❌ Get playlists failed:', result.error);
    return false;
  }
  
  return true;
}

// 测试获取歌单详情
async function testGetPlaylistById() {
  console.log('\n🔍 Testing get playlist by ID...');
  
  if (!testPlaylistId) {
    console.log('❌ No test playlist ID available');
    return false;
  }
  
  const result = await makeRequest('GET', `/playlists/${testPlaylistId}`, null, authToken);
  
  if (result.success) {
    console.log('✅ Get playlist by ID successful');
    console.log(`   Name: ${result.data.data.playlist.name}`);
    console.log(`   Creator: ${result.data.data.playlist.createdBy.username}`);
    console.log(`   Songs count: ${result.data.data.playlist.songCount}`);
    console.log(`   Is favorited: ${result.data.data.playlist.isFavorited}`);
  } else {
    console.log('❌ Get playlist by ID failed:', result.error);
    return false;
  }
  
  return true;
}

// 测试更新歌单
async function testUpdatePlaylist() {
  console.log('\n✏️ Testing playlist update...');
  
  if (!testPlaylistId) {
    console.log('❌ No test playlist ID available');
    return false;
  }
  
  const updateData = {
    name: '我的更新测试歌单',
    description: '这是一个更新后的测试歌单',
    tags: ['测试', '流行', '更新']
  };
  
  const result = await makeRequest('PUT', `/playlists/${testPlaylistId}`, updateData, authToken);
  
  if (result.success) {
    console.log('✅ Playlist update successful');
    console.log(`   Updated name: ${result.data.data.playlist.name}`);
    console.log(`   Updated description: ${result.data.data.playlist.description}`);
    console.log(`   Updated tags: ${result.data.data.playlist.tags.join(', ')}`);
  } else {
    console.log('❌ Playlist update failed:', result.error);
    return false;
  }
  
  return true;
}

// 测试获取用户歌单
async function testGetUserPlaylists() {
  console.log('\n👤 Testing get user playlists...');
  
  const result = await makeRequest('GET', `/playlists/user/${testUserId}`, null, authToken);
  
  if (result.success) {
    console.log('✅ Get user playlists successful');
    console.log(`   Total playlists: ${result.data.data.pagination.total}`);
    
    if (result.data.data.playlists.length > 0) {
      result.data.data.playlists.forEach((playlist, index) => {
        console.log(`   ${index + 1}. ${playlist.name} (${playlist.isPublic ? 'Public' : 'Private'})`);
      });
    }
  } else {
    console.log('❌ Get user playlists failed:', result.error);
    return false;
  }
  
  return true;
}

// 测试获取热门歌单
async function testGetPopularPlaylists() {
  console.log('\n🔥 Testing get popular playlists...');
  
  const result = await makeRequest('GET', '/playlists/popular?page=1&limit=5');
  
  if (result.success) {
    console.log('✅ Get popular playlists successful');
    console.log(`   Total playlists: ${result.data.data.pagination.total}`);
    
    if (result.data.data.playlists.length > 0) {
      result.data.data.playlists.forEach((playlist, index) => {
        console.log(`   ${index + 1}. ${playlist.name} (Play: ${playlist.playCount}, Favorite: ${playlist.favoriteCount})`);
      });
    }
  } else {
    console.log('❌ Get popular playlists failed:', result.error);
    return false;
  }
  
  return true;
}

// 主测试函数
async function runTests() {
  console.log('🎵 Starting Playlist API Tests...');
  console.log('=====================================');
  
  // 测试登录
  const loginSuccess = await testLogin();
  if (!loginSuccess) {
    console.log('\n❌ Cannot proceed without authentication');
    return;
  }
  
  // 测试创建歌单
  await testCreatePlaylist();
  
  // 测试获取歌单列表
  await testGetPlaylists();
  
  // 测试获取歌单详情
  await testGetPlaylistById();
  
  // 测试更新歌单
  await testUpdatePlaylist();
  
  // 测试获取用户歌单
  await testGetUserPlaylists();
  
  // 测试获取热门歌单
  await testGetPopularPlaylists();
  
  console.log('\n=====================================');
  console.log('🎉 Playlist API Tests Completed!');
}

// 运行测试
runTests().catch(console.error);
