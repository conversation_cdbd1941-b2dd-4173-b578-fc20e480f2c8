#!/usr/bin/env node

/**
 * 音乐推荐算法测试脚本
 * 测试新增的推荐系统功能
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';

// 测试用户凭据
const USER_CREDENTIALS = {
  username: 'testuser',
  password: 'password123'
};

let userToken = '';

/**
 * 登录获取token
 */
async function login(credentials) {
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, credentials);
    return response.data.data.token;
  } catch (error) {
    console.error('Login failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试个性化推荐
 */
async function testPersonalizedRecommendations() {
  console.log('\n🎯 Testing: Personalized recommendations');
  try {
    const response = await axios.get(`${BASE_URL}/music/recommendations`, {
      headers: { Authorization: `Bearer ${userToken}` },
      params: { limit: 10, algorithm: 'hybrid' }
    });
    
    console.log('✅ Personalized recommendations retrieved successfully');
    console.log(`   Algorithm: ${response.data.data.algorithm}`);
    console.log(`   Count: ${response.data.data.count}`);
    
    if (response.data.data.recommendations.length > 0) {
      const sample = response.data.data.recommendations[0];
      console.log(`   Sample: ${sample.title} by ${sample.artist}`);
      console.log(`   Reason: ${sample.recommendationReason || 'N/A'}`);
    }
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Personalized recommendations failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试基于流派的推荐
 */
async function testGenreBasedRecommendations() {
  console.log('\n🎵 Testing: Genre-based recommendations');
  try {
    const response = await axios.get(`${BASE_URL}/music/recommendations/genre`, {
      headers: { Authorization: `Bearer ${userToken}` },
      params: { genre: 'Pop', limit: 5 }
    });
    
    console.log('✅ Genre-based recommendations retrieved successfully');
    console.log(`   Found ${response.data.data.length} Pop music recommendations`);
    
    if (response.data.data.length > 0) {
      console.log(`   Sample: ${response.data.data[0].title} by ${response.data.data[0].artist}`);
    }
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Genre-based recommendations failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试热门趋势音乐
 */
async function testTrendingMusic() {
  console.log('\n📈 Testing: Trending music');
  try {
    const response = await axios.get(`${BASE_URL}/music/trending`, {
      params: { period: '7d', limit: 10 }
    });
    
    console.log('✅ Trending music retrieved successfully');
    console.log(`   Found ${response.data.data.length} trending music`);
    
    if (response.data.data.length > 0) {
      const sample = response.data.data[0];
      console.log(`   Top trending: ${sample.title} by ${sample.artist}`);
      console.log(`   Trending score: ${sample.trendingScore || 'N/A'}`);
      if (sample.periodStats) {
        console.log(`   Stats: ${sample.periodStats.plays} plays, ${sample.periodStats.likes} likes`);
      }
    }
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Trending music failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试新发现音乐
 */
async function testDiscoverMusic() {
  console.log('\n🔍 Testing: Discover music');
  try {
    const response = await axios.get(`${BASE_URL}/music/discover`, {
      headers: { Authorization: `Bearer ${userToken}` },
      params: { limit: 10 }
    });
    
    console.log('✅ Discover music retrieved successfully');
    console.log(`   Found ${response.data.data.length} new music to discover`);
    
    if (response.data.data.length > 0) {
      console.log(`   Sample: ${response.data.data[0].title} by ${response.data.data[0].artist}`);
    }
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Discover music failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试播放行为记录
 */
async function testPlayBehaviorRecording() {
  console.log('\n📊 Testing: Play behavior recording');
  try {
    // 先获取一个音乐ID
    const musicResponse = await axios.get(`${BASE_URL}/music`, {
      params: { limit: 1 }
    });
    
    if (musicResponse.data.data.music.length === 0) {
      console.log('ℹ️  No music found for behavior recording test');
      return null;
    }
    
    const musicId = musicResponse.data.data.music[0]._id;
    
    const response = await axios.post(`${BASE_URL}/music/${musicId}/play-behavior`, {
      duration: 180, // 3 minutes
      completed: false
    }, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    
    console.log('✅ Play behavior recorded successfully');
    console.log(`   Music: ${musicResponse.data.data.music[0].title}`);
    console.log(`   Duration: 180 seconds`);
    
    return response.data;
  } catch (error) {
    console.error('❌ Play behavior recording failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试不同推荐算法
 */
async function testDifferentAlgorithms() {
  console.log('\n🧠 Testing: Different recommendation algorithms');
  
  const algorithms = ['collaborative', 'content', 'hybrid'];
  
  for (const algorithm of algorithms) {
    try {
      const response = await axios.get(`${BASE_URL}/music/recommendations`, {
        headers: { Authorization: `Bearer ${userToken}` },
        params: { limit: 5, algorithm }
      });
      
      console.log(`✅ ${algorithm} algorithm: ${response.data.data.count} recommendations`);
    } catch (error) {
      console.error(`❌ ${algorithm} algorithm failed:`, error.response?.data?.message || error.message);
    }
  }
}

/**
 * 测试相似音乐功能
 */
async function testSimilarMusic() {
  console.log('\n🎼 Testing: Similar music');
  try {
    // 先获取一个音乐ID
    const musicResponse = await axios.get(`${BASE_URL}/music`, {
      params: { limit: 1 }
    });
    
    if (musicResponse.data.data.music.length === 0) {
      console.log('ℹ️  No music found for similar music test');
      return null;
    }
    
    const musicId = musicResponse.data.data.music[0]._id;
    const response = await axios.get(`${BASE_URL}/music/${musicId}/similar`, {
      params: { limit: 5 }
    });
    
    console.log('✅ Similar music retrieved successfully');
    console.log(`   Found ${response.data.data.length} similar music`);
    console.log(`   Based on: ${musicResponse.data.data.music[0].title}`);
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Similar music failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 Starting Music Recommendation Tests\n');
  
  // 登录获取token
  console.log('🔐 Logging in...');
  userToken = await login(USER_CREDENTIALS);
  
  if (!userToken) {
    console.error('❌ Failed to get user token. Please ensure test user exists.');
    return;
  }
  
  console.log('✅ Login successful');
  
  // 运行测试
  await testTrendingMusic();
  await testGenreBasedRecommendations();
  await testSimilarMusic();
  await testPlayBehaviorRecording();
  await testPersonalizedRecommendations();
  await testDiscoverMusic();
  await testDifferentAlgorithms();
  
  console.log('\n🎉 Music Recommendation Tests Completed!');
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  testPersonalizedRecommendations,
  testGenreBasedRecommendations,
  testTrendingMusic,
  testDiscoverMusic,
  testPlayBehaviorRecording,
  testDifferentAlgorithms,
  testSimilarMusic
};
