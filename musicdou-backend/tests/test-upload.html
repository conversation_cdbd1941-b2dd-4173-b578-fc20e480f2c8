<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MusicDou Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .upload-section h3 {
            margin-top: 0;
            color: #333;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>MusicDou File Upload Test</h1>
    
    <div class="upload-section">
        <h3>Music Upload Test</h3>
        <input type="file" id="musicFile" accept=".mp3,.flac,.wav,.aac">
        <button onclick="uploadMusic()">Upload Music</button>
        <div id="musicResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="upload-section">
        <h3>Cover Image Upload Test</h3>
        <input type="file" id="coverFile" accept=".jpg,.jpeg,.png,.webp">
        <button onclick="uploadCover()">Upload Cover</button>
        <div id="coverResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="upload-section">
        <h3>Avatar Upload Test</h3>
        <input type="file" id="avatarFile" accept=".jpg,.jpeg,.png,.webp">
        <button onclick="uploadAvatar()">Upload Avatar</button>
        <div id="avatarResult" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api/v1';

        async function uploadFile(endpoint, fileInputId, resultId) {
            const fileInput = document.getElementById(fileInputId);
            const resultDiv = document.getElementById(resultId);
            
            if (!fileInput.files[0]) {
                showResult(resultDiv, 'Please select a file first', 'error');
                return;
            }
            
            const formData = new FormData();
            const fieldName = endpoint.split('/').pop(); // music, cover, or avatar
            formData.append(fieldName, fileInput.files[0]);
            
            try {
                showResult(resultDiv, 'Uploading...', 'success');
                
                const response = await fetch(`${API_BASE}/upload/${endpoint}`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    let message = `✅ ${result.message}<br>`;
                    message += `📁 Bucket: ${result.data.bucket}<br>`;
                    message += `📄 File: ${result.data.objectName}<br>`;
                    message += `📏 Size: ${(result.data.size / 1024 / 1024).toFixed(2)} MB<br>`;
                    
                    if (result.data.accessUrl) {
                        message += `🔗 <a href="${result.data.accessUrl}" target="_blank">Access URL</a><br>`;
                    }
                    if (result.data.imageUrl) {
                        message += `🖼️ <a href="${result.data.imageUrl}" target="_blank">Image URL</a><br>`;
                        message += `<img src="${result.data.imageUrl}" style="max-width: 200px; margin-top: 10px;">`;
                    }
                    if (result.data.avatarUrl) {
                        message += `👤 <a href="${result.data.avatarUrl}" target="_blank">Avatar URL</a><br>`;
                        message += `<img src="${result.data.avatarUrl}" style="max-width: 100px; border-radius: 50%; margin-top: 10px;">`;
                    }
                    
                    showResult(resultDiv, message, 'success');
                } else {
                    showResult(resultDiv, `❌ ${result.error}: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ Upload failed: ${error.message}`, 'error');
            }
        }
        
        function showResult(element, message, type) {
            element.innerHTML = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }
        
        function uploadMusic() {
            uploadFile('music', 'musicFile', 'musicResult');
        }
        
        function uploadCover() {
            uploadFile('cover', 'coverFile', 'coverResult');
        }
        
        function uploadAvatar() {
            uploadFile('avatar', 'avatarFile', 'avatarResult');
        }
        
        // Load upload info on page load
        window.onload = async function() {
            try {
                const response = await fetch(`${API_BASE}/upload/info`);
                const info = await response.json();
                console.log('Upload configuration:', info);
            } catch (error) {
                console.error('Failed to load upload info:', error);
            }
        };
    </script>
</body>
</html>
