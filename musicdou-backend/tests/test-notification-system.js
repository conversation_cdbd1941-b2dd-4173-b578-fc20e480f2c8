const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000/api/v1';
let authToken = '';
let testUserId = '';
let testUser2Id = '';

// 测试用户数据
const testUsers = [
  {
    username: 'notif_test_user1_' + Date.now(),
    email: 'notif_test1_' + Date.now() + '@example.com',
    password: 'password123'
  },
  {
    username: 'notif_test_user2_' + Date.now(),
    email: 'notif_test2_' + Date.now() + '@example.com',
    password: 'password123'
  }
];

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    if (authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      console.error(`API Error: ${error.response.status} - ${error.response.data.message || error.response.statusText}`);
    } else if (error.request) {
      console.error('Network Error: No response received');
    } else {
      console.error('Error:', error.message);
    }
    return Promise.reject(error);
  }
);

// 工具函数
function log(message, data = null) {
  console.log(`\n📢 ${message}`);
  if (data) {
    console.log(JSON.stringify(data, null, 2));
  }
}

function success(message) {
  console.log(`✅ ${message}`);
}

function error(message, err = null) {
  console.log(`❌ ${message}`);
  if (err) {
    console.error(err.response?.data || err.message);
  }
}

// 测试函数
async function setupTestUsers() {
  log('设置测试用户...');
  
  try {
    // 注册第一个测试用户
    const user1Response = await api.post('/auth/register', testUsers[0]);
    success('第一个测试用户注册成功');
    
    // 登录第一个用户
    const login1Response = await api.post('/auth/login', {
      identifier: testUsers[0].email,
      password: testUsers[0].password
    });
    
    authToken = login1Response.data.data.token;
    testUserId = login1Response.data.data.user._id;
    success(`第一个用户登录成功，ID: ${testUserId}`);
    
    // 注册第二个测试用户
    const user2Response = await api.post('/auth/register', testUsers[1]);
    success('第二个测试用户注册成功');
    
    // 获取第二个用户ID（通过登录）
    const login2Response = await api.post('/auth/login', {
      identifier: testUsers[1].email,
      password: testUsers[1].password
    });
    testUser2Id = login2Response.data.data.user._id;
    success(`第二个用户ID: ${testUser2Id}`);
    
    // 切换回第一个用户的token
    authToken = login1Response.data.data.token;
    
  } catch (err) {
    if ((err.response?.status === 400 || err.response?.status === 409) &&
        (err.response?.data?.message?.includes('already exists') || err.response?.data?.error?.includes('already exists'))) {
      // 用户已存在，尝试登录
      try {
        const loginResponse = await api.post('/auth/login', {
          identifier: testUsers[0].email,
          password: testUsers[0].password
        });
        authToken = loginResponse.data.data.token;
        testUserId = loginResponse.data.data.user._id;
        success('使用现有测试用户');
        
        // 获取第二个用户ID
        const login2Response = await api.post('/auth/login', {
          identifier: testUsers[1].email,
          password: testUsers[1].password
        });
        testUser2Id = login2Response.data.data.user._id;
        
        // 切换回第一个用户
        authToken = loginResponse.data.data.token;
      } catch (loginErr) {
        error('登录现有用户失败', loginErr);
        throw loginErr;
      }
    } else {
      error('设置测试用户失败', err);
      throw err;
    }
  }
}

async function testGetNotifications() {
  log('测试获取通知列表...');
  
  try {
    const response = await api.get('/notifications');
    success(`获取通知列表成功，共 ${response.data.data.notifications.length} 条通知`);
    log('通知列表', response.data.data);
    return response.data.data.notifications;
  } catch (err) {
    error('获取通知列表失败', err);
    throw err;
  }
}

async function testGetUnreadCount() {
  log('测试获取未读通知数量...');
  
  try {
    const response = await api.get('/notifications/unread/count');
    success(`未读通知数量: ${response.data.data.count}`);
    return response.data.data.count;
  } catch (err) {
    error('获取未读通知数量失败', err);
    throw err;
  }
}

async function testGetNotificationSettings() {
  log('测试获取通知设置...');
  
  try {
    const response = await api.get('/notifications/settings');
    success('获取通知设置成功');
    log('通知设置', response.data.data);
    return response.data.data;
  } catch (err) {
    error('获取通知设置失败', err);
    throw err;
  }
}

async function testUpdateNotificationSettings() {
  log('测试更新通知设置...');
  
  try {
    const newSettings = {
      follow: {
        inApp: true,
        email: false,
        push: true
      },
      like: {
        inApp: true,
        email: true,
        push: true
      },
      comment: {
        inApp: true,
        email: true,
        push: true
      }
    };
    
    const response = await api.put('/notifications/settings', { settings: newSettings });
    success('更新通知设置成功');
    log('更新后的设置', response.data.data);
    return response.data.data;
  } catch (err) {
    error('更新通知设置失败', err);
    throw err;
  }
}

async function testCreateTestNotification() {
  log('测试创建测试通知...');
  
  try {
    const response = await api.post('/notifications/test', {
      type: 'system',
      title: '测试系统通知',
      content: '这是一个测试系统通知，用于验证通知系统功能'
    });
    success('创建测试通知成功');
    log('测试通知', response.data.data);
    return response.data.data;
  } catch (err) {
    error('创建测试通知失败', err);
    throw err;
  }
}

async function testCreateAchievementNotification() {
  log('测试创建成就通知...');
  
  try {
    const response = await api.post('/notifications/test', {
      type: 'achievement',
      title: '测试成就通知',
      content: '恭喜获得测试成就'
    });
    success('创建成就通知成功');
    log('成就通知', response.data.data);
    return response.data.data;
  } catch (err) {
    error('创建成就通知失败', err);
    throw err;
  }
}

async function testMarkNotificationAsRead(notificationId) {
  log(`测试标记通知为已读: ${notificationId}`);
  
  try {
    const response = await api.put(`/notifications/${notificationId}/read`);
    success('标记通知为已读成功');
    return response.data.data;
  } catch (err) {
    error('标记通知为已读失败', err);
    throw err;
  }
}

async function testGetNotificationStats() {
  log('测试获取通知统计...');
  
  try {
    const response = await api.get('/notifications/stats');
    success('获取通知统计成功');
    log('通知统计', response.data.data);
    return response.data.data;
  } catch (err) {
    error('获取通知统计失败', err);
    throw err;
  }
}

async function testGetNotificationTypes() {
  log('测试获取通知类型列表...');
  
  try {
    const response = await api.get('/notifications/types');
    success(`获取通知类型成功，共 ${response.data.data.length} 种类型`);
    log('通知类型', response.data.data);
    return response.data.data;
  } catch (err) {
    error('获取通知类型失败', err);
    throw err;
  }
}

async function testMarkAllAsRead() {
  log('测试标记所有通知为已读...');
  
  try {
    const response = await api.put('/notifications/all/read');
    success(`标记所有通知为已读成功，共 ${response.data.data.modifiedCount} 条`);
    return response.data.data;
  } catch (err) {
    error('标记所有通知为已读失败', err);
    throw err;
  }
}

async function cleanupTestData() {
  log('清理测试数据...');
  
  try {
    // 这里可以添加清理逻辑，比如删除测试通知
    success('测试数据清理完成');
  } catch (err) {
    error('清理测试数据失败', err);
  }
}

// 主测试函数
async function runNotificationSystemTests() {
  console.log('🚀 开始通知系统测试...\n');
  
  try {
    // 1. 设置测试用户
    await setupTestUsers();
    
    // 2. 测试获取通知类型
    await testGetNotificationTypes();
    
    // 3. 测试获取通知设置
    await testGetNotificationSettings();
    
    // 4. 测试更新通知设置
    await testUpdateNotificationSettings();
    
    // 5. 测试获取通知列表（初始状态）
    await testGetNotifications();
    
    // 6. 测试获取未读通知数量
    await testGetUnreadCount();
    
    // 7. 测试创建测试通知
    const testNotification = await testCreateTestNotification();
    
    // 8. 测试创建成就通知
    const achievementNotification = await testCreateAchievementNotification();
    
    // 9. 再次获取通知列表（应该有新通知）
    const notifications = await testGetNotifications();
    
    // 10. 测试标记通知为已读
    if (notifications.length > 0) {
      await testMarkNotificationAsRead(notifications[0]._id);
    }
    
    // 11. 测试获取通知统计
    await testGetNotificationStats();
    
    // 12. 测试标记所有通知为已读
    await testMarkAllAsRead();
    
    // 13. 清理测试数据
    await cleanupTestData();
    
    console.log('\n🎉 通知系统测试完成！所有测试用例都通过了。');
    
  } catch (err) {
    console.log('\n💥 通知系统测试失败！');
    console.error(err.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runNotificationSystemTests();
}

module.exports = {
  runNotificationSystemTests
};
