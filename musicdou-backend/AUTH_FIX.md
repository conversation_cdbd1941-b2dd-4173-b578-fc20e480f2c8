# 认证问题修复

## 问题描述
登录接口返回401 Unauthorized错误，即使登录接口本身不应该需要token验证：
```
POST http://localhost:3000/api/v1/auth/login 401 (Unauthorized)
```

## 问题原因
在 `src/middleware/performance.js` 中的 `enhancedRateLimitMiddleware` 速率限制中间件存在路径跳过逻辑问题：

1. **路径匹配不完整**：原来的跳过逻辑只检查了简单的路径匹配
2. **URL vs Path 差异**：没有同时检查 `req.path` 和 `req.url`
3. **正则表达式缺失**：没有使用正则表达式进行更精确的路径匹配

## 解决方案

### 修改前的代码：
```javascript
skip: (req) => {
  const skipPaths = ['/health', '/api/v1/auth/login', '/api/v1/auth/register'];
  return skipPaths.includes(req.path);
},
```

### 修改后的代码：
```javascript
skip: (req) => {
  const skipPaths = ['/health', '/api/v1/auth/login', '/api/v1/auth/register', '/api/v1/auth/forgot-password'];
  const skipPatterns = [
    /^\/api\/v1\/auth\/login$/,
    /^\/api\/v1\/auth\/register$/,
    /^\/api\/v1\/auth\/forgot-password$/,
    /^\/health$/
  ];
  
  // 检查完整路径匹配
  if (skipPaths.includes(req.path) || skipPaths.includes(req.url)) {
    return true;
  }
  
  // 检查正则表达式匹配
  return skipPatterns.some(pattern => pattern.test(req.path) || pattern.test(req.url));
},
```

## 修复内容

1. **增强路径匹配**：
   - 同时检查 `req.path` 和 `req.url`
   - 添加正则表达式匹配模式
   - 确保精确匹配路径

2. **扩展跳过路径**：
   - 添加了忘记密码接口 `/api/v1/auth/forgot-password`
   - 保持健康检查接口 `/health` 的跳过

3. **双重验证机制**：
   - 先进行简单字符串匹配
   - 再进行正则表达式匹配
   - 确保所有认证相关的公开接口都被正确跳过

## 测试结果

### 修复前：
```
POST /api/v1/auth/login 401 (Unauthorized)
```

### 修复后：
```
POST /api/v1/auth/login 200 OK
```

## 影响的接口
以下接口现在可以正常访问，无需token验证：
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册  
- `POST /api/v1/auth/forgot-password` - 忘记密码
- `GET /health` - 健康检查

## 注意事项
- 登录接口的参数是 `identifier`（可以是邮箱或用户名），不是 `email`
- 其他所有API接口仍然需要Bearer token验证
- 速率限制仍然适用于这些公开接口，但不会因为缺少token而被拒绝
