#!/bin/bash

# Docker 环境测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查Docker是否安装
check_docker_installed() {
    print_message "检查 Docker 是否安装..."
    if command -v docker &> /dev/null; then
        print_success "Docker 已安装"
        docker --version
    else
        print_error "Docker 未安装，请先安装 Docker Desktop"
        exit 1
    fi
}

# 检查Docker是否运行
check_docker_running() {
    print_message "检查 Docker 是否运行..."
    if docker info &> /dev/null; then
        print_success "Docker 正在运行"
    else
        print_error "Docker 未运行，请启动 Docker Desktop"
        exit 1
    fi
}

# 检查docker-compose是否可用
check_docker_compose() {
    print_message "检查 docker-compose 是否可用..."
    if docker compose version &> /dev/null; then
        print_success "docker-compose 可用"
        docker compose version
    else
        print_error "docker-compose 不可用"
        exit 1
    fi
}

# 测试Docker配置文件
test_docker_config() {
    print_message "测试 docker-compose.yml 配置..."
    if [ -f "docker-compose.yml" ]; then
        print_success "docker-compose.yml 文件存在"
        
        # 验证配置文件语法
        if docker compose config &> /dev/null; then
            print_success "docker-compose.yml 配置语法正确"
        else
            print_error "docker-compose.yml 配置语法错误"
            docker compose config
            exit 1
        fi
    else
        print_error "docker-compose.yml 文件不存在"
        exit 1
    fi
}

# 测试网络连接
test_network_connectivity() {
    print_message "测试网络连接..."
    
    # 测试端口是否被占用
    ports=(27017 6379 9000 9001)
    for port in "${ports[@]}"; do
        if lsof -i :$port &> /dev/null; then
            print_warning "端口 $port 已被占用"
        else
            print_success "端口 $port 可用"
        fi
    done
}

# 测试环境变量
test_env_config() {
    print_message "检查环境变量配置..."
    if [ -f ".env" ]; then
        print_success ".env 文件存在"
        
        # 检查关键环境变量
        source .env
        
        if [ -n "$MONGODB_URI" ]; then
            print_success "MONGODB_URI 已配置"
        else
            print_warning "MONGODB_URI 未配置"
        fi
        
        if [ -n "$REDIS_PASSWORD" ]; then
            print_success "REDIS_PASSWORD 已配置"
        else
            print_warning "REDIS_PASSWORD 未配置"
        fi
        
        if [ -n "$MINIO_SECRET_KEY" ]; then
            print_success "MINIO_SECRET_KEY 已配置"
        else
            print_warning "MINIO_SECRET_KEY 未配置"
        fi
    else
        print_error ".env 文件不存在"
        exit 1
    fi
}

# 运行所有测试
run_all_tests() {
    print_message "开始 Docker 环境测试..."
    echo ""
    
    check_docker_installed
    echo ""
    
    check_docker_running
    echo ""
    
    check_docker_compose
    echo ""
    
    test_docker_config
    echo ""
    
    test_network_connectivity
    echo ""
    
    test_env_config
    echo ""
    
    print_success "所有测试完成！Docker 环境配置正确。"
    print_message "现在可以运行 'npm run docker:start' 启动服务"
}

# 主函数
main() {
    case "${1:-all}" in
        docker)
            check_docker_installed
            check_docker_running
            ;;
        compose)
            check_docker_compose
            test_docker_config
            ;;
        network)
            test_network_connectivity
            ;;
        env)
            test_env_config
            ;;
        all)
            run_all_tests
            ;;
        *)
            echo "用法: $0 [docker|compose|network|env|all]"
            echo ""
            echo "  docker   - 检查 Docker 安装和运行状态"
            echo "  compose  - 检查 docker-compose 配置"
            echo "  network  - 检查网络端口"
            echo "  env      - 检查环境变量"
            echo "  all      - 运行所有测试 (默认)"
            ;;
    esac
}

main "$@"
