version: '3.8'

services:
  # MongoDB 数据库
  mongodb:
    image: mongo:7.0
    container_name: musicdou-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: musicdou123
      MONGO_INITDB_DATABASE: musicdou
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongodb/init:/docker-entrypoint-initdb.d
    networks:
      - musicdou-network

  # Redis 缓存
  redis:
    image: redis:7.2-alpine
    container_name: musicdou-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --requirepass musicdou123
    volumes:
      - redis_data:/data
    networks:
      - musicdou-network

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: musicdou-minio
    restart: unless-stopped
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: musicdou123
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - musicdou-network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local

networks:
  musicdou-network:
    driver: bridge
